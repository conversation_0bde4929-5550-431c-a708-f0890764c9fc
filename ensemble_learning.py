"""
集成学习模块
实现多模型集成预测，结合不同模型的优势
"""

import torch
import torch.nn as nn
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from collections import deque
import warnings

warnings.filterwarnings("ignore")


class EnsemblePredictor:
    """集成预测器"""
    
    def __init__(self, models, scalers, weights=None, combination_method='weighted_average'):
        """
        初始化集成预测器
        
        Args:
            models: 模型列表
            scalers: 标准化器列表
            weights: 模型权重（如果为None则使用等权重）
            combination_method: 组合方法 ('weighted_average', 'median', 'adaptive', 'stacking')
        """
        self.models = models
        self.scalers = scalers
        self.combination_method = combination_method
        
        if weights is None:
            self.weights = np.ones(len(models)) / len(models)
        else:
            self.weights = np.array(weights)
            self.weights = self.weights / np.sum(self.weights)  # 归一化
        
        # 自适应权重相关
        self.adaptive_weights = self.weights.copy()
        self.model_errors = [deque(maxlen=50) for _ in range(len(models))]
        self.weight_update_rate = 0.1
        
        # 堆叠模型（用于stacking方法）
        self.meta_model = None
        
    def predict_single(self, input_sequence, actual_value=None):
        """
        单点集成预测
        
        Args:
            input_sequence: 输入序列
            actual_value: 真实值（用于自适应权重更新）
        
        Returns:
            float: 集成预测结果
        """
        # 获取各个模型的预测
        predictions = []
        for i, (model, scaler) in enumerate(zip(self.models, self.scalers)):
            try:
                pred = self._single_model_predict(model, scaler, input_sequence)
                predictions.append(pred)
                
                # 更新模型误差历史（用于自适应权重）
                if actual_value is not None:
                    error = abs(pred - actual_value)
                    self.model_errors[i].append(error)
                    
            except Exception as e:
                print(f"模型 {i} 预测失败: {e}")
                predictions.append(0.0)  # 使用默认值
        
        # 更新自适应权重
        if actual_value is not None and self.combination_method == 'adaptive':
            self._update_adaptive_weights()
        
        # 组合预测结果
        ensemble_prediction = self._combine_predictions(predictions)
        
        return ensemble_prediction
    
    def _single_model_predict(self, model, scaler, input_sequence):
        """单个模型预测"""
        # 标准化输入
        normalized_seq = scaler.transform(input_sequence.reshape(-1, 1)).flatten()
        input_tensor = torch.FloatTensor(normalized_seq).unsqueeze(0).unsqueeze(-1)
        
        # 模型预测
        model.eval()
        with torch.no_grad():
            normalized_pred = model(input_tensor).item()
        
        # 反标准化
        prediction = scaler.inverse_transform([[normalized_pred]])[0, 0]
        
        return prediction
    
    def _combine_predictions(self, predictions):
        """组合预测结果"""
        predictions = np.array(predictions)
        
        if self.combination_method == 'weighted_average':
            return np.average(predictions, weights=self.weights)
        
        elif self.combination_method == 'adaptive':
            return np.average(predictions, weights=self.adaptive_weights)
        
        elif self.combination_method == 'median':
            return np.median(predictions)
        
        elif self.combination_method == 'trimmed_mean':
            # 去除最大和最小值后取平均
            if len(predictions) > 2:
                sorted_preds = np.sort(predictions)
                trimmed = sorted_preds[1:-1]
                return np.mean(trimmed)
            else:
                return np.mean(predictions)
        
        elif self.combination_method == 'stacking' and self.meta_model is not None:
            # 使用元模型进行预测
            meta_input = torch.FloatTensor(predictions).unsqueeze(0)
            with torch.no_grad():
                return self.meta_model(meta_input).item()
        
        else:
            # 默认使用加权平均
            return np.average(predictions, weights=self.weights)
    
    def _update_adaptive_weights(self):
        """更新自适应权重"""
        # 计算各模型的平均误差
        avg_errors = []
        for errors in self.model_errors:
            if len(errors) > 0:
                avg_errors.append(np.mean(errors))
            else:
                avg_errors.append(1.0)  # 默认误差
        
        avg_errors = np.array(avg_errors)
        
        # 计算权重（误差越小权重越大）
        if np.sum(avg_errors) > 0:
            # 使用误差的倒数作为权重
            inv_errors = 1.0 / (avg_errors + 1e-8)
            new_weights = inv_errors / np.sum(inv_errors)
            
            # 平滑更新权重
            self.adaptive_weights = (1 - self.weight_update_rate) * self.adaptive_weights + \
                                  self.weight_update_rate * new_weights
    
    def train_meta_model(self, X, y, meta_model_type='linear'):
        """
        训练元模型（用于stacking）
        
        Args:
            X: 训练数据
            y: 目标数据
            meta_model_type: 元模型类型 ('linear', 'mlp')
        """
        print("训练元模型...")
        
        # 生成基模型预测
        base_predictions = []
        for i in range(len(X)):
            input_seq = X[i]
            preds = []
            for model, scaler in zip(self.models, self.scalers):
                pred = self._single_model_predict(model, scaler, input_seq)
                preds.append(pred)
            base_predictions.append(preds)
        
        base_predictions = np.array(base_predictions)
        
        # 创建元模型
        if meta_model_type == 'linear':
            self.meta_model = nn.Linear(len(self.models), 1)
        elif meta_model_type == 'mlp':
            self.meta_model = nn.Sequential(
                nn.Linear(len(self.models), 16),
                nn.ReLU(),
                nn.Linear(16, 8),
                nn.ReLU(),
                nn.Linear(8, 1)
            )
        
        # 训练元模型
        criterion = nn.MSELoss()
        optimizer = torch.optim.Adam(self.meta_model.parameters(), lr=0.01)
        
        X_meta = torch.FloatTensor(base_predictions)
        y_meta = torch.FloatTensor(y)
        
        for epoch in range(100):
            optimizer.zero_grad()
            outputs = self.meta_model(X_meta).squeeze()
            loss = criterion(outputs, y_meta)
            loss.backward()
            optimizer.step()
            
            if epoch % 20 == 0:
                print(f"元模型训练 Epoch {epoch}: Loss = {loss.item():.6f}")
        
        print("元模型训练完成")
    
    def get_model_weights(self):
        """获取当前模型权重"""
        if self.combination_method == 'adaptive':
            return self.adaptive_weights.copy()
        else:
            return self.weights.copy()
    
    def get_model_performance(self):
        """获取各模型性能统计"""
        performance = {}
        for i, errors in enumerate(self.model_errors):
            if len(errors) > 0:
                performance[f'model_{i}'] = {
                    'avg_error': np.mean(errors),
                    'std_error': np.std(errors),
                    'min_error': np.min(errors),
                    'max_error': np.max(errors),
                    'sample_count': len(errors)
                }
        return performance


class DiversityBasedEnsemble:
    """基于多样性的集成方法"""
    
    def __init__(self, base_model_class, num_models=5, diversity_method='bootstrap'):
        """
        初始化多样性集成
        
        Args:
            base_model_class: 基础模型类
            num_models: 模型数量
            diversity_method: 多样性方法 ('bootstrap', 'feature_subset', 'parameter_variation')
        """
        self.base_model_class = base_model_class
        self.num_models = num_models
        self.diversity_method = diversity_method
        self.models = []
        self.scalers = []
    
    def train_diverse_models(self, X, y, window_size=25):
        """训练多样化的模型"""
        print(f"训练 {self.num_models} 个多样化模型...")
        
        for i in range(self.num_models):
            print(f"训练模型 {i+1}/{self.num_models}")
            
            # 生成多样化的训练数据
            if self.diversity_method == 'bootstrap':
                X_diverse, y_diverse = self._bootstrap_sample(X, y)
            elif self.diversity_method == 'feature_subset':
                X_diverse, y_diverse = self._feature_subset_sample(X, y)
            else:  # parameter_variation
                X_diverse, y_diverse = X, y
            
            # 创建模型（参数变化）
            if self.diversity_method == 'parameter_variation':
                model = self._create_varied_model(i)
            else:
                model = self.base_model_class()
            
            # 创建标准化器
            scaler = MinMaxScaler(feature_range=(-1, 1))
            
            # 训练模型
            self._train_single_model(model, scaler, X_diverse, y_diverse)
            
            self.models.append(model)
            self.scalers.append(scaler)
        
        print("多样化模型训练完成")
    
    def _bootstrap_sample(self, X, y, sample_ratio=0.8):
        """Bootstrap采样"""
        n_samples = int(len(X) * sample_ratio)
        indices = np.random.choice(len(X), n_samples, replace=True)
        return X[indices], y[indices]
    
    def _feature_subset_sample(self, X, y, feature_ratio=0.9):
        """特征子集采样"""
        # 对于时间序列，随机选择时间步
        seq_len = X.shape[1]
        n_features = int(seq_len * feature_ratio)
        feature_indices = np.random.choice(seq_len, n_features, replace=False)
        feature_indices = np.sort(feature_indices)
        
        return X[:, feature_indices, :], y
    
    def _create_varied_model(self, model_index):
        """创建参数变化的模型"""
        # 随机变化模型参数
        hidden_sizes = [64, 96, 128, 160, 192]
        num_layers_options = [1, 2, 3]
        dropout_options = [0.1, 0.15, 0.2, 0.25, 0.3]
        
        hidden_size = hidden_sizes[model_index % len(hidden_sizes)]
        num_layers = num_layers_options[model_index % len(num_layers_options)]
        dropout = dropout_options[model_index % len(dropout_options)]
        
        # 假设基础模型类支持这些参数
        try:
            model = self.base_model_class(
                hidden_size=hidden_size,
                num_layers=num_layers,
                dropout=dropout
            )
        except:
            # 如果不支持，使用默认参数
            model = self.base_model_class()
        
        return model
    
    def _train_single_model(self, model, scaler, X, y, epochs=50):
        """训练单个模型"""
        # 数据预处理
        flat_data = X.reshape(-1, X.shape[-1])
        scaler.fit(flat_data)
        
        # 创建训练序列
        sequences, targets = self._create_sequences(X, y, scaler)
        
        # 训练
        criterion = nn.MSELoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
        
        model.train()
        for epoch in range(epochs):
            optimizer.zero_grad()
            outputs = model(sequences).squeeze()
            loss = criterion(outputs, targets)
            loss.backward()
            optimizer.step()
    
    def _create_sequences(self, X, y, scaler):
        """创建训练序列"""
        # 标准化
        normalized_X = []
        for seq in X:
            norm_seq = scaler.transform(seq.reshape(-1, 1)).flatten()
            normalized_X.append(norm_seq)
        
        normalized_y = scaler.transform(y.reshape(-1, 1)).flatten()
        
        X_tensor = torch.FloatTensor(normalized_X).unsqueeze(-1)
        y_tensor = torch.FloatTensor(normalized_y)
        
        return X_tensor, y_tensor
    
    def create_ensemble_predictor(self, combination_method='adaptive'):
        """创建集成预测器"""
        return EnsemblePredictor(
            models=self.models,
            scalers=self.scalers,
            combination_method=combination_method
        )


def evaluate_ensemble_performance(ensemble_predictor, test_X, test_y, window_size=25):
    """
    评估集成模型性能
    
    Args:
        ensemble_predictor: 集成预测器
        test_X: 测试数据
        test_y: 测试目标
        window_size: 窗口大小
    
    Returns:
        dict: 性能指标
    """
    print("评估集成模型性能...")
    
    predictions = []
    individual_predictions = {f'model_{i}': [] for i in range(len(ensemble_predictor.models))}
    
    for i in range(len(test_X)):
        input_seq = test_X[i]
        actual_val = test_y[i]
        
        # 集成预测
        ensemble_pred = ensemble_predictor.predict_single(input_seq, actual_val)
        predictions.append(ensemble_pred)
        
        # 个体模型预测
        for j, (model, scaler) in enumerate(zip(ensemble_predictor.models, ensemble_predictor.scalers)):
            try:
                individual_pred = ensemble_predictor._single_model_predict(model, scaler, input_seq)
                individual_predictions[f'model_{j}'].append(individual_pred)
            except:
                individual_predictions[f'model_{j}'].append(0.0)
    
    # 计算性能指标
    predictions = np.array(predictions)
    test_y = np.array(test_y)
    
    ensemble_mse = np.mean((predictions - test_y) ** 2)
    ensemble_mae = np.mean(np.abs(predictions - test_y))
    ensemble_rmse = np.sqrt(ensemble_mse)
    
    # 计算个体模型性能
    individual_performance = {}
    for model_name, preds in individual_predictions.items():
        if len(preds) > 0:
            preds = np.array(preds)
            individual_performance[model_name] = {
                'mse': np.mean((preds - test_y) ** 2),
                'mae': np.mean(np.abs(preds - test_y)),
                'rmse': np.sqrt(np.mean((preds - test_y) ** 2))
            }
    
    # 计算改进程度
    avg_individual_mse = np.mean([perf['mse'] for perf in individual_performance.values()])
    improvement = (avg_individual_mse - ensemble_mse) / avg_individual_mse * 100
    
    results = {
        'ensemble_performance': {
            'mse': ensemble_mse,
            'mae': ensemble_mae,
            'rmse': ensemble_rmse
        },
        'individual_performance': individual_performance,
        'improvement_percentage': improvement,
        'model_weights': ensemble_predictor.get_model_weights(),
        'model_performance_stats': ensemble_predictor.get_model_performance()
    }
    
    print(f"集成模型 MSE: {ensemble_mse:.8f}")
    print(f"集成模型 MAE: {ensemble_mae:.6f}")
    print(f"集成模型 RMSE: {ensemble_rmse:.6f}")
    print(f"相对个体模型改进: {improvement:.2f}%")
    
    return results


if __name__ == "__main__":
    # 测试集成学习
    print("集成学习模块测试")
    
    # 这里需要导入实际的模型类进行测试
    # 由于依赖关系，这里只做基本的结构测试
    print("集成学习模块结构测试完成")
