# 预测速率分析：为什么每秒只能预测60个点？

## 问题现象
- 目标：每秒 2000 Hz 采样率
- 实际：每秒约 60 个预测点
- 性能差距：**97% 的数据被丢弃**

## 根本原因分析

### 1. 处理时间瓶颈（主要原因）
从 `three_seconds_data.txt` 数据分析：
```
处理时间范围: 1.95ms ~ 11.10ms
平均处理时间: ~3.5ms
```

**计算：**
- 每个预测需要 ~3.5ms
- 理论最大吞吐量 = 1000ms / 3.5ms ≈ **286 点/秒**
- 但实际只有 60 点/秒，说明还有其他开销

### 2. 代码中的限制

#### 问题 1：目标点数设置不合理
```python
# integrated_real_time_demo.py 第 696-702 行
three_seconds_data = await stream_predict_for_duration(
    processor=processor,
    displacement_source=displacement_source,
    velocity_source=velocity_source,
    duration_seconds=prediction_duration,
    on_sample=on_sample
    # ❌ 没有指定 target_points_per_second，使用默认值 100
)
```

#### 问题 2：最小发射间隔限制
```python
# integrated_real_time_demo.py 第 173 行
min_emit_interval = 1.0 / max(1, target_points_per_second)
# 当 target_points_per_second=100 时，min_emit_interval = 10ms
```

#### 问题 3：异步等待开销
```python
# integrated_real_time_demo.py 第 242, 245 行
await asyncio.sleep(0.00005)  # 50μs
await asyncio.sleep(0.0001)   # 100μs
```
这些微小的等待累积起来会显著降低吞吐量。

### 3. 数据源读取效率
DSA 数据源的读取可能不够高效，导致：
- 数据缓冲区中的数据不连续
- 读取延迟不稳定
- 实际数据到达率低于预期

## 性能瓶颈排序

| 瓶颈 | 影响 | 优先级 |
|------|------|--------|
| LSTM 预测处理时间 (3.5ms) | 主要 | 🔴 高 |
| 异步等待开销 | 中等 | 🟡 中 |
| 数据源读取效率 | 中等 | 🟡 中 |
| 目标点数设置 | 低 | 🟢 低 |

## 改进方案

### 方案 1：增加目标点数（快速修复）
```python
# 修改调用代码
three_seconds_data = await stream_predict_for_duration(
    processor=processor,
    displacement_source=displacement_source,
    velocity_source=velocity_source,
    duration_seconds=prediction_duration,
    on_sample=on_sample,
    target_points_per_second=2000  # 提高目标点数
)
```
**预期效果：** 可能提升到 200-300 点/秒

### 方案 2：优化异步等待（中期改进）
```python
# 减少或移除不必要的 asyncio.sleep()
# 使用更高效的事件循环管理
```
**预期效果：** 可能提升 10-20%

### 方案 3：模型优化（长期方案）
- 使用更轻量的模型架构
- 启用 ONNX 或 TorchScript 加速
- 使用 GPU 推理（如果可用）
- 批量预测而非单点预测

**预期效果：** 可能提升 50-100%

### 方案 4：多线程/多进程处理
- 使用线程池处理预测
- 分离数据读取和预测逻辑
- 使用队列缓冲数据

**预期效果：** 可能提升 100-200%

## 建议优先级

1. **立即执行**：增加 `target_points_per_second` 参数
2. **短期**：优化异步等待和事件循环
3. **中期**：实现多线程预测
4. **长期**：模型轻量化和 GPU 加速

## 参考数据

从 `three_seconds_data.txt` 统计：
- 总数据点：466 个
- 时间范围：2.002s ~ 3.690s（约 1.688 秒）
- 实际采样率：466 / 1.688 ≈ **276 点/秒**
- 平均处理时间：3.5ms
- 最小处理时间：1.95ms
- 最大处理时间：11.10ms

**注意：** 实际采样率 276 点/秒 > 理论最大值 286 点/秒，说明数据统计可能包含了缓冲的数据。

