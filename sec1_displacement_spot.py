import matplotlib.pyplot as plt
import numpy as np
import os

def read_displacement_data(filename):
    """读取位移数据文件"""
    if not os.path.exists(filename):
        print(f"错误：找不到文件 {filename}")
        return None, None
    
    timestamps = []
    displacements = []
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 跳过注释行，读取数据
        for line in lines:
            line = line.strip()
            if line.startswith('#') or not line:
                continue
            
            # 解析数据行：时间戳 位移值
            parts = line.split()
            if len(parts) >= 2:
                try:
                    timestamp = float(parts[0])
                    displacement = float(parts[1])
                    timestamps.append(timestamp)
                    displacements.append(displacement)
                except ValueError:
                    continue
        
        print(f"成功读取 {len(displacements)} 个数据点")
        return timestamps, displacements
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None, None

def plot_displacement_data(timestamps, displacements, filename):
    """绘制位移数据图表"""
    if not timestamps or not displacements:
        print("没有数据可以绘制")
        return
    
    # 创建点数序列（从1开始）
    point_numbers = list(range(1, len(displacements) + 1))
    
    # 计算统计信息
    mean_displacement = np.mean(displacements)
    max_displacement = np.max(displacements)
    min_displacement = np.min(displacements)
    std_displacement = np.std(displacements)
    range_displacement = max_displacement - min_displacement
    
    # 创建图表
    plt.figure(figsize=(12, 8))
    
    # 绘制位移数据点
    plt.plot(point_numbers, displacements, 'b-', linewidth=0.8, alpha=0.7, label='位移数据')
    plt.scatter(point_numbers[::50], displacements[::50], c='red', s=10, alpha=0.6, label='采样点 (每50个点)')
    
    # 添加统计线
    plt.axhline(y=mean_displacement, color='green', linestyle='--', alpha=0.8, label=f'平均值: {mean_displacement:.3f} μm')
    plt.axhline(y=max_displacement, color='orange', linestyle=':', alpha=0.6, label=f'最大值: {max_displacement:.3f} μm')
    plt.axhline(y=min_displacement, color='orange', linestyle=':', alpha=0.6, label=f'最小值: {min_displacement:.3f} μm')
    
    # 设置图表属性
    plt.xlabel('数据点序号', fontsize=12)
    plt.ylabel('位移 (μm)', fontsize=12)
    plt.title(f'第1秒位移数据可视化\n文件: {filename}', fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3)
    plt.legend(loc='upper right')
    
    # 添加统计信息文本框
    stats_text = f'''统计信息:
数据点数: {len(displacements)}
平均值: {mean_displacement:.6f} μm
标准差: {std_displacement:.6f} μm
变化范围: {range_displacement:.6f} μm
最大值: {max_displacement:.6f} μm
最小值: {min_displacement:.6f} μm'''
    
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
             fontsize=10, fontfamily='monospace')
    
    # 调整布局
    plt.tight_layout()
    
    # 显示图表
    plt.show()
    
    # 保存图表
    output_filename = filename.replace('.txt', '_plot.png')
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    print(f"图表已保存为: {output_filename}")

def analyze_data_variation(displacements):
    """分析数据变化特征"""
    print("\n=== 数据变化分析 ===")
    
    # 计算相邻点的差值
    differences = np.diff(displacements)
    
    # 统计相同值的数量
    unique_values = len(set(displacements))
    total_points = len(displacements)
    
    print(f"唯一数值数量: {unique_values}")
    print(f"总数据点数: {total_points}")
    print(f"数据重复率: {(total_points - unique_values) / total_points * 100:.1f}%")
    
    # 分析变化幅度
    if len(differences) > 0:
        max_change = np.max(np.abs(differences))
        mean_change = np.mean(np.abs(differences))
        print(f"最大变化幅度: {max_change:.6f} μm")
        print(f"平均变化幅度: {mean_change:.6f} μm")
        
        # 统计变化方向
        increases = np.sum(differences > 0)
        decreases = np.sum(differences < 0)
        no_change = np.sum(differences == 0)
        
        print(f"上升点数: {increases}")
        print(f"下降点数: {decreases}")
        print(f"不变点数: {no_change}")
    
    # 检查连续相同值
    consecutive_same = []
    current_count = 1
    
    for i in range(1, len(displacements)):
        if abs(displacements[i] - displacements[i-1]) < 1e-9:  # 认为是相同值
            current_count += 1
        else:
            if current_count > 1:
                consecutive_same.append(current_count)
            current_count = 1
    
    if current_count > 1:
        consecutive_same.append(current_count)
    
    if consecutive_same:
        print(f"连续相同值组数: {len(consecutive_same)}")
        print(f"最长连续相同值: {max(consecutive_same)} 个点")
        print(f"平均连续长度: {np.mean(consecutive_same):.1f} 个点")

def main():
    """主函数"""
    print("位移数据可视化工具")
    print("=" * 40)
    
    # 文件名
    filename = "second_1_displacement_data.txt"
    
    # 检查文件是否存在
    if not os.path.exists(filename):
        print(f"错误：找不到文件 {filename}")
        print("请确保文件在当前目录下")
        return
    
    # 读取数据
    print(f"正在读取文件: {filename}")
    timestamps, displacements = read_displacement_data(filename)
    
    if timestamps is None or displacements is None:
        print("读取数据失败")
        return
    
    # 显示基本信息
    print(f"\n=== 文件信息 ===")
    print(f"文件名: {filename}")
    print(f"数据点数: {len(displacements)}")
    if timestamps:
        time_span = timestamps[-1] - timestamps[0]
        print(f"时间跨度: {time_span:.6f} 秒 ({time_span*1000:.1f} 毫秒)")
        print(f"起始时间戳: {timestamps[0]:.6f}")
        print(f"结束时间戳: {timestamps[-1]:.6f}")
    
    # 分析数据变化
    analyze_data_variation(displacements)
    
    # 绘制图表
    print(f"\n=== 开始绘制图表 ===")
    plot_displacement_data(timestamps, displacements, filename)
    
    print("\n可视化完成！")

if __name__ == "__main__":
    # 设置中文字体支持
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    
    main()
