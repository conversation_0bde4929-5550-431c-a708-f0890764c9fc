"""
实时数据流处理器
支持多种数据源的实时振动预测系统
"""

import asyncio
import queue
import threading
import time
import numpy as np
import pandas as pd
from collections import deque
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import warnings

warnings.filterwarnings("ignore")

class AdaptiveKalmanFilter:
    """自适应卡尔曼滤波器"""

    def __init__(self, process_variance=1e-5, measurement_variance=1e-3, adaptation_rate=0.01):
        # 状态估计
        self.x = 0.0  # 状态估计值
        self.P = 1.0  # 估计误差协方差

        # 噪声参数
        self.Q = process_variance  # 过程噪声方差
        self.R = measurement_variance  # 测量噪声方差

        # 自适应参数
        self.adaptation_rate = adaptation_rate
        self.innovation_history = []
        self.max_history = 10

        # 统计信息
        self.prediction_count = 0
        self.total_innovation = 0.0

    def predict(self, measurement, actual_value=None):
        """兼容旧接口：将 measurement 视为观测，使用内部状态作为先验。"""
        # 预测（内生先验）
        x_pred = self.x
        P_pred = self.P + self.Q
        # 更新
        innovation = measurement - x_pred
        S = P_pred + self.R
        K = P_pred / S
        self.x = x_pred + K * innovation
        self.P = (1 - K) * P_pred
        self._adapt_noise_parameters(innovation, actual_value)
        self.prediction_count += 1
        return self.x

    # 新增两步接口：先验来自外部（LSTM），观测来自真实传感器
    def time_update(self, prior_state):
        """时间更新：使用外部先验作为预测状态。"""
        # 这里将 LSTM 先验视为 x_pred，协方差按过程噪声增长
        self._x_pred = float(prior_state)
        self._P_pred = self.P + self.Q
        return self._x_pred

    def measurement_update(self, measurement):
        """观测更新：使用真实观测修正先验。"""
        if not hasattr(self, '_x_pred'):
            # 若未调用 time_update，则退化为使用当前状态为先验
            self._x_pred = self.x
            self._P_pred = self.P + self.Q
        innovation = float(measurement) - self._x_pred
        S = self._P_pred + self.R
        K = self._P_pred / S
        self.x = self._x_pred + K * innovation
        self.P = (1 - K) * self._P_pred
        self._adapt_noise_parameters(innovation, measurement)
        self.prediction_count += 1
        return self.x

    def _adapt_noise_parameters(self, innovation, actual_value=None):
        """自适应调整噪声参数"""
        # 记录新息历史
        self.innovation_history.append(abs(innovation))
        if len(self.innovation_history) > self.max_history:
            self.innovation_history.pop(0)

        # 计算新息的统计特性
        if len(self.innovation_history) >= 3:
            avg_innovation = np.mean(self.innovation_history)

            # 根据新息大小自适应调整测量噪声
            if avg_innovation > 0.01:  # 如果新息较大，增加测量噪声
                self.R = min(self.R * (1 + self.adaptation_rate), 0.1)
            else:  # 如果新息较小，减少测量噪声
                self.R = max(self.R * (1 - self.adaptation_rate), 1e-6)

            # 根据实际值调整过程噪声（如果有的话）
            if actual_value is not None:
                prediction_error = abs(self.x - actual_value)
                if prediction_error > 0.005:
                    self.Q = min(self.Q * (1 + self.adaptation_rate), 0.01)
                else:
                    self.Q = max(self.Q * (1 - self.adaptation_rate), 1e-8)

    def reset(self):
        """重置滤波器"""
        self.x = 0.0
        self.P = 1.0
        self.innovation_history = []
        self.prediction_count = 0
        self.total_innovation = 0.0

class RealTimeStreamProcessor:
    """实时数据流处理器 - 集成LSTM+AKF+偏移校正"""

    def __init__(self, model, scaler, window_size=25, buffer_size=10000, enable_akf=True, enable_bias_correction=True):
        self.model = model
        self.scaler = scaler
        self.window_size = window_size
        self.model.eval()

        # 数据缓冲区
        self.raw_buffer = queue.Queue(maxsize=buffer_size)
        self.processed_buffer = deque(maxlen=window_size)

        # 预测结果存储
        self.predictions = deque(maxlen=100000)  # LSTM原始预测
        self.akf_predictions = deque(maxlen=100000)  # AKF优化后的预测
        self.timestamps = deque(maxlen=100000)
        self.real_values = deque(maxlen=100000)

        # 最近一次预测（用于外部实时对比打印）
        self.last_lstm_prediction = None
        self.last_corrected_prediction = None
        self.last_akf_prediction = None

        # 控制标志
        self.is_running = False
        self.is_initialized = False
        self.enable_akf = enable_akf
        self.enable_bias_correction = enable_bias_correction

        # 偏移校正参数
        self.bias_correction = 0.0
        self.bias_samples = deque(maxlen=50)  # 用于统计展示
        self.bias_calculated = True  # 改为始终启用EMA校正
        self.bias_ema_alpha = 0.05   # 偏移EMA步长（越大响应越快）
        self.bias_clip = 1.0         # 偏移限幅（μm）

        # 增益自适应校正参数（解决尺度偏差）
        self.enable_gain_correction = True
        self.gain_correction = 1.0
        self.gain_ema_alpha = 0.05   # 增益EMA步长
        self.gain_clip_min = 0.7     # 增益下限，避免过度压缩
        self.gain_clip_max = 1.3     # 增益上限，避免过度放大

        # 输出平滑（与AKF互补的轻量EMA）
        self.enable_output_ema = True
        self.output_ema_alpha = 0.2
        self._last_output_ema = None
        # 预测结果硬性保护阈值（防止初期极端值爆炸，单位：μm）
        self.sanity_limit_um = 1000.0

        # AKF滤波器
        if self.enable_akf:
            self.akf = AdaptiveKalmanFilter()
            print("✅ AKF滤波器已启用")
            # 启用AKF时：关闭EMA，降低纠偏步长，避免双重平滑与过强前置纠偏
            self.enable_output_ema = False
            self.bias_ema_alpha = min(self.bias_ema_alpha, 0.02)
            self.gain_ema_alpha = min(self.gain_ema_alpha, 0.02)
        else:
            self.akf = None
            print("⚠️ AKF滤波器已禁用")

        if self.enable_bias_correction:
            print("✅ 偏移校正已启用")
        else:
            print("⚠️ 偏移校正已禁用")

        # 性能统计
        self.processing_times = deque(maxlen=10000)
        self.prediction_count = 0
        self.dropped_count = 0

        # 数据质量监控
        self.outlier_threshold = 5.0  # 放宽到5倍标准差
        self.last_values = deque(maxlen=20)  # 增加历史窗口
        self.enable_outlier_detection = True  # 可以关闭异常检测
        
    def initialize_with_historical_data(self, historical_data):
        """使用历史数据初始化处理器"""
        if len(historical_data) < self.window_size:
            raise ValueError(f"历史数据长度必须至少为 {self.window_size}")
        
        # 标准化历史数据
        normalized_data = self.scaler.transform(
            np.array(historical_data[-self.window_size:]).reshape(-1, 1)
        ).flatten()
        
        # 填充处理缓冲区
        self.processed_buffer.clear()
        for value in normalized_data:
            self.processed_buffer.append(value)
        
        self.is_initialized = True
        print(f"实时处理器已初始化，包含 {len(self.processed_buffer)} 个历史数据点")
    
    def add_raw_data(self, value, timestamp=None):
        """添加原始数据到缓冲区"""
        if timestamp is None:
            timestamp = time.time()
        
        data_point = {
            'value': value,
            'timestamp': timestamp,
            'sequence': self.prediction_count
        }
        
        try:
            self.raw_buffer.put_nowait(data_point)
        except queue.Full:
            self.dropped_count += 1
            print(f"警告: 数据缓冲区满，丢弃数据点。总丢弃: {self.dropped_count}")
    
    def _is_outlier(self, value):
        """检测异常值"""
        if not self.enable_outlier_detection or len(self.last_values) < 5:
            return False

        recent_values = list(self.last_values)
        mean_val = np.mean(recent_values)
        std_val = np.std(recent_values)

        if std_val == 0:
            return False

        z_score = abs((value - mean_val) / std_val)
        return z_score > self.outlier_threshold
    
    def _preprocess_data(self, raw_value):
        """数据预处理"""
        # 1. 异常值检测和处理
        if self._is_outlier(raw_value):
            # 使用更温和的处理方式：线性插值而不是直接替换
            if len(self.last_values) >= 2:
                # 使用最近两个值的平均值进行平滑
                recent_values = list(self.last_values)[-2:]
                processed_value = np.mean(recent_values)
                print(f"检测到异常值 {raw_value:.6f}，平滑为 {processed_value:.6f}")
            else:
                processed_value = raw_value
        else:
            processed_value = raw_value

        # 2. 更新最近值记录（使用原始值而不是处理后的值）
        self.last_values.append(raw_value)

        # 3. 数据标准化
        normalized_value = self.scaler.transform([[processed_value]])[0, 0]

        return normalized_value
    
    def _calculate_bias_correction(self, lstm_prediction, actual_value):
        """计算并更新偏移与增益自适应校正"""
        if actual_value is None:
            return

        # 偏移校正（bias）
        if self.enable_bias_correction:
            bias = lstm_prediction - actual_value
            self.bias_samples.append(bias)
            self.bias_correction = (1.0 - self.bias_ema_alpha) * self.bias_correction + self.bias_ema_alpha * bias
            # 限幅，避免校正过度
            if self.bias_correction > self.bias_clip:
                self.bias_correction = self.bias_clip
            elif self.bias_correction < -self.bias_clip:
                self.bias_correction = -self.bias_clip

        # 增益校正（scale/gain）
        if self.enable_gain_correction:
            eps = 1e-6
            denom = lstm_prediction if abs(lstm_prediction) > eps else (eps if lstm_prediction >= 0 else -eps)
            instant_gain = actual_value / denom
            # 忽略异常增益比
            if np.isfinite(instant_gain):
                # EMA更新
                self.gain_correction = (1.0 - self.gain_ema_alpha) * self.gain_correction + self.gain_ema_alpha * float(instant_gain)
                # 限幅
                if self.gain_correction < self.gain_clip_min:
                    self.gain_correction = self.gain_clip_min
                elif self.gain_correction > self.gain_clip_max:
                    self.gain_correction = self.gain_clip_max

    def _make_prediction(self, actual_value=None):
        """进行LSTM+AKF+偏移校正预测 - 真正的预测（不融合当前观测值）"""
        if len(self.processed_buffer) < self.window_size:
            return None, None

        start_time = time.time()

        # 准备输入序列
        input_seq = torch.FloatTensor(list(self.processed_buffer)).unsqueeze(0).unsqueeze(-1)

        # LSTM预测
        with torch.no_grad():
            normalized_prediction = self.model(input_seq).item()

        # 反标准化得到LSTM原始预测
        lstm_prediction = self.scaler.inverse_transform([[normalized_prediction]])[0, 0]

        # 偏移+增益联合校正：y ≈ gain * (pred - bias)
        corrected_prediction = lstm_prediction
        if self.enable_bias_correction or self.enable_gain_correction:
            bias_term = self.bias_correction if self.enable_bias_correction else 0.0
            gain_term = self.gain_correction if self.enable_gain_correction else 1.0
            corrected_prediction = gain_term * (lstm_prediction - bias_term)

        # 移除硬性保护：真正的预测不应该使用当前观测值
        # 注释掉原来的硬性保护逻辑
        # if actual_value is not None:
        #     if abs(corrected_prediction - actual_value) > self.sanity_limit_um:
        #         corrected_prediction = actual_value

        # 注释掉原来的偏移校正计算，改为在预测完成后单独更新
        # self._calculate_bias_correction(lstm_prediction, actual_value)

        # 夹紧到最近真实值范围，减少系统性上偏
        if len(self.real_values) >= 10:
            recent_vals = np.array(list(self.real_values)[-100:])
            r_min, r_max = np.min(recent_vals), np.max(recent_vals)
            # 允许小幅超出范围
            margin = max(0.05 * (r_max - r_min), 0.01)
            if corrected_prediction > r_max + margin:
                corrected_prediction = r_max + margin
            elif corrected_prediction < r_min - margin:
                corrected_prediction = r_min - margin

        # 轻量EMA输出平滑（与AKF互补，降低短期抖动）
        if self.enable_output_ema:
            if self._last_output_ema is None:
                self._last_output_ema = corrected_prediction
            else:
                self._last_output_ema = (1.0 - self.output_ema_alpha) * self._last_output_ema + self.output_ema_alpha * corrected_prediction
            corrected_prediction = self._last_output_ema

        # AKF优化预测结果：仅使用历史观测进行状态估计，不融合当前观测
        if self.enable_akf and self.akf is not None:
            # 使用LSTM预测作为先验，但不使用当前观测值进行更新
            # 这样AKF只基于历史数据进行状态估计，保持预测的独立性
            prior = corrected_prediction

            # 仅进行时间更新，不进行测量更新（保持真正的预测性质）
            akf_prediction = self.akf.time_update(prior)

            # 注释掉AKF的测量更新，改为在预测完成后单独更新
            # if actual_value is not None and len(self.real_values) > 0:
            #     last_observation = list(self.real_values)[-1]
            #     self.akf.measurement_update(last_observation)
        else:
            # 如果没有启用AKF，直接使用纠偏/EMA后的预测
            akf_prediction = corrected_prediction

        # 记录处理时间
        processing_time = time.time() - start_time
        self.processing_times.append(processing_time)

        # 存储最近一次预测以便外部读取
        self.last_lstm_prediction = lstm_prediction
        self.last_corrected_prediction = corrected_prediction
        self.last_akf_prediction = akf_prediction

        return lstm_prediction, akf_prediction

    def _update_correction_parameters(self, lstm_prediction, actual_value):
        """在预测完成后更新校正参数（不影响当前预测）"""
        if actual_value is None:
            return

        # 更新偏移校正参数
        self._calculate_bias_correction(lstm_prediction, actual_value)

        # 更新AKF状态（使用当前观测值）
        if self.enable_akf and self.akf is not None:
            # 使用当前观测值更新AKF的内部状态，为下次预测做准备
            self.akf.measurement_update(actual_value)

    def process_single_point(self, raw_value, timestamp=None):
        """处理单个数据点"""
        if not self.is_initialized:
            raise RuntimeError("处理器未初始化，请先调用 initialize_with_historical_data()")

        if timestamp is None:
            timestamp = time.time()

        # 1. 数据预处理
        processed_value = self._preprocess_data(raw_value)

        # 2. 更新滑动窗口
        self.processed_buffer.append(processed_value)

        # 3. 进行LSTM+AKF预测（不传递当前观测值，保持预测的独立性）
        lstm_prediction, akf_prediction = self._make_prediction()

        # 4. 存储结果
        if lstm_prediction is not None and akf_prediction is not None:
            self.predictions.append(lstm_prediction)  # LSTM原始预测
            self.akf_predictions.append(akf_prediction)  # AKF优化预测
            self.timestamps.append(timestamp)
            self.real_values.append(raw_value)
            self.prediction_count += 1

            # 5. 使用当前观测值更新校正参数（但不影响当前预测）
            self._update_correction_parameters(lstm_prediction, raw_value)

        # 返回AKF优化后的预测结果
        return akf_prediction

    def get_last_predictions(self):
        """获取最近一次的(LSTM原始预测, AKF预测)。若暂无则返回(None, None)"""
        return self.last_lstm_prediction, self.last_akf_prediction

    def get_last_predictions_extended(self):
        """获取最近一次的(LSTM原始, 纠偏后, AKF)三种预测。"""
        return self.last_lstm_prediction, self.last_corrected_prediction, self.last_akf_prediction
    
    async def start_real_time_processing(self, data_source, prediction_callback=None):
        """启动实时处理"""
        self.is_running = True
        print("开始实时数据流处理...")
        
        while self.is_running:
            try:
                # 从数据源读取数据
                raw_data = await data_source.read_data()
                
                if raw_data is not None:
                    # 处理数据点
                    prediction = self.process_single_point(raw_data)
                    
                    # 调用回调函数
                    if prediction is not None and prediction_callback:
                        await prediction_callback(raw_data, prediction, time.time())
                
                # 控制处理频率
                await asyncio.sleep(0.001)  # 1ms间隔，支持1000Hz采样率
                
            except Exception as e:
                print(f"处理错误: {e}")
                await asyncio.sleep(0.1)
    
    def stop_processing(self):
        """停止实时处理"""
        self.is_running = False
        print("实时处理已停止")

    def get_statistics(self):
        """获取处理统计信息 - 包括LSTM和AKF对比"""
        if len(self.predictions) == 0:
            return {}

        real_vals = np.array(list(self.real_values))
        lstm_vals = np.array(list(self.predictions))
        akf_vals = np.array(list(self.akf_predictions))
        proc_times = np.array(list(self.processing_times))

        # LSTM误差统计
        lstm_errors = np.abs(real_vals - lstm_vals)
        lstm_relative_errors = np.abs((real_vals - lstm_vals) / real_vals) * 100

        # AKF误差统计
        akf_errors = np.abs(real_vals - akf_vals)
        akf_relative_errors = np.abs((real_vals - akf_vals) / real_vals) * 100

        # 改进率计算
        mae_improvement = (np.mean(lstm_errors) - np.mean(akf_errors)) / np.mean(lstm_errors) * 100
        rmse_improvement = (np.sqrt(np.mean(lstm_errors**2)) - np.sqrt(np.mean(akf_errors**2))) / np.sqrt(np.mean(lstm_errors**2)) * 100

        return {
            'total_predictions': len(self.predictions),
            'dropped_count': self.dropped_count,
            'avg_processing_time': np.mean(proc_times),
            'max_processing_time': np.max(proc_times),

            # LSTM统计
            'lstm_mae': np.mean(lstm_errors),
            'lstm_rmse': np.sqrt(np.mean(lstm_errors**2)),
            'lstm_accuracy_10percent': np.sum(lstm_relative_errors <= 10.0) / len(lstm_relative_errors) * 100,

            # AKF统计
            'akf_mae': np.mean(akf_errors),
            'akf_rmse': np.sqrt(np.mean(akf_errors**2)),
            'akf_accuracy_10percent': np.sum(akf_relative_errors <= 10.0) / len(akf_relative_errors) * 100,

            # 改进率
            'mae_improvement': mae_improvement,
            'rmse_improvement': rmse_improvement,
            'akf_enabled': self.enable_akf,

            # 偏移校正统计
            'bias_correction_enabled': self.enable_bias_correction,
            'bias_correction_value': self.bias_correction,
            'bias_calculated': self.bias_calculated,
            'bias_samples_count': len(self.bias_samples),

            # 增益校正统计
            'gain_correction_enabled': self.enable_gain_correction,
            'gain_correction_value': self.gain_correction,
        }
    
    def get_performance_stats(self):
        """获取性能统计"""
        if len(self.processing_times) == 0:
            return {}
        
        return {
            'avg_processing_time': np.mean(self.processing_times),
            'max_processing_time': np.max(self.processing_times),
            'min_processing_time': np.min(self.processing_times),
            'processing_rate': 1.0 / np.mean(self.processing_times),
            'total_predictions': self.prediction_count,
            'dropped_data_points': self.dropped_count,
            'buffer_utilization': len(self.processed_buffer) / self.window_size
        }
    
    def get_recent_results(self, n=100):
        """获取最近的预测结果"""
        n = min(n, len(self.predictions))
        return {
            'timestamps': list(self.timestamps)[-n:],
            'real_values': list(self.real_values)[-n:],
            'predictions': list(self.predictions)[-n:]
        }

# 模拟数据源（用于测试）
class SimulatedDataSource:
    """模拟实时数据源"""
    
    def __init__(self, data_file='v2.txt', sampling_rate=100):
        # 加载测试数据
        self.data = pd.read_csv(data_file, sep='\t', encoding='utf-8')
        self.displacement_data = self.data.iloc[:, 1].values  # 位移列
        self.current_index = 0
        self.sampling_interval = 1.0 / sampling_rate
        self.last_time = time.time()
        
    async def read_data(self):
        """模拟实时数据读取"""
        current_time = time.time()
        
        # 控制采样率
        if current_time - self.last_time < self.sampling_interval:
            return None
        
        if self.current_index >= len(self.displacement_data):
            return None
        
        value = self.displacement_data[self.current_index]
        self.current_index += 1
        self.last_time = current_time
        
        # 添加一些随机噪声模拟真实传感器
        noise = np.random.normal(0, 0.001)
        return value + noise
    
    def is_connected(self):
        return self.current_index < len(self.displacement_data)

# 实时可视化
class RealTimeVisualizer:
    """实时数据可视化"""
    
    def __init__(self, processor, max_points=200):
        self.processor = processor
        self.max_points = max_points
        
        # 创建图形
        self.fig, (self.ax1, self.ax2) = plt.subplots(2, 1, figsize=(12, 8))
        self.fig.suptitle('实时振动预测监控')
        
        # 初始化空线条
        self.line_real, = self.ax1.plot([], [], 'b-', label='真实值', alpha=0.7)
        self.line_pred, = self.ax1.plot([], [], 'r-', label='预测值', alpha=0.8)
        self.ax1.set_ylabel('位移 (μm)')
        self.ax1.set_title('实时预测对比')
        self.ax1.legend()
        self.ax1.grid(True, alpha=0.3)
        
        # 误差图
        self.line_error, = self.ax2.plot([], [], 'g-', label='预测误差', alpha=0.8)
        self.ax2.set_xlabel('时间步')
        self.ax2.set_ylabel('误差 (μm)')
        self.ax2.set_title('预测误差')
        self.ax2.legend()
        self.ax2.grid(True, alpha=0.3)
        
    def update_plot(self, frame):
        """更新图形"""
        results = self.processor.get_recent_results(self.max_points)
        
        if len(results['real_values']) > 1:
            # 更新数据
            x_data = range(len(results['real_values']))
            
            self.line_real.set_data(x_data, results['real_values'])
            self.line_pred.set_data(x_data, results['predictions'])
            
            # 计算误差
            errors = [abs(r - p) for r, p in zip(results['real_values'], results['predictions'])]
            self.line_error.set_data(x_data, errors)
            
            # 自动调整坐标轴
            for ax in [self.ax1, self.ax2]:
                ax.relim()
                ax.autoscale_view()
        
        return self.line_real, self.line_pred, self.line_error
    
    def start_animation(self, interval=100):
        """启动动画"""
        self.animation = FuncAnimation(
            self.fig, self.update_plot, interval=interval, blit=True
        )
        plt.show()

# 使用示例
async def prediction_callback(real_value, prediction, timestamp):
    """预测结果回调函数"""
    error = abs(real_value - prediction)
    print(f"时间: {timestamp:.3f}, 真实: {real_value:.6f}, 预测: {prediction:.6f}, 误差: {error:.6f}")

if __name__ == "__main__":
    print("实时数据流处理器演示")
    print("注意: 本演示需要v1.txt数据文件")
    print("程序将自动训练LSTM模型并进行实时预测演示")
