"""
测试预测时长修改是否正确
"""

import os
import re
import glob

def check_duration_in_file(filepath):
    """检查文件中的时长设置"""
    if not os.path.exists(filepath):
        return f"❌ 文件不存在: {filepath}"
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        results = []
        
        # 检查 duration_seconds 参数
        duration_matches = re.findall(r'duration_seconds\s*=\s*(\d+)', content)
        if duration_matches:
            for match in duration_matches:
                if match == '1':
                    results.append(f"✅ duration_seconds={match}")
                else:
                    results.append(f"⚠️ duration_seconds={match} (不是1秒)")
        
        # 检查 prediction_duration 变量
        pred_duration_matches = re.findall(r'prediction_duration\s*=\s*(\d+)', content)
        if pred_duration_matches:
            for match in pred_duration_matches:
                if match == '1':
                    results.append(f"✅ prediction_duration={match}")
                else:
                    results.append(f"⚠️ prediction_duration={match} (不是1秒)")
        
        # 检查 test_duration 变量
        test_duration_matches = re.findall(r'test_duration\s*=\s*(\d+)', content)
        if test_duration_matches:
            for match in test_duration_matches:
                if match == '1':
                    results.append(f"✅ test_duration={match}")
                else:
                    results.append(f"⚠️ test_duration={match} (不是1秒)")
        
        # 检查默认值提示
        default_matches = re.findall(r'默认(\d+)', content)
        if default_matches:
            for match in default_matches:
                if match == '1':
                    results.append(f"✅ 默认值={match}")
                else:
                    results.append(f"⚠️ 默认值={match} (不是1秒)")
        
        if not results:
            return f"ℹ️ 未找到时长设置"
        
        return results
        
    except Exception as e:
        return f"❌ 读取文件失败: {e}"

def main():
    """主函数"""
    print("🔍 检查预测时长修改情况")
    print("="*60)
    
    # 需要检查的文件列表
    files_to_check = [
        "real_time_callback_prediction.py",
        "advanced_real_time_prediction.py", 
        "run_prediction_and_plot.py",
        "test_csv_only.py",
        "start_real_time_prediction.py",
        "integrated_compare.py",
        "integrated_real_time_demo.py",
        "optimized_real_time_demo.py",
        "diagnose_data_source.py"
    ]
    
    for filepath in files_to_check:
        print(f"\n📄 检查文件: {filepath}")
        print("-" * 50)
        
        results = check_duration_in_file(filepath)
        if isinstance(results, list):
            for result in results:
                print(f"   {result}")
        else:
            print(f"   {results}")
    
    print("\n" + "="*60)
    print("🎯 总结:")
    print("   ✅ = 已正确设置为1秒")
    print("   ⚠️ = 需要手动检查或修改")
    print("   ❌ = 文件不存在或读取失败")
    print("   ℹ️ = 未找到相关设置")

if __name__ == "__main__":
    main()
