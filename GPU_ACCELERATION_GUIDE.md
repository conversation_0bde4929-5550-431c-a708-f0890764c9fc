# LSTM 模型 GPU 加速完整指南

## 📋 目录
1. [环境检查](#环境检查)
2. [GPU 加速方法](#gpu-加速方法)
3. [代码修改](#代码修改)
4. [性能对比](#性能对比)
5. [常见问题](#常见问题)

---

## 🔍 环境检查

### 1. 检查 CUDA 是否可用
```python
import torch
print(f"CUDA 可用: {torch.cuda.is_available()}")
print(f"CUDA 版本: {torch.version.cuda}")
print(f"GPU 数量: {torch.cuda.device_count()}")
print(f"当前 GPU: {torch.cuda.get_device_name(0)}")
```

### 2. 检查 PyTorch 版本
```bash
python -c "import torch; print(torch.__version__)"
```

### 3. 检查 GPU 内存
```python
import torch
print(f"GPU 内存总量: {torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB")
print(f"GPU 内存已用: {torch.cuda.memory_allocated(0) / 1e9:.2f} GB")
```

---

## 🚀 GPU 加速方法

### 方法 1：基础 GPU 加速（推荐）

#### 步骤 1：定义设备
```python
import torch

# 自动检测 GPU
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")
```

#### 步骤 2：模型移到 GPU
```python
# 训练时
model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
model = model.to(device)  # 移到 GPU

# 或者
model.cuda()  # 等价于 model.to("cuda")
```

#### 步骤 3：数据移到 GPU
```python
# 训练数据
X = X.to(device)
y = y.to(device)

# 推理数据
input_seq = input_seq.to(device)
```

#### 步骤 4：推理
```python
with torch.no_grad():
    output = model(input_seq)  # 自动在 GPU 上计算
```

---

### 方法 2：混合精度加速（更快）

```python
from torch.cuda.amp import autocast, GradScaler

# 创建 GradScaler
scaler = GradScaler()

# 训练循环
for epoch in range(epochs):
    optimizer.zero_grad()
    
    # 使用自动混合精度
    with autocast():
        outputs = model(X)
        loss = criterion(outputs, y)
    
    # 反向传播
    scaler.scale(loss).backward()
    scaler.step(optimizer)
    scaler.update()
```

---

### 方法 3：批量预测加速（最快）

```python
# 一次预测多个数据点，而不是逐个预测
batch_size = 32
predictions = []

with torch.no_grad():
    for i in range(0, len(data), batch_size):
        batch = data[i:i+batch_size].to(device)
        batch_pred = model(batch)
        predictions.append(batch_pred.cpu())

predictions = torch.cat(predictions)
```

---

## 💻 代码修改

### 修改 1：训练代码（real_time_train.py）

```python
# 在 train_displacement_model 函数中添加

# 检测设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

# 创建模型并移到 GPU
model = DisplacementLSTM(input_size=1, hidden_size=hidden_size, output_size=1)
model = model.to(device)

# 数据移到 GPU
X = X.to(device)
y = y.to(device)

# 训练循环保持不变
for epoch in range(epochs):
    optimizer.zero_grad()
    outputs = model(X).squeeze()
    loss = criterion(outputs, y)
    loss.backward()
    optimizer.step()
    
    if epoch % 25 == 0:
        print(f'Epoch {epoch:3d}: Loss = {loss.item():.6f}')

# 保存前移回 CPU（可选）
model = model.cpu()
```

### 修改 2：推理代码（real_time_stream_processor.py）

```python
# 在 __init__ 中添加
self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
self.model = self.model.to(self.device)

# 在 _make_prediction 中修改
input_seq = torch.FloatTensor(list(self.processed_buffer)).unsqueeze(0).unsqueeze(-1)
input_seq = input_seq.to(self.device)  # 移到 GPU

with torch.no_grad():
    normalized_prediction = self.model(input_seq).item()
```

### 修改 3：加载模型代码

```python
# 加载模型时指定设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
model.load_state_dict(torch.load('displacement_model.pth'))
model = model.to(device)
model.eval()
```

---

## 📊 性能对比

### 预期性能提升

| 操作 | CPU | GPU | 提升倍数 |
|------|-----|-----|---------|
| 单点推理 | 3.3ms | 0.5ms | 6.6x |
| 批量推理(32) | 105ms | 8ms | 13x |
| 训练(100 epoch) | 45s | 5s | 9x |

### 实际测试结果

```
GPU 加速前：
- 采样率: 60 Hz
- 处理时间: 3.3ms
- 理论最大: 302.5 Hz

GPU 加速后（预期）：
- 采样率: 200-300 Hz
- 处理时间: 0.5-1ms
- 理论最大: 1000-2000 Hz
```

---

## ⚠️ 常见问题

### Q1: 显存不足怎么办？
```python
# 减小批量大小
batch_size = 16  # 从 32 改为 16

# 或者使用梯度累积
accumulation_steps = 4
for i, (X, y) in enumerate(dataloader):
    outputs = model(X.to(device))
    loss = criterion(outputs, y.to(device)) / accumulation_steps
    loss.backward()
    
    if (i + 1) % accumulation_steps == 0:
        optimizer.step()
        optimizer.zero_grad()
```

### Q2: 模型在 GPU 上但数据在 CPU 上怎么办？
```python
# 错误示例
model = model.cuda()
output = model(input_data)  # 错误！input_data 在 CPU 上

# 正确示例
model = model.cuda()
input_data = input_data.cuda()
output = model(input_data)  # 正确！
```

### Q3: 如何在 CPU 和 GPU 之间切换？
```python
# 动态选择设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 或者强制使用 CPU
device = torch.device("cpu")

# 模型和数据都使用同一个设备
model = model.to(device)
data = data.to(device)
```

### Q4: 保存和加载模型时需要注意什么？
```python
# 保存时（模型可以在 GPU 上）
torch.save(model.state_dict(), 'model.pth')

# 加载时（指定设备）
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = DisplacementLSTM()
model.load_state_dict(torch.load('model.pth'))
model = model.to(device)
```

---

## 🎯 快速开始

### 1. 检查环境
```bash
python check_gpu.py
```

### 2. 修改代码
- 在模型初始化后添加 `.to(device)`
- 在数据处理时添加 `.to(device)`
- 在推理时确保数据在同一设备上

### 3. 运行程序
```bash
python integrated_real_time_demo.py
```

### 4. 验证性能
```bash
python verify_optimization.py
```

---

## 📈 预期结果

### 采样率提升
- **优化前**：60 Hz
- **GPU 加速后**：200-300 Hz（3-5 倍提升）
- **GPU + 批量预测**：500-1000 Hz（8-16 倍提升）

### 处理时间降低
- **优化前**：3.3ms
- **GPU 加速后**：0.5-1ms（3-6 倍加速）
- **GPU + 批量预测**：0.1-0.2ms（16-33 倍加速）

---

**下一步**：查看 `gpu_acceleration_implementation.py` 了解完整的实现代码。

