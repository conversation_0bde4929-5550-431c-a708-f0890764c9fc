"""
分析和绘制已保存的位移预测数据
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import glob
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def find_latest_data_file():
    """查找最新的数据文件"""
    # 查找CSV文件
    csv_files = glob.glob("real_time_predictions_*.csv")
    if not csv_files:
        csv_files = glob.glob("displacement_data_*.csv")
    
    if not csv_files:
        return None
    
    # 按修改时间排序，返回最新的
    csv_files.sort(key=os.path.getmtime, reverse=True)
    return csv_files[0]

def load_and_analyze_data(filename, time_threshold=0.0001):
    """加载和分析数据"""
    print(f"📊 加载数据文件: {filename}")

    try:
        df = pd.read_csv(filename)
        print(f"✅ 成功加载 {len(df)} 行数据")

        # 检查列名
        print(f"📋 数据列: {list(df.columns)}")

        # 检查是否已经有标准列名
        has_time = 'time' in df.columns
        has_real_value = 'real_value' in df.columns
        has_prediction = 'prediction' in df.columns
        has_error = 'error' in df.columns

        print(f"📊 列名检查:")
        print(f"   time: {'✅' if has_time else '❌'}")
        print(f"   real_value: {'✅' if has_real_value else '❌'}")
        print(f"   prediction: {'✅' if has_prediction else '❌'}")
        print(f"   error: {'✅' if has_error else '❌'}")

        # 如果已经有标准列名，直接使用，不进行重命名
        if has_time and has_real_value and has_prediction:
            print("📊 数据列已标准化，直接使用")
        else:
            # 只有在缺少标准列名时才进行映射
            column_mapping = {}
            for col in df.columns:
                if not has_time and col != 'time' and ('time' in col.lower() or '时间' in col):
                    column_mapping[col] = 'time'
                elif not has_real_value and col != 'real_value' and ('real' in col.lower() or '真实' in col or '实际' in col):
                    column_mapping[col] = 'real_value'
                elif not has_prediction and col != 'prediction' and ('pred' in col.lower() or '预测' in col):
                    column_mapping[col] = 'prediction'
                elif not has_error and col != 'error' and ('error' in col.lower() or '误差' in col):
                    column_mapping[col] = 'error'

            # 执行重命名
            if column_mapping:
                print(f"📊 重命名列: {column_mapping}")
                df = df.rename(columns=column_mapping)
            else:
                print("📊 无需重命名列")

        # 如果没有误差列，计算误差
        if 'error' not in df.columns and 'real_value' in df.columns and 'prediction' in df.columns:
            df['error'] = df['prediction'] - df['real_value']
            print("📊 计算预测误差")

        # 检查数据质量
        print(f"📊 数据质量检查:")
        print(f"   数据类型: {df.dtypes.to_dict()}")

        # 检查是否有重复的时间戳
        if 'time' in df.columns:
            duplicate_times = df['time'].duplicated().sum()
            print(f"   重复时间戳: {duplicate_times} 个")

            if duplicate_times > 0:
                print("⚠️ 发现重复时间戳，正在处理...")
                # 为重复的时间戳添加微小偏移
                df = df.sort_values('time').reset_index(drop=True)

                # 找到重复的时间戳并添加微小偏移
                for i in range(1, len(df)):
                    if df.loc[i, 'time'] <= df.loc[i-1, 'time']:
                        df.loc[i, 'time'] = df.loc[i-1, 'time'] + 1e-8

                print(f"   ✅ 已处理重复时间戳")

        # 过滤时间数据：只保留time_threshold秒以后的数据
        if 'time' in df.columns:
            original_count = len(df)
            df = df[df['time'] > time_threshold].copy()
            filtered_count = len(df)
            print(f"🔍 时间过滤: 保留 {time_threshold} 秒后的数据")
            print(f"   过滤前: {original_count} 行")
            print(f"   过滤后: {filtered_count} 行")
            print(f"   移除了: {original_count - filtered_count} 行初始数据")

            # 重置索引
            df = df.reset_index(drop=True)

            # 重置时间，使过滤后的数据从0开始
            if filtered_count > 0:
                time_offset = df['time'].min()
                df['time'] = df['time'] - time_offset
                print(f"   时间重置: 从 {df['time'].min():.6f}s 开始")
        else:
            print("⚠️ 未找到时间列，无法进行时间过滤")

        return df
        
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return None

def create_comprehensive_plots(df, filename):
    """创建综合分析图表"""
    print("🎨 生成综合分析图表...")
    
    # 创建图形
    fig = plt.figure(figsize=(20, 15))
    
    # 获取数据
    times = df['time'].values if 'time' in df.columns else np.arange(len(df))
    real_values = df['real_value'].values
    predictions = df['prediction'].values
    errors = df['error'].values
    
    # 1. 位移对比图 (大图)
    ax1 = plt.subplot(3, 2, (1, 2))
    ax1.plot(times, real_values, 'b-', linewidth=1.5, label='实际位移', alpha=0.8)
    ax1.plot(times, predictions, 'r-', linewidth=1.5, label='预测位移', alpha=0.8)
    ax1.set_title('实际位移 vs 预测位移对比', fontsize=14, fontweight='bold')
    ax1.set_xlabel('时间 (秒)')
    ax1.set_ylabel('位移 (μm)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 添加统计信息
    mae = np.mean(np.abs(errors))
    rmse = np.sqrt(np.mean(errors**2))
    correlation = np.corrcoef(real_values, predictions)[0, 1]
    
    stats_text = f'统计信息:\nMAE: {mae:.6f}μm\nRMSE: {rmse:.6f}μm\n相关系数: {correlation:.6f}\n数据点: {len(df)}'
    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 2. 误差时间序列图
    ax2 = plt.subplot(3, 2, (3, 4))
    ax2.plot(times, errors, 'g-', linewidth=1, alpha=0.7, label='预测误差')
    ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax2.axhline(y=np.mean(errors), color='red', linestyle='--', alpha=0.7, label=f'平均误差: {np.mean(errors):.6f}μm')
    ax2.fill_between(times, errors, 0, alpha=0.3, color='green')
    ax2.set_title('预测误差随时间变化', fontsize=14, fontweight='bold')
    ax2.set_xlabel('时间 (秒)')
    ax2.set_ylabel('误差 (μm)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 误差分布直方图
    ax3 = plt.subplot(3, 2, 5)
    n, bins, patches = ax3.hist(errors, bins=50, alpha=0.7, color='orange', edgecolor='black', density=True)
    ax3.axvline(x=np.mean(errors), color='red', linestyle='--', linewidth=2, label=f'平均: {np.mean(errors):.6f}μm')
    ax3.axvline(x=np.median(errors), color='blue', linestyle='--', linewidth=2, label=f'中位数: {np.median(errors):.6f}μm')
    ax3.axvline(x=0, color='black', linestyle='-', alpha=0.5)
    ax3.set_title('误差分布直方图', fontsize=12, fontweight='bold')
    ax3.set_xlabel('误差 (μm)')
    ax3.set_ylabel('概率密度')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 散点图 (预测 vs 实际)
    ax4 = plt.subplot(3, 2, 6)
    scatter = ax4.scatter(real_values, predictions, alpha=0.6, s=10, c=np.abs(errors), cmap='viridis')
    
    # 添加理想预测线 (y=x)
    min_val = min(np.min(real_values), np.min(predictions))
    max_val = max(np.max(real_values), np.max(predictions))
    ax4.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='理想预测线')
    
    ax4.set_title('预测值 vs 实际值散点图', fontsize=12, fontweight='bold')
    ax4.set_xlabel('实际位移 (μm)')
    ax4.set_ylabel('预测位移 (μm)')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 添加颜色条
    cbar = plt.colorbar(scatter, ax=ax4)
    cbar.set_label('绝对误差 (μm)')
    
    plt.tight_layout()
    
    # 保存图表
    base_name = os.path.splitext(filename)[0]
    plot_filename = f"{base_name}_analysis.png"
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"📊 综合分析图表已保存到: {plot_filename}")
    
    return fig

def create_detailed_error_analysis(df, filename):
    """创建详细的误差分析图"""
    print("🔍 生成详细误差分析图...")
    
    errors = df['error'].values
    times = df['time'].values if 'time' in df.columns else np.arange(len(df))
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('详细误差分析', fontsize=16, fontweight='bold')
    
    # 1. 误差时间序列（带统计区间）
    ax = axes[0, 0]
    ax.plot(times, errors, 'b-', linewidth=1, alpha=0.7)
    ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax.axhline(y=np.mean(errors), color='red', linestyle='--', label='平均值')
    ax.axhline(y=np.mean(errors) + np.std(errors), color='orange', linestyle=':', label='+1σ')
    ax.axhline(y=np.mean(errors) - np.std(errors), color='orange', linestyle=':', label='-1σ')
    ax.fill_between(times, np.mean(errors) - np.std(errors), np.mean(errors) + np.std(errors), alpha=0.2, color='orange')
    ax.set_title('误差时间序列（含统计区间）')
    ax.set_xlabel('时间 (秒)')
    ax.set_ylabel('误差 (μm)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 2. 累积误差
    ax = axes[0, 1]
    cumulative_error = np.cumsum(np.abs(errors))
    ax.plot(times, cumulative_error, 'g-', linewidth=2)
    ax.set_title('累积绝对误差')
    ax.set_xlabel('时间 (秒)')
    ax.set_ylabel('累积绝对误差 (μm)')
    ax.grid(True, alpha=0.3)
    
    # 3. 滚动平均误差
    ax = axes[0, 2]
    window_size = max(50, len(errors) // 100)
    rolling_mae = pd.Series(np.abs(errors)).rolling(window=window_size).mean()
    ax.plot(times, rolling_mae, 'purple', linewidth=2, label=f'滚动MAE (窗口={window_size})')
    ax.set_title('滚动平均绝对误差')
    ax.set_xlabel('时间 (秒)')
    ax.set_ylabel('滚动MAE (μm)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 4. 误差分布（正态性检验）
    ax = axes[1, 0]
    from scipy import stats
    ax.hist(errors, bins=50, alpha=0.7, density=True, color='lightblue', edgecolor='black')
    
    # 拟合正态分布
    mu, sigma = stats.norm.fit(errors)
    x = np.linspace(np.min(errors), np.max(errors), 100)
    ax.plot(x, stats.norm.pdf(x, mu, sigma), 'r-', linewidth=2, label=f'正态拟合 (μ={mu:.6f}, σ={sigma:.6f})')
    ax.set_title('误差分布与正态拟合')
    ax.set_xlabel('误差 (μm)')
    ax.set_ylabel('概率密度')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 5. Q-Q图（正态性检验）
    ax = axes[1, 1]
    stats.probplot(errors, dist="norm", plot=ax)
    ax.set_title('Q-Q图（正态性检验）')
    ax.grid(True, alpha=0.3)
    
    # 6. 误差自相关（可选，需要statsmodels）
    ax = axes[1, 2]
    try:
        from statsmodels.tsa.stattools import acf
        lags = min(100, len(errors) // 10)
        autocorr = acf(errors, nlags=lags, fft=True)
        ax.plot(range(len(autocorr)), autocorr, 'b-', linewidth=2)
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax.axhline(y=0.05, color='red', linestyle='--', alpha=0.5, label='5%显著性水平')
        ax.axhline(y=-0.05, color='red', linestyle='--', alpha=0.5)
        ax.set_title('误差自相关函数')
        ax.set_xlabel('滞后期')
        ax.set_ylabel('自相关系数')
        ax.legend()
        ax.grid(True, alpha=0.3)
    except ImportError:
        # 如果没有statsmodels，显示简单的误差统计
        ax.text(0.5, 0.5, '误差自相关分析\n(需要安装statsmodels)\n\npip install statsmodels',
                ha='center', va='center', transform=ax.transAxes, fontsize=12,
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        ax.set_title('误差自相关函数 (不可用)')
        ax.set_xticks([])
        ax.set_yticks([])
    
    plt.tight_layout()
    
    # 保存图表
    base_name = os.path.splitext(filename)[0]
    plot_filename = f"{base_name}_error_analysis.png"
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"🔍 详细误差分析图已保存到: {plot_filename}")
    
    return fig

def print_detailed_statistics(df):
    """打印详细统计信息"""
    print("\n" + "="*60)
    print("📊 详细统计分析报告")
    print("="*60)
    
    real_values = df['real_value'].values
    predictions = df['prediction'].values
    errors = df['error'].values
    
    print(f"📈 数据概况:")
    print(f"   数据点数量: {len(df)}")
    if 'time' in df.columns:
        total_time = df['time'].max() - df['time'].min()
        print(f"   时间跨度: {total_time:.2f} 秒")
        print(f"   采样频率: {len(df)/total_time:.1f} Hz")
    
    print(f"\n📊 位移统计:")
    print(f"   实际位移范围: [{np.min(real_values):.6f}, {np.max(real_values):.6f}] μm")
    print(f"   预测位移范围: [{np.min(predictions):.6f}, {np.max(predictions):.6f}] μm")
    print(f"   位移变化幅度: {np.max(real_values) - np.min(real_values):.6f} μm")
    print(f"   实际位移标准差: {np.std(real_values):.6f} μm")
    print(f"   预测位移标准差: {np.std(predictions):.6f} μm")
    
    print(f"\n📊 误差统计:")
    print(f"   平均误差(ME): {np.mean(errors):.6f} μm")
    print(f"   平均绝对误差(MAE): {np.mean(np.abs(errors)):.6f} μm")
    print(f"   均方根误差(RMSE): {np.sqrt(np.mean(errors**2)):.6f} μm")
    print(f"   误差标准差: {np.std(errors):.6f} μm")
    print(f"   最大正误差: {np.max(errors):.6f} μm")
    print(f"   最大负误差: {np.min(errors):.6f} μm")
    print(f"   误差范围: [{np.min(errors):.6f}, {np.max(errors):.6f}] μm")
    
    # 百分位数
    percentiles = [5, 25, 50, 75, 95]
    print(f"\n📊 误差百分位数:")
    for p in percentiles:
        value = np.percentile(np.abs(errors), p)
        print(f"   {p}%: {value:.6f} μm")
    
    # 相关性分析
    correlation = np.corrcoef(real_values, predictions)[0, 1]
    print(f"\n📊 预测质量:")
    print(f"   预测-实际相关系数: {correlation:.6f}")
    
    # 相对误差
    relative_errors = np.abs(errors) / (np.abs(real_values) + 1e-10) * 100
    print(f"   平均相对误差: {np.mean(relative_errors):.4f}%")
    print(f"   相对误差标准差: {np.std(relative_errors):.4f}%")
    
    # 质量评级
    if correlation > 0.95 and np.mean(np.abs(errors)) < 0.1:
        quality = "优秀"
    elif correlation > 0.9 and np.mean(np.abs(errors)) < 0.5:
        quality = "良好"
    elif correlation > 0.8 and np.mean(np.abs(errors)) < 1.0:
        quality = "一般"
    else:
        quality = "需要改进"
    
    print(f"   预测质量评级: {quality}")

def main():
    """主函数"""
    print("📊 位移预测数据分析和可视化程序")
    print("="*60)

    # 获取用户输入的时间阈值
    time_threshold_input = input("请输入时间过滤阈值（秒，默认0.0001秒）: ").strip()
    try:
        time_threshold = float(time_threshold_input) if time_threshold_input else 0.0001
    except ValueError:
        time_threshold = 0.0001
        print(f"⚠️ 输入无效，使用默认值: {time_threshold} 秒")

    print(f"🔍 将过滤掉前 {time_threshold} 秒的数据")

    # 查找数据文件
    filename = find_latest_data_file()

    if filename is None:
        print("❌ 未找到数据文件")
        print("请确保存在以下格式的CSV文件:")
        print("  - real_time_predictions_*.csv")
        print("  - displacement_data_*.csv")
        return

    # 加载数据
    df = load_and_analyze_data(filename, time_threshold=time_threshold)
    if df is None:
        return

    # 检查过滤后是否还有数据
    if len(df) == 0:
        print(f"❌ 过滤后没有数据！时间阈值 {time_threshold} 秒可能过大")
        return
    
    # 检查必要的列
    required_columns = ['real_value', 'prediction']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"❌ 缺少必要的数据列: {missing_columns}")
        print(f"可用的列: {list(df.columns)}")
        return
    
    # 生成图表
    try:
        # 综合分析图
        fig1 = create_comprehensive_plots(df, filename)
        
        # 详细误差分析图
        fig2 = create_detailed_error_analysis(df, filename)
        
        # 打印统计信息
        print_detailed_statistics(df)
        
        # 显示图表
        plt.show()
        
        print("\n✅ 分析完成！图表已保存并显示。")
        
    except Exception as e:
        print(f"❌ 生成图表时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
