#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU 环境检查脚本
快速检查 CUDA 和 GPU 是否可用
"""

import torch
import sys


def main():
    print("\n" + "=" * 70)
    print("GPU 环境检查")
    print("=" * 70 + "\n")
    
    # 1. PyTorch 版本
    print("📦 PyTorch 信息:")
    print(f"   版本: {torch.__version__}")
    print(f"   CUDA 版本: {torch.version.cuda}")
    print()
    
    # 2. CUDA 可用性
    print("🔍 CUDA 检查:")
    cuda_available = torch.cuda.is_available()
    print(f"   CUDA 可用: {'✅ 是' if cuda_available else '❌ 否'}")
    
    if not cuda_available:
        print("\n⚠️  GPU 不可用！")
        print("   请检查:")
        print("   1. NVIDIA GPU 驱动是否已安装")
        print("   2. CUDA Toolkit 是否已安装")
        print("   3. PyTorch 是否使用了 GPU 版本")
        print("\n   安装 GPU 版本 PyTorch:")
        print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
        return False
    
    # 3. GPU 信息
    print(f"   GPU 数量: {torch.cuda.device_count()}")
    print()
    
    print("🖥️  GPU 信息:")
    for i in range(torch.cuda.device_count()):
        props = torch.cuda.get_device_properties(i)
        print(f"\n   GPU {i}: {props.name}")
        print(f"   - 显存: {props.total_memory / 1e9:.2f} GB")
        print(f"   - 计算能力: {props.major}.{props.minor}")
        if hasattr(props, 'multi_processor_count'):
            print(f"   - 多处理器数: {props.multi_processor_count}")
    
    # 4. 当前 GPU 内存
    print("\n💾 GPU 内存:")
    total_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
    allocated_memory = torch.cuda.memory_allocated(0) / 1e9
    reserved_memory = torch.cuda.memory_reserved(0) / 1e9
    
    print(f"   总显存: {total_memory:.2f} GB")
    print(f"   已分配: {allocated_memory:.2f} GB")
    print(f"   已预留: {reserved_memory:.2f} GB")
    print(f"   可用: {total_memory - reserved_memory:.2f} GB")
    
    # 5. 简单测试
    print("\n🧪 功能测试:")
    try:
        # 创建张量
        x = torch.randn(1000, 1000).cuda()
        y = torch.randn(1000, 1000).cuda()
        
        # 矩阵乘法
        z = torch.matmul(x, y)
        
        # 同步
        torch.cuda.synchronize()
        
        print("   ✅ GPU 计算测试: 通过")
        print(f"   - 输入形状: {x.shape}")
        print(f"   - 输出形状: {z.shape}")
    except Exception as e:
        print(f"   ❌ GPU 计算测试: 失败")
        print(f"   - 错误: {e}")
        return False
    
    # 6. 性能测试
    print("\n⚡ 性能测试:")
    import time
    
    # CPU 测试
    x_cpu = torch.randn(10000, 10000)
    y_cpu = torch.randn(10000, 10000)
    
    start = time.time()
    for _ in range(10):
        _ = torch.matmul(x_cpu, y_cpu)
    cpu_time = time.time() - start
    
    # GPU 测试
    x_gpu = torch.randn(10000, 10000).cuda()
    y_gpu = torch.randn(10000, 10000).cuda()
    
    torch.cuda.synchronize()
    start = time.time()
    for _ in range(10):
        _ = torch.matmul(x_gpu, y_gpu)
    torch.cuda.synchronize()
    gpu_time = time.time() - start
    
    speedup = cpu_time / gpu_time
    print(f"   CPU 耗时: {cpu_time:.3f}s")
    print(f"   GPU 耗时: {gpu_time:.3f}s")
    print(f"   加速比: {speedup:.1f}x")
    
    # 7. 总结
    print("\n" + "=" * 70)
    print("✅ GPU 环境检查完成！")
    print("=" * 70 + "\n")
    
    print("📝 建议:")
    print("   1. 使用 GPU 加速 LSTM 训练和推理")
    print("   2. 在代码中添加 .to(device) 将模型和数据移到 GPU")
    print("   3. 使用混合精度加速（torch.cuda.amp）")
    print("   4. 考虑批量预测以获得最佳性能")
    print()
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

