
def predict_with_bias_correction(model, scaler, input_sequence, bias_correction):
    """
    使用偏差校正的预测函数
    
    Args:
        model: 训练好的LSTM模型
        scaler: 数据标准化器
        input_sequence: 输入序列 (numpy array)
        bias_correction: 偏差校正值
    
    Returns:
        float: 校正后的预测值
    """
    import torch
    import numpy as np
    
    # 标准化输入序列
    normalized_seq = scaler.transform(input_sequence.reshape(-1, 1)).flatten()
    input_tensor = torch.FloatTensor(normalized_seq).unsqueeze(0).unsqueeze(-1)
    
    # 模型预测
    with torch.no_grad():
        normalized_pred = model(input_tensor).item()
    
    # 反标准化
    pred = scaler.inverse_transform([[normalized_pred]])[0, 0]
    
    # 应用偏差校正
    corrected_pred = pred - bias_correction
    
    return corrected_pred

# 使用示例:
# bias_correction = 0.015184  # 从bias_correction_config.pkl中加载
# corrected_prediction = predict_with_bias_correction(model, scaler, input_seq, bias_correction)
