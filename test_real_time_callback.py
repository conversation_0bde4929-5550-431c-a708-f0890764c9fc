"""
测试DSA实时回调预测系统
验证每次DSA回调都能触发预测
"""

import asyncio
import time
import numpy as np
import os
from data_collector_3s import DSADataCollector
from real_time_stream_processor import RealTimeStreamProcessor
from real_time_train import train_displacement_model

class SimpleRealTimeTest:
    """简单的实时预测测试"""
    
    def __init__(self, dll_path):
        self.dll_path = dll_path
        self.prediction_count = 0
        self.start_time = None
        self.predictions = []
        
    def prediction_callback(self, real_value, prediction, timestamp, data_type):
        """预测回调函数"""
        if self.start_time is None:
            self.start_time = timestamp
            print("🚀 开始接收实时预测结果...")
        
        relative_time = timestamp - self.start_time
        error = abs(real_value - prediction)
        
        self.prediction_count += 1
        self.predictions.append({
            'time': relative_time,
            'real': real_value,
            'pred': prediction,
            'error': error
        })
        
        # 每10个预测显示一次
        if self.prediction_count % 10 == 0:
            print(f"预测#{self.prediction_count}: t={relative_time:.6f}s, "
                  f"真实={real_value:.6f}μm, 预测={prediction:.6f}μm, 误差={error:.6f}μm")
    
    async def run_test(self, duration=10):
        """运行测试"""
        print("="*50)
        print("🧪 DSA实时回调预测测试")
        print("="*50)
        
        # 1. 检查训练数据
        if not os.path.exists('v1.txt'):
            print("❌ 未找到训练数据 v1.txt")
            print("请先运行数据收集程序")
            return False
        
        # 2. 快速训练模型
        print("🚀 快速训练模型...")
        model, scaler = train_displacement_model(
            data_file='v1.txt',
            window_size=25,
            hidden_size=64,  # 较小的模型用于测试
            learning_rate=0.01,
            epochs=50,  # 较少的轮数用于测试
            train_points=1000  # 较少的数据用于测试
        )
        
        if model is None or scaler is None:
            print("❌ 模型训练失败")
            return False
        
        print("✅ 模型训练完成")
        
        # 3. 创建预测器
        print("🔧 创建预测器...")
        predictor = RealTimeStreamProcessor(
            model=model,
            scaler=scaler,
            window_size=25,
            enable_akf=True
        )
        
        # 4. 创建数据收集器
        print("🔧 创建数据收集器...")
        collector = DSADataCollector(self.dll_path)
        collector.enable_continuous_mode()
        collector.enable_real_time_streaming()
        
        # 5. 收集初始化数据
        print("📊 收集初始化数据...")
        if not collector.initialize_sdk():
            print("❌ SDK初始化失败")
            return False
        
        if not collector.start_collection():
            print("❌ 数据收集启动失败")
            return False
        
        # 收集25个初始化数据点
        historical_data = []
        timeout_count = 0
        
        while len(historical_data) < 25 and timeout_count < 50:
            data = collector.get_real_time_data_by_type('displacement', timeout=0.1)
            if data is not None:
                historical_data.append(data['value'])
                if len(historical_data) % 5 == 0:
                    print(f"收集初始化数据: {len(historical_data)}/25")
            else:
                timeout_count += 1
                await asyncio.sleep(0.1)
        
        if len(historical_data) < 25:
            print(f"❌ 初始化数据不足: {len(historical_data)}/25")
            collector.stop_collection()
            return False
        
        # 初始化预测器
        predictor.initialize_with_historical_data(historical_data)
        print("✅ 预测器初始化完成")
        
        # 6. 设置实时预测
        collector.set_real_time_predictor(predictor)
        collector.set_prediction_callback(self.prediction_callback)
        
        print(f"🚀 开始{duration}秒实时预测测试...")
        print("每次DSA回调都会自动触发预测")
        
        # 7. 等待测试完成
        try:
            await asyncio.sleep(duration)
        except KeyboardInterrupt:
            print("⚠️ 用户中断测试")
        
        # 8. 停止收集
        collector.stop_collection()
        
        # 9. 显示结果
        self.show_results()
        
        return True
    
    def show_results(self):
        """显示测试结果"""
        print("\n" + "="*50)
        print("📊 测试结果")
        print("="*50)
        
        if len(self.predictions) == 0:
            print("❌ 没有收集到预测数据")
            return
        
        # 计算统计信息
        errors = [p['error'] for p in self.predictions]
        times = [p['time'] for p in self.predictions]
        
        total_time = max(times) - min(times) if len(times) > 1 else 0
        prediction_rate = len(self.predictions) / total_time if total_time > 0 else 0
        
        avg_error = np.mean(errors)
        max_error = np.max(errors)
        min_error = np.min(errors)
        
        print(f"⏱️  测试时长: {total_time:.2f} 秒")
        print(f"📈 预测次数: {len(self.predictions)}")
        print(f"🎯 预测频率: {prediction_rate:.1f} Hz")
        print(f"📊 平均误差: {avg_error:.6f} μm")
        print(f"📊 最大误差: {max_error:.6f} μm")
        print(f"📊 最小误差: {min_error:.6f} μm")
        
        # 显示最后几个预测
        print("\n📋 最后5个预测:")
        for i, pred in enumerate(self.predictions[-5:]):
            print(f"  {i+1}. t={pred['time']:.6f}s, 真实={pred['real']:.6f}μm, "
                  f"预测={pred['pred']:.6f}μm, 误差={pred['error']:.6f}μm")

async def main():
    """主函数"""
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    # 检查DLL文件
    if not os.path.exists(dll_path):
        print(f"❌ 找不到DLL文件: {dll_path}")
        return
    
    # 创建测试实例
    test = SimpleRealTimeTest(dll_path)
    
    # 运行测试
    success = await test.run_test(duration=15)  # 15秒测试
    
    if success:
        print("✅ 实时回调预测测试完成")
    else:
        print("❌ 实时回调预测测试失败")

if __name__ == "__main__":
    print("🧪 DSA实时回调预测测试程序")
    print("验证每次DSA回调都能触发预测")
    asyncio.run(main())
