"""
测试零误差修复效果
验证预测值和真实值是否不再完全相同
"""

import asyncio
import time
import numpy as np
import os
from data_collector_3s import DSADataCollector
from real_time_stream_processor import RealTimeStreamProcessor
from real_time_train import train_displacement_model

class ZeroErrorFixTest:
    """零误差修复测试"""
    
    def __init__(self, dll_path):
        self.dll_path = dll_path
        self.prediction_count = 0
        self.start_time = None
        self.predictions = []
        self.zero_error_count = 0
        self.identical_value_count = 0
        
    def prediction_callback(self, real_value, prediction, timestamp, data_type):
        """预测回调函数"""
        if self.start_time is None:
            self.start_time = timestamp
            print("🚀 开始零误差修复测试...")
            print("📊 检查预测值是否与真实值完全相同")
        
        relative_time = timestamp - self.start_time
        error = abs(real_value - prediction)
        
        # 检查是否为零误差
        is_zero_error = error < 1e-10
        if is_zero_error:
            self.zero_error_count += 1
        
        # 检查预测值是否与真实值完全相同
        is_identical = abs(real_value - prediction) < 1e-15
        if is_identical:
            self.identical_value_count += 1
        
        self.prediction_count += 1
        self.predictions.append({
            'prediction_num': self.prediction_count,
            'relative_time': relative_time,
            'real_value': real_value,
            'prediction': prediction,
            'error': error,
            'is_zero_error': is_zero_error,
            'is_identical': is_identical
        })
        
        # 显示前50个预测的详细信息
        if self.prediction_count <= 50:
            status = "完全相同" if is_identical else ("零误差" if is_zero_error else "正常")
            print(f"   预测#{self.prediction_count}: 真实={real_value:.6f}μm, "
                  f"预测={prediction:.6f}μm, 误差={error:.6f}μm ({status})")
        elif self.prediction_count % 100 == 0:
            # 每100个预测显示统计信息
            zero_ratio = self.zero_error_count / self.prediction_count * 100
            identical_ratio = self.identical_value_count / self.prediction_count * 100
            print(f"📊 预测#{self.prediction_count}: 零误差率={zero_ratio:.1f}%, "
                  f"完全相同率={identical_ratio:.1f}%, 当前误差={error:.6f}μm")
    
    async def run_test(self, duration=15):
        """运行零误差修复测试"""
        print("="*60)
        print("🧪 DSA实时回调预测系统 - 零误差修复测试")
        print("="*60)
        
        # 1. 检查训练数据
        if not os.path.exists('v1.txt'):
            print("❌ 未找到训练数据 v1.txt")
            print("请先运行数据收集程序")
            return False
        
        # 2. 快速训练模型
        print("🚀 快速训练模型...")
        model, scaler = train_displacement_model(
            data_file='v1.txt',
            window_size=25,
            hidden_size=64,
            learning_rate=0.01,
            epochs=50,
            train_points=1000
        )
        
        if model is None or scaler is None:
            print("❌ 模型训练失败")
            return False
        
        print("✅ 模型训练完成")
        
        # 3. 创建预测器
        print("🔧 创建预测器...")
        predictor = RealTimeStreamProcessor(
            model=model,
            scaler=scaler,
            window_size=25,
            enable_akf=True,
            enable_bias_correction=True
        )
        
        # 4. 创建数据收集器
        print("🔧 创建数据收集器...")
        collector = DSADataCollector(self.dll_path)
        collector.enable_continuous_mode()  # 重要：启用连续模式
        collector.enable_real_time_streaming()
        
        # 5. 收集初始化数据
        print("📊 收集初始化数据...")
        if not collector.initialize_sdk():
            print("❌ SDK初始化失败")
            return False
        
        if not collector.start_collection():
            print("❌ 数据收集启动失败")
            return False
        
        # 收集25个初始化数据点
        historical_data = []
        timeout_count = 0
        
        while len(historical_data) < 25 and timeout_count < 50:
            data = collector.get_real_time_data_by_type('displacement', timeout=0.1)
            if data is not None:
                historical_data.append(data['value'])
                if len(historical_data) % 5 == 0:
                    print(f"收集初始化数据: {len(historical_data)}/25")
            else:
                timeout_count += 1
                await asyncio.sleep(0.1)
        
        if len(historical_data) < 25:
            print(f"❌ 初始化数据不足: {len(historical_data)}/25")
            collector.stop_collection()
            return False
        
        # 初始化预测器
        predictor.initialize_with_historical_data(historical_data)
        print("✅ 预测器初始化完成")
        
        # 6. 设置实时预测
        collector.set_real_time_predictor(predictor)
        collector.set_prediction_callback(self.prediction_callback)
        
        print(f"🚀 开始{duration}秒零误差修复测试...")
        print("检查预测值是否与真实值完全相同")
        
        # 7. 等待测试完成
        try:
            await asyncio.sleep(duration)
        except KeyboardInterrupt:
            print("⚠️ 用户中断测试")
        
        # 8. 停止收集
        collector.stop_collection()
        
        # 9. 分析修复效果
        self.analyze_fix_results()
        
        return True
    
    def analyze_fix_results(self):
        """分析修复效果"""
        print("\n" + "="*60)
        print("📊 零误差修复效果分析")
        print("="*60)
        
        if len(self.predictions) == 0:
            print("❌ 没有收集到预测数据")
            return
        
        # 基本统计
        total_predictions = len(self.predictions)
        zero_error_ratio = self.zero_error_count / total_predictions * 100
        identical_ratio = self.identical_value_count / total_predictions * 100
        
        # 误差统计
        errors = [p['error'] for p in self.predictions]
        avg_error = np.mean(errors)
        max_error = np.max(errors)
        min_error = np.min(errors)
        std_error = np.std(errors)
        
        print(f"📈 总预测次数: {total_predictions}")
        print(f"📊 零误差预测数量: {self.zero_error_count}")
        print(f"📊 零误差比例: {zero_error_ratio:.2f}%")
        print(f"📊 完全相同预测数量: {self.identical_value_count}")
        print(f"📊 完全相同比例: {identical_ratio:.2f}%")
        
        print(f"\n📊 误差统计:")
        print(f"   平均误差: {avg_error:.6f} μm")
        print(f"   最大误差: {max_error:.6f} μm")
        print(f"   最小误差: {min_error:.6f} μm")
        print(f"   误差标准差: {std_error:.6f} μm")
        
        # 修复效果评估
        print(f"\n🎯 修复效果评估:")
        
        if identical_ratio > 90:
            print(f"   ❌ 修复失败：{identical_ratio:.1f}%的预测值与真实值完全相同")
            print(f"   问题仍然存在，需要进一步检查代码")
        elif identical_ratio > 50:
            print(f"   ⚠️ 修复部分成功：{identical_ratio:.1f}%的预测值与真实值完全相同")
            print(f"   仍有较多预测值完全相同，需要继续优化")
        elif identical_ratio > 10:
            print(f"   ✅ 修复基本成功：{identical_ratio:.1f}%的预测值与真实值完全相同")
            print(f"   大部分预测已正常，少量相同可能是巧合")
        else:
            print(f"   ✅ 修复完全成功：仅{identical_ratio:.1f}%的预测值与真实值完全相同")
            print(f"   预测系统工作正常")
        
        if avg_error > 0.001:
            print(f"   ✅ 平均误差正常：{avg_error:.6f} μm")
        elif avg_error > 0.0001:
            print(f"   ✅ 平均误差较小但正常：{avg_error:.6f} μm")
        else:
            print(f"   ⚠️ 平均误差过小：{avg_error:.6f} μm，可能仍有问题")
        
        # 显示一些具体的预测示例
        print(f"\n📋 预测示例（前10个）:")
        for i, pred in enumerate(self.predictions[:10]):
            status = "完全相同" if pred['is_identical'] else ("零误差" if pred['is_zero_error'] else "正常")
            print(f"  {i+1:2d}. 真实={pred['real_value']:.6f}μm, "
                  f"预测={pred['prediction']:.6f}μm, "
                  f"误差={pred['error']:.6f}μm ({status})")
        
        if len(self.predictions) > 10:
            print(f"\n📋 预测示例（后10个）:")
            for i, pred in enumerate(self.predictions[-10:], len(self.predictions)-9):
                status = "完全相同" if pred['is_identical'] else ("零误差" if pred['is_zero_error'] else "正常")
                print(f"  {i:2d}. 真实={pred['real_value']:.6f}μm, "
                      f"预测={pred['prediction']:.6f}μm, "
                      f"误差={pred['error']:.6f}μm ({status})")

async def main():
    """主函数"""
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    # 检查DLL文件
    if not os.path.exists(dll_path):
        print(f"❌ 找不到DLL文件: {dll_path}")
        return
    
    # 创建测试实例
    test = ZeroErrorFixTest(dll_path)
    
    # 运行测试
    success = await test.run_test(duration=15)  # 15秒测试
    
    if success:
        print("✅ 零误差修复测试完成")
    else:
        print("❌ 零误差修复测试失败")

if __name__ == "__main__":
    print("🧪 DSA实时回调预测系统 - 零误差修复测试程序")
    print("验证预测值是否与真实值完全相同")
    asyncio.run(main())
