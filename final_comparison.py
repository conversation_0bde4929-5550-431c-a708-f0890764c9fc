#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终对比分析：修复前后的模型性能
"""

import pandas as pd
import numpy as np
import torch
import pickle
from real_time_train import DisplacementLSTM
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

def load_original_model():
    """加载原始模型"""
    try:
        model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
        model.load_state_dict(torch.load('displacement_model.pth'))
        model.eval()
        
        with open('displacement_scaler.pkl', 'rb') as f:
            scaler = pickle.load(f)
        
        return model, scaler
    except:
        return None, None

def load_fixed_model():
    """加载修复后的模型"""
    try:
        model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
        model.load_state_dict(torch.load('displacement_model_fixed.pth'))
        model.eval()
        
        with open('displacement_scaler_fixed.pkl', 'rb') as f:
            scaler = pickle.load(f)
        
        return model, scaler
    except:
        return None, None

def predict_with_model(model, scaler, test_data, window_size=25):
    """使用模型进行预测"""
    predictions = []
    
    for i in range(window_size, len(test_data)):
        # 准备输入序列
        input_seq = test_data[i-window_size:i]
        
        try:
            normalized_seq = scaler.transform(input_seq.reshape(-1, 1)).flatten()
            input_tensor = torch.FloatTensor(normalized_seq).unsqueeze(0).unsqueeze(-1)
            
            # 预测
            with torch.no_grad():
                normalized_pred = model(input_tensor).item()
            
            # 反标准化
            pred = scaler.inverse_transform([[normalized_pred]])[0, 0]
            predictions.append(pred)
            
        except Exception as e:
            # 如果预测失败，使用前一个值
            if predictions:
                predictions.append(predictions[-1])
            else:
                predictions.append(test_data[i-1])
    
    return np.array(predictions)

def calculate_metrics(predictions, actual_values):
    """计算性能指标"""
    mae = np.mean(np.abs(predictions - actual_values))
    rmse = np.sqrt(np.mean((predictions - actual_values)**2))
    bias = np.mean(predictions - actual_values)
    
    # 计算相关系数
    if len(predictions) > 1 and len(actual_values) > 1:
        correlation = np.corrcoef(predictions, actual_values)[0, 1]
    else:
        correlation = 0
    
    # 计算相对误差
    relative_error = np.mean(np.abs(predictions - actual_values) / np.abs(actual_values)) * 100
    
    return {
        'MAE': mae,
        'RMSE': rmse,
        'Bias': bias,
        'Correlation': correlation,
        'Relative_Error': relative_error
    }

def compare_models():
    """对比模型性能"""
    print("=== 模型性能对比分析 ===")
    
    # 加载测试数据
    test_data = pd.read_csv('three_seconds_data.txt', sep='\t', encoding='utf-8')
    test_displacement = test_data['位移[μm]'].values
    
    print(f"测试数据信息:")
    print(f"  数据点数: {len(test_displacement)}")
    print(f"  数据范围: {test_displacement.min():.6f} - {test_displacement.max():.6f} μm")
    print(f"  数据均值: {test_displacement.mean():.6f} μm")
    print(f"  数据标准差: {test_displacement.std():.6f} μm")
    
    window_size = 25
    actual_values = test_displacement[window_size:]
    
    # 1. 测试原始模型
    print(f"\n1. 测试原始模型...")
    original_model, original_scaler = load_original_model()
    
    if original_model is not None and original_scaler is not None:
        print("   ✅ 原始模型加载成功")
        print(f"   标准化器数据范围: {original_scaler.data_min_[0]:.6f} - {original_scaler.data_max_[0]:.6f}")
        
        original_predictions = predict_with_model(original_model, original_scaler, test_displacement, window_size)
        original_metrics = calculate_metrics(original_predictions, actual_values)
        
        print("   原始模型性能:")
        for key, value in original_metrics.items():
            print(f"     {key}: {value:.6f}")
    else:
        print("   ❌ 原始模型加载失败")
        original_predictions = None
        original_metrics = None
    
    # 2. 测试修复后的模型
    print(f"\n2. 测试修复后的模型...")
    fixed_model, fixed_scaler = load_fixed_model()
    
    if fixed_model is not None and fixed_scaler is not None:
        print("   ✅ 修复后模型加载成功")
        print(f"   标准化器数据范围: {fixed_scaler.data_min_[0]:.6f} - {fixed_scaler.data_max_[0]:.6f}")
        
        fixed_predictions = predict_with_model(fixed_model, fixed_scaler, test_displacement, window_size)
        fixed_metrics = calculate_metrics(fixed_predictions, actual_values)
        
        print("   修复后模型性能:")
        for key, value in fixed_metrics.items():
            print(f"     {key}: {value:.6f}")
    else:
        print("   ❌ 修复后模型加载失败")
        fixed_predictions = None
        fixed_metrics = None
    
    # 3. 性能对比
    if original_metrics is not None and fixed_metrics is not None:
        print(f"\n3. 性能改进对比:")
        print(f"{'指标':<15} {'原始模型':<15} {'修复后模型':<15} {'改进幅度':<15}")
        print("-" * 65)
        
        for key in original_metrics.keys():
            original_val = original_metrics[key]
            fixed_val = fixed_metrics[key]
            
            if key == 'Correlation':
                # 相关系数越大越好
                improvement = ((fixed_val - original_val) / abs(original_val)) * 100 if original_val != 0 else 0
            else:
                # 其他指标越小越好
                improvement = ((original_val - fixed_val) / abs(original_val)) * 100 if original_val != 0 else 0
            
            print(f"{key:<15} {original_val:<15.6f} {fixed_val:<15.6f} {improvement:<15.1f}%")
    
    # 4. 绘制对比图
    if original_predictions is not None and fixed_predictions is not None:
        plot_comparison(actual_values, original_predictions, fixed_predictions)
    
    return {
        'actual_values': actual_values,
        'original_predictions': original_predictions,
        'fixed_predictions': fixed_predictions,
        'original_metrics': original_metrics,
        'fixed_metrics': fixed_metrics
    }

def plot_comparison(actual_values, original_predictions, fixed_predictions):
    """绘制对比图"""
    
    time_axis = np.arange(len(actual_values)) * 0.015  # 假设采样间隔约15ms
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 时间序列对比
    axes[0, 0].plot(time_axis, actual_values, 'b-', label='真实位移', linewidth=2, alpha=0.8)
    axes[0, 0].plot(time_axis, original_predictions, 'r--', label='原始模型预测', linewidth=1.5, alpha=0.7)
    axes[0, 0].plot(time_axis, fixed_predictions, 'g:', label='修复后模型预测', linewidth=1.5, alpha=0.7)
    axes[0, 0].set_xlabel('时间 [s]')
    axes[0, 0].set_ylabel('位移 [μm]')
    axes[0, 0].set_title('时间序列对比')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 误差对比
    original_error = original_predictions - actual_values
    fixed_error = fixed_predictions - actual_values
    
    axes[0, 1].plot(time_axis, original_error, 'r-', label='原始模型误差', alpha=0.7)
    axes[0, 1].plot(time_axis, fixed_error, 'g-', label='修复后模型误差', alpha=0.7)
    axes[0, 1].axhline(y=0, color='k', linestyle='--', alpha=0.5)
    axes[0, 1].set_xlabel('时间 [s]')
    axes[0, 1].set_ylabel('预测误差 [μm]')
    axes[0, 1].set_title('误差时间序列对比')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 误差分布对比
    axes[0, 2].hist(original_error, bins=20, alpha=0.5, color='red', label='原始模型', density=True)
    axes[0, 2].hist(fixed_error, bins=20, alpha=0.5, color='green', label='修复后模型', density=True)
    axes[0, 2].set_xlabel('预测误差 [μm]')
    axes[0, 2].set_ylabel('密度')
    axes[0, 2].set_title('误差分布对比')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # 4. 散点图对比 - 原始模型
    axes[1, 0].scatter(actual_values, original_predictions, alpha=0.6, color='red', s=20)
    min_val = min(actual_values.min(), original_predictions.min())
    max_val = max(actual_values.max(), original_predictions.max())
    axes[1, 0].plot([min_val, max_val], [min_val, max_val], 'k--', linewidth=2, label='理想预测线')
    axes[1, 0].set_xlabel('真实位移 [μm]')
    axes[1, 0].set_ylabel('预测位移 [μm]')
    axes[1, 0].set_title('原始模型：预测 vs 真实值')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 5. 散点图对比 - 修复后模型
    axes[1, 1].scatter(actual_values, fixed_predictions, alpha=0.6, color='green', s=20)
    min_val = min(actual_values.min(), fixed_predictions.min())
    max_val = max(actual_values.max(), fixed_predictions.max())
    axes[1, 1].plot([min_val, max_val], [min_val, max_val], 'k--', linewidth=2, label='理想预测线')
    axes[1, 1].set_xlabel('真实位移 [μm]')
    axes[1, 1].set_ylabel('预测位移 [μm]')
    axes[1, 1].set_title('修复后模型：预测 vs 真实值')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].set_aspect('equal', adjustable='box')
    
    # 6. 绝对误差对比
    original_abs_error = np.abs(original_error)
    fixed_abs_error = np.abs(fixed_error)
    
    axes[1, 2].plot(time_axis, original_abs_error, 'r-', label='原始模型', alpha=0.7)
    axes[1, 2].plot(time_axis, fixed_abs_error, 'g-', label='修复后模型', alpha=0.7)
    axes[1, 2].set_xlabel('时间 [s]')
    axes[1, 2].set_ylabel('绝对误差 [μm]')
    axes[1, 2].set_title('绝对误差对比')
    axes[1, 2].legend()
    axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('model_comparison_final.png', dpi=300, bbox_inches='tight')
    print("最终对比图已保存为 model_comparison_final.png")
    plt.close()

def main():
    """主函数"""
    print("模型修复前后性能对比分析")
    print("="*60)
    
    results = compare_models()
    
    print(f"\n=== 总结 ===")
    print("预测误差大的根本原因:")
    print("  🔍 标准化器参数完全错误")
    print("  🔍 原始模型的标准化器使用了错误的数据范围")
    print("  🔍 导致预测值与实际值相差数万倍")
    
    print(f"\n修复措施:")
    print("  ✅ 重新训练模型，确保使用正确的训练数据")
    print("  ✅ 验证标准化器参数的正确性")
    print("  ✅ 测试模型在真实数据上的性能")
    
    if results['fixed_metrics'] is not None:
        print(f"\n修复后的性能:")
        print(f"  📊 MAE: {results['fixed_metrics']['MAE']:.6f} μm")
        print(f"  📊 RMSE: {results['fixed_metrics']['RMSE']:.6f} μm")
        print(f"  📊 偏差: {results['fixed_metrics']['Bias']:.6f} μm")
        print(f"  📊 相关系数: {results['fixed_metrics']['Correlation']:.6f}")
        print(f"  📊 相对误差: {results['fixed_metrics']['Relative_Error']:.3f}%")
    
    print(f"\n建议:")
    print("  1. 使用修复后的模型文件:")
    print("     - displacement_model_fixed.pth")
    print("     - displacement_scaler_fixed.pkl")
    print("  2. 在实时预测系统中替换原始模型")
    print("  3. 定期验证模型性能，确保预测精度")

if __name__ == "__main__":
    main()
