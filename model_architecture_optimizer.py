"""
模型架构优化模块
提供多种LSTM架构和超参数优化功能
"""

import torch
import torch.nn as nn
import numpy as np
from sklearn.preprocessing import MinMaxScaler
import warnings

warnings.filterwarnings("ignore")


class EnhancedLSTM(nn.Module):
    """增强版LSTM模型"""
    
    def __init__(self, input_size=1, hidden_size=128, num_layers=2, output_size=1, 
                 dropout=0.2, bidirectional=False, use_attention=False):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.bidirectional = bidirectional
        self.use_attention = use_attention
        
        # LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=bidirectional
        )
        
        # 计算LSTM输出维度
        lstm_output_size = hidden_size * (2 if bidirectional else 1)
        
        # 注意力机制
        if use_attention:
            self.attention = nn.MultiheadAttention(
                embed_dim=lstm_output_size,
                num_heads=8,
                dropout=dropout,
                batch_first=True
            )
        
        # 全连接层
        self.dropout = nn.Dropout(dropout)
        self.fc1 = nn.Linear(lstm_output_size, hidden_size // 2)
        self.fc2 = nn.Linear(hidden_size // 2, output_size)
        self.relu = nn.ReLU()
        
        # 批标准化
        self.batch_norm = nn.BatchNorm1d(hidden_size // 2)
        
    def forward(self, x):
        # LSTM前向传播
        lstm_out, (h_n, c_n) = self.lstm(x)
        
        # 注意力机制
        if self.use_attention:
            attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
            # 使用最后一个时间步的输出
            out = attn_out[:, -1, :]
        else:
            # 使用最后一个时间步的输出
            out = lstm_out[:, -1, :]
        
        # 全连接层
        out = self.dropout(out)
        out = self.fc1(out)
        out = self.batch_norm(out)
        out = self.relu(out)
        out = self.dropout(out)
        prediction = self.fc2(out)
        
        return prediction


class ResidualLSTM(nn.Module):
    """带残差连接的LSTM模型"""
    
    def __init__(self, input_size=1, hidden_size=128, num_layers=3, output_size=1, dropout=0.2):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # 多层LSTM
        self.lstm_layers = nn.ModuleList()
        for i in range(num_layers):
            layer_input_size = input_size if i == 0 else hidden_size
            self.lstm_layers.append(
                nn.LSTM(layer_input_size, hidden_size, batch_first=True, dropout=0)
            )
        
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(hidden_size)
        
        # 输出层
        self.fc = nn.Linear(hidden_size, output_size)
        
    def forward(self, x):
        out = x
        
        for i, lstm_layer in enumerate(self.lstm_layers):
            lstm_out, _ = lstm_layer(out)
            
            # 残差连接（从第二层开始）
            if i > 0 and out.size(-1) == lstm_out.size(-1):
                lstm_out = lstm_out + out
            
            # 层标准化和dropout
            lstm_out = self.layer_norm(lstm_out)
            out = self.dropout(lstm_out)
        
        # 使用最后一个时间步
        prediction = self.fc(out[:, -1, :])
        return prediction


class ConvLSTM(nn.Module):
    """卷积LSTM模型"""
    
    def __init__(self, input_size=1, hidden_size=128, output_size=1, dropout=0.2):
        super().__init__()
        
        # 1D卷积层用于特征提取
        self.conv1 = nn.Conv1d(input_size, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv1d(32, 64, kernel_size=3, padding=1)
        self.pool = nn.MaxPool1d(2)
        
        # LSTM层
        self.lstm = nn.LSTM(64, hidden_size, batch_first=True, dropout=dropout)
        
        # 输出层
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_size, output_size)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        # 调整维度用于卷积 (batch, features, sequence)
        x = x.transpose(1, 2)
        
        # 卷积特征提取
        x = self.relu(self.conv1(x))
        x = self.relu(self.conv2(x))
        
        # 调整回LSTM格式 (batch, sequence, features)
        x = x.transpose(1, 2)
        
        # LSTM处理
        lstm_out, _ = self.lstm(x)
        
        # 输出
        out = self.dropout(lstm_out[:, -1, :])
        prediction = self.fc(out)
        
        return prediction


class ModelArchitectureOptimizer:
    """模型架构优化器"""
    
    def __init__(self):
        self.model_configs = {
            'basic_lstm': {
                'class': EnhancedLSTM,
                'params': {
                    'hidden_size': 64,
                    'num_layers': 1,
                    'dropout': 0.1,
                    'bidirectional': False,
                    'use_attention': False
                }
            },
            'enhanced_lstm': {
                'class': EnhancedLSTM,
                'params': {
                    'hidden_size': 128,
                    'num_layers': 2,
                    'dropout': 0.2,
                    'bidirectional': False,
                    'use_attention': False
                }
            },
            'bidirectional_lstm': {
                'class': EnhancedLSTM,
                'params': {
                    'hidden_size': 96,
                    'num_layers': 2,
                    'dropout': 0.2,
                    'bidirectional': True,
                    'use_attention': False
                }
            },
            'attention_lstm': {
                'class': EnhancedLSTM,
                'params': {
                    'hidden_size': 128,
                    'num_layers': 2,
                    'dropout': 0.2,
                    'bidirectional': False,
                    'use_attention': True
                }
            },
            'residual_lstm': {
                'class': ResidualLSTM,
                'params': {
                    'hidden_size': 128,
                    'num_layers': 3,
                    'dropout': 0.2
                }
            },
            'conv_lstm': {
                'class': ConvLSTM,
                'params': {
                    'hidden_size': 128,
                    'dropout': 0.2
                }
            }
        }
    
    def create_model(self, model_type='enhanced_lstm', input_size=1, output_size=1):
        """创建指定类型的模型"""
        if model_type not in self.model_configs:
            raise ValueError(f"未知的模型类型: {model_type}")
        
        config = self.model_configs[model_type]
        model_class = config['class']
        params = config['params'].copy()
        params['input_size'] = input_size
        params['output_size'] = output_size
        
        model = model_class(**params)
        return model
    
    def get_model_info(self, model):
        """获取模型信息"""
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        info = {
            'model_type': model.__class__.__name__,
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'model_size_mb': total_params * 4 / (1024 * 1024)  # 假设float32
        }
        
        return info
    
    def optimize_hyperparameters(self, X, y, model_type='enhanced_lstm', 
                                search_space=None, max_trials=10):
        """
        超参数优化
        
        Args:
            X: 训练数据
            y: 目标数据
            model_type: 模型类型
            search_space: 搜索空间
            max_trials: 最大试验次数
        
        Returns:
            dict: 最佳超参数和性能
        """
        if search_space is None:
            search_space = {
                'hidden_size': [64, 96, 128, 192, 256],
                'num_layers': [1, 2, 3],
                'dropout': [0.1, 0.2, 0.3],
                'learning_rate': [0.001, 0.005, 0.01, 0.02]
            }
        
        best_loss = float('inf')
        best_params = None
        results = []
        
        print(f"开始超参数优化，模型类型: {model_type}")
        print(f"搜索空间: {search_space}")
        print(f"最大试验次数: {max_trials}")
        
        for trial in range(max_trials):
            # 随机采样超参数
            params = {}
            for key, values in search_space.items():
                params[key] = np.random.choice(values)
            
            try:
                # 创建模型
                model_params = self.model_configs[model_type]['params'].copy()
                model_params.update({k: v for k, v in params.items() if k != 'learning_rate'})
                
                model = self.model_configs[model_type]['class'](
                    input_size=X.shape[-1],
                    output_size=y.shape[-1] if len(y.shape) > 1 else 1,
                    **model_params
                )
                
                # 训练模型
                loss = self._train_and_evaluate(model, X, y, params['learning_rate'])
                
                results.append({
                    'trial': trial + 1,
                    'params': params,
                    'loss': loss
                })
                
                if loss < best_loss:
                    best_loss = loss
                    best_params = params
                
                print(f"试验 {trial + 1}: 损失 = {loss:.6f}, 参数 = {params}")
                
            except Exception as e:
                print(f"试验 {trial + 1} 失败: {e}")
                continue
        
        print(f"\n最佳参数: {best_params}")
        print(f"最佳损失: {best_loss:.6f}")
        
        return {
            'best_params': best_params,
            'best_loss': best_loss,
            'all_results': results
        }
    
    def _train_and_evaluate(self, model, X, y, learning_rate, epochs=50):
        """训练和评估模型"""
        criterion = nn.MSELoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
        
        model.train()
        for epoch in range(epochs):
            optimizer.zero_grad()
            outputs = model(X).squeeze()
            loss = criterion(outputs, y)
            loss.backward()
            optimizer.step()
        
        model.eval()
        with torch.no_grad():
            outputs = model(X).squeeze()
            final_loss = criterion(outputs, y).item()
        
        return final_loss


    def compare_model_architectures(self, X, y, architectures=['basic_lstm', 'enhanced_lstm', 'attention_lstm']):
        """
        比较不同模型架构的性能

        Args:
            X: 训练数据
            y: 目标数据
            architectures: 要比较的架构列表

        Returns:
            dict: 比较结果
        """
        results = {}

        print("=" * 60)
        print("模型架构性能比较")
        print("=" * 60)

        for arch in architectures:
            print(f"\n测试架构: {arch}")
            try:
                # 创建模型
                model = self.create_model(arch, input_size=X.shape[-1])

                # 获取模型信息
                info = self.get_model_info(model)
                print(f"  参数数量: {info['total_parameters']:,}")
                print(f"  模型大小: {info['model_size_mb']:.2f} MB")

                # 训练和评估
                loss = self._train_and_evaluate(model, X, y, learning_rate=0.01)

                results[arch] = {
                    'loss': loss,
                    'model_info': info
                }

                print(f"  最终损失: {loss:.6f}")

            except Exception as e:
                print(f"  ❌ 测试失败: {e}")
                results[arch] = {'error': str(e)}

        # 找出最佳架构
        valid_results = {k: v for k, v in results.items() if 'loss' in v}
        if valid_results:
            best_arch = min(valid_results.keys(), key=lambda k: valid_results[k]['loss'])
            print(f"\n🏆 最佳架构: {best_arch}")
            print(f"   损失: {valid_results[best_arch]['loss']:.6f}")

        return results


def compare_model_architectures(X, y, architectures=['basic_lstm', 'enhanced_lstm', 'attention_lstm']):
    """
    比较不同模型架构的性能（独立函数版本）

    Args:
        X: 训练数据
        y: 目标数据
        architectures: 要比较的架构列表

    Returns:
        dict: 比较结果
    """
    optimizer = ModelArchitectureOptimizer()
    return optimizer.compare_model_architectures(X, y, architectures)


if __name__ == "__main__":
    # 测试模型架构优化
    print("模型架构优化模块测试")
    
    # 生成测试数据
    X = torch.randn(100, 25, 1)  # (batch_size, sequence_length, input_size)
    y = torch.randn(100)         # (batch_size,)
    
    # 比较不同架构
    compare_model_architectures(X, y)
