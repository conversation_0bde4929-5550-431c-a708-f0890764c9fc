"""
快速绘制过滤后的位移预测数据
专门用于分析 real_time_predictions_*.csv 文件
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def quick_plot_filtered_data(filename, time_threshold=0.0001):
    """快速绘制过滤后的数据"""
    print(f"📊 快速分析文件: {filename}")
    print(f"🔍 时间过滤阈值: {time_threshold} 秒")
    
    # 读取数据
    try:
        df = pd.read_csv(filename)
        print(f"✅ 成功加载 {len(df)} 行数据")
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return
    
    # 检查列数和数据格式
    print(f"📋 数据列数: {len(df.columns)}")
    print(f"📋 列名: {list(df.columns)}")
    print(f"📋 前5行数据:")
    print(df.head())

    # 根据列数判断数据格式
    if len(df.columns) == 6:
        # 格式: time, real_value, prediction, error, timestamp, data_type
        df.columns = ['time', 'real_value', 'prediction', 'error', 'timestamp', 'data_type']
    elif len(df.columns) == 5:
        # 格式: time, real_value, prediction, error, timestamp
        df.columns = ['time', 'real_value', 'prediction', 'error', 'timestamp']
    elif len(df.columns) == 4:
        # 格式: time, real_value, prediction, error
        df.columns = ['time', 'real_value', 'prediction', 'error']
    else:
        print(f"❌ 不支持的数据格式，列数: {len(df.columns)}")
        return

    # 数据质量检查和处理
    print(f"📊 数据质量检查:")
    duplicate_times = df['time'].duplicated().sum()
    print(f"   重复时间戳: {duplicate_times} 个")

    if duplicate_times > 0:
        print("⚠️ 发现重复时间戳，正在处理...")
        # 排序并重置索引
        df = df.sort_values('time').reset_index(drop=True)

        # 为重复的时间戳添加微小偏移
        for i in range(1, len(df)):
            if df.loc[i, 'time'] <= df.loc[i-1, 'time']:
                df.loc[i, 'time'] = df.loc[i-1, 'time'] + 1e-8

        print(f"   ✅ 已处理重复时间戳")

    # 时间过滤
    original_count = len(df)
    df_filtered = df[df['time'] > time_threshold].copy()
    filtered_count = len(df_filtered)
    
    print(f"\n🔍 时间过滤结果:")
    print(f"   原始数据: {original_count} 行")
    print(f"   过滤后: {filtered_count} 行")
    print(f"   移除: {original_count - filtered_count} 行 ({(original_count - filtered_count)/original_count*100:.1f}%)")
    
    if filtered_count == 0:
        print("❌ 过滤后没有数据！")
        return
    
    # 重置索引
    df_filtered = df_filtered.reset_index(drop=True)

    # 重置时间
    time_offset = df_filtered['time'].min()
    df_filtered['time'] = df_filtered['time'] - time_offset

    # 重新计算误差（确保一致性）
    df_filtered['error'] = df_filtered['prediction'] - df_filtered['real_value']
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'位移预测分析 (过滤阈值: {time_threshold}s)', fontsize=16, fontweight='bold')
    
    times = df_filtered['time'].values
    real_values = df_filtered['real_value'].values
    predictions = df_filtered['prediction'].values
    errors = df_filtered['error'].values
    
    # 1. 位移对比图
    ax1 = axes[0, 0]
    ax1.plot(times, real_values, 'b-', linewidth=1.5, label='实际位移', alpha=0.8)
    ax1.plot(times, predictions, 'r-', linewidth=1.5, label='预测位移', alpha=0.8)
    ax1.set_title('实际位移 vs 预测位移')
    ax1.set_xlabel('时间 (秒)')
    ax1.set_ylabel('位移 (μm)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 添加统计信息
    mae = np.mean(np.abs(errors))
    rmse = np.sqrt(np.mean(errors**2))
    correlation = np.corrcoef(real_values, predictions)[0, 1]
    
    stats_text = f'MAE: {mae:.6f}μm\nRMSE: {rmse:.6f}μm\n相关系数: {correlation:.6f}\n数据点: {len(df_filtered)}'
    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 2. 误差时间序列
    ax2 = axes[0, 1]
    ax2.plot(times, errors, 'g-', linewidth=1, alpha=0.7, label='预测误差')
    ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax2.axhline(y=np.mean(errors), color='red', linestyle='--', alpha=0.7, 
                label=f'平均误差: {np.mean(errors):.6f}μm')
    ax2.fill_between(times, errors, 0, alpha=0.3, color='green')
    ax2.set_title('预测误差随时间变化')
    ax2.set_xlabel('时间 (秒)')
    ax2.set_ylabel('误差 (μm)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 误差分布直方图
    ax3 = axes[1, 0]
    n, bins, patches = ax3.hist(errors, bins=50, alpha=0.7, color='orange', edgecolor='black', density=True)
    ax3.axvline(x=np.mean(errors), color='red', linestyle='--', linewidth=2, 
                label=f'平均: {np.mean(errors):.6f}μm')
    ax3.axvline(x=np.median(errors), color='blue', linestyle='--', linewidth=2, 
                label=f'中位数: {np.median(errors):.6f}μm')
    ax3.axvline(x=0, color='black', linestyle='-', alpha=0.5)
    ax3.set_title('误差分布直方图')
    ax3.set_xlabel('误差 (μm)')
    ax3.set_ylabel('概率密度')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 散点图 (预测 vs 实际)
    ax4 = axes[1, 1]
    scatter = ax4.scatter(real_values, predictions, alpha=0.6, s=10, c=np.abs(errors), cmap='viridis')
    
    # 添加理想预测线 (y=x)
    min_val = min(np.min(real_values), np.min(predictions))
    max_val = max(np.max(real_values), np.max(predictions))
    ax4.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='理想预测线')
    
    ax4.set_title('预测值 vs 实际值散点图')
    ax4.set_xlabel('实际位移 (μm)')
    ax4.set_ylabel('预测位移 (μm)')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 添加颜色条
    cbar = plt.colorbar(scatter, ax=ax4)
    cbar.set_label('绝对误差 (μm)')
    
    plt.tight_layout()
    
    # 保存图表
    base_name = os.path.splitext(filename)[0]
    plot_filename = f"{base_name}_filtered_{time_threshold}s.png"
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"📊 过滤后分析图已保存到: {plot_filename}")
    
    # 打印详细统计
    print(f"\n📊 过滤后数据统计:")
    print(f"   时间范围: {times.min():.6f} - {times.max():.6f} 秒")
    print(f"   数据时长: {times.max() - times.min():.6f} 秒")
    print(f"   采样频率: {len(df_filtered)/(times.max() - times.min()):.1f} Hz")
    print(f"   位移范围: {real_values.min():.6f} - {real_values.max():.6f} μm")
    print(f"   位移变化: {real_values.max() - real_values.min():.6f} μm")
    print(f"   平均绝对误差: {mae:.6f} μm")
    print(f"   均方根误差: {rmse:.6f} μm")
    print(f"   误差标准差: {np.std(errors):.6f} μm")
    print(f"   预测-实际相关系数: {correlation:.6f}")
    
    # 误差百分位数
    percentiles = [5, 25, 50, 75, 95]
    print(f"\n📊 误差百分位数:")
    for p in percentiles:
        value = np.percentile(np.abs(errors), p)
        print(f"   {p}%: {value:.6f} μm")
    
    # 保存过滤后的数据
    filtered_filename = f"{base_name}_filtered_{time_threshold}s.csv"
    df_filtered.to_csv(filtered_filename, index=False)
    print(f"💾 过滤后数据已保存到: {filtered_filename}")
    
    plt.show()
    
    return df_filtered

def main():
    """主函数"""
    print("📊 快速位移预测数据分析工具")
    print("="*50)
    
    # 查找CSV文件
    filename = "real_time_predictions_1762246628.csv"
    
    # 检查文件是否存在
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        
        # 尝试查找其他文件
        import glob
        csv_files = glob.glob("real_time_predictions_*.csv")
        if csv_files:
            csv_files.sort(key=os.path.getmtime, reverse=True)
            filename = csv_files[0]
            print(f"🔍 找到最新文件: {filename}")
        else:
            print("❌ 未找到任何 real_time_predictions_*.csv 文件")
            return
    
    # 获取时间阈值
    time_threshold_input = input("请输入时间过滤阈值（秒，默认0.0001）: ").strip()
    try:
        time_threshold = float(time_threshold_input) if time_threshold_input else 0.0001
    except ValueError:
        time_threshold = 0.0001
        print(f"⚠️ 输入无效，使用默认值: {time_threshold} 秒")
    
    # 分析数据
    df_filtered = quick_plot_filtered_data(filename, time_threshold)
    
    if df_filtered is not None:
        print("✅ 分析完成！")
    else:
        print("❌ 分析失败！")

if __name__ == "__main__":
    main()
