import ctypes
from ctypes import *
import time
import socket
import subprocess
import os

class DSANet:
    def __init__(self, dll_path):
        self.sdk = ctypes.cdll.LoadLibrary(dll_path)
        self.CALLBACK = CFUNCTYPE(None, c_int, c_int, c_int, c_int, POINTER(c_float), c_int)
        self._callback_func = self.CALLBACK(self._data_callback)
        self.sdk.setDataCallBack(self._callback_func)

    def _data_callback(self, dataType, sRate, vRange, dRange, data_ptr, dataLen):
        array_type = c_float * dataLen
        data = cast(data_ptr, POINTER(array_type)).contents
        values = list(data)
        print(f"[{dataType}] 位移数据共 {dataLen} 点，前5项: {values[:5]}")

    def check_port(self, port):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0

    def get_error_description(self, error_code):
        """获取错误码的描述"""
        error_descriptions = {
            0: "成功",
            10013: "网络权限被拒绝 - 可能是端口被占用或防火墙阻止",
            10048: "地址已在使用中 - 端口被占用",
            10049: "无法分配请求的地址",
            10054: "连接被对方重置",
            10060: "连接超时",
            10061: "连接被拒绝"
        }
        return error_descriptions.get(error_code, f"未知错误码: {error_code}")

    def check_port_usage(self, port):
        """检查端口占用情况"""
        try:
            result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            for line in lines:
                if f':{port}' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        print(f"端口 {port} 被进程 PID {pid} 占用")
                        return True
        except:
            pass
        return False

    def initialize(self):
        result = self.sdk.initialize()
        print(f"初始化结果: {result}")
        if result != 0:
            error_code = self.sdk.getLastErrCode()
            error_desc = self.get_error_description(error_code)
            print(f"初始化失败，错误码: {error_code} - {error_desc}")
            return False

        # 尝试配置网络端口
        if not self.try_different_ports():
            print("警告: 所有端口都被占用，可能会导致启动失败")
            # 使用默认端口
            self.sdk.setBindInfo(b"127.0.0.1", 62618)
            self.sdk.setBoardcastInfo(b"127.0.0.1", 63082)

        return True

    def try_different_ports(self):
        """尝试不同的端口配置"""
        port_configs = [
            (62618, 63082),  # 默认端口
            (62619, 63083),  # 备用端口1
            (62620, 63084),  # 备用端口2
        ]

        for bind_port, broadcast_port in port_configs:
            print(f"尝试端口配置: 绑定端口={bind_port}, 广播端口={broadcast_port}")
            if not self.check_port(bind_port) and not self.check_port(broadcast_port):
                self.sdk.setBindInfo(b"127.0.0.1", bind_port)
                self.sdk.setBoardcastInfo(b"127.0.0.1", broadcast_port)
                return True
            else:
                print(f"端口 {bind_port} 或 {broadcast_port} 被占用")
        return False

    def configure_measurement(self):
        # 可能需要设置为模拟模式
        try:
            self.sdk.setSimulationMode(1)  # 如果有这个函数
        except:
            pass

        self.sdk.setOutputFilter(1)
        self.sdk.setVelocityRange(1)
        self.sdk.setDisplacementRange(1)
        self.sdk.setOutDataType(0)
        time.sleep(0.05)
        self.sdk.setOutDataType(1)

    def start(self):
        result = self.sdk.start()
        print(f"启动结果: {result}")
        if result != 0:
            error_code = self.sdk.getLastErrCode()
            error_desc = self.get_error_description(error_code)
            print(f"启动失败，错误码: {error_code} - {error_desc}")

            # 如果是网络权限错误，提供解决建议
            if error_code == 10013:
                print("\n检查端口占用情况:")
                self.check_port_usage(62618)
                self.check_port_usage(63082)

                print("\n可能的解决方案:")
                print("1. 以管理员身份运行程序")
                print("2. 检查防火墙设置，允许Python访问网络")
                print("3. 确保没有其他程序占用相关端口")
                print("4. 尝试重启程序或重启计算机")
                print("5. 如果有其他DSA程序在运行，请先关闭它们")

            return False
        return True

    def stop(self):
        self.sdk.stop()
        self.sdk.unInitialize()

# ========== 主程序 ==========
if __name__ == "__main__":
    sdk_path = r"x64\DSANetSDK.dll"  # 使用相对路径
    dsa = DSANet(sdk_path)

    # 初始化
    if not dsa.initialize():
        print("初始化失败，程序退出")
        exit(1)

    # 配置测量参数
    dsa.configure_measurement()

    # 启动测量
    if not dsa.start():
        print("启动失败，程序退出")
        dsa.stop()
        exit(1)

    print("正在接收实时位移数据，按 Ctrl+C 停止...")
    try:
        while True:
            time.sleep(0.5)
    except KeyboardInterrupt:
        print("\n结束测量")
        dsa.stop()
