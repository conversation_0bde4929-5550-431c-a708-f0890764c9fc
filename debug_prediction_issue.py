"""
调试预测停止问题
"""

import asyncio
import os
import sys
import time

# 添加当前目录到路径
sys.path.insert(0, '.')

from real_time_callback_prediction import RealTimePredictionSystem

class DebugPredictionSystem(RealTimePredictionSystem):
    """带调试功能的预测系统"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.debug_prediction_count = 0
        self.last_prediction_time = None
        self.prediction_intervals = []
    
    def prediction_callback(self, real_value, prediction, timestamp, data_type):
        """调试版预测回调函数"""
        current_time = time.time()
        
        # 记录预测间隔
        if self.last_prediction_time is not None:
            interval = current_time - self.last_prediction_time
            self.prediction_intervals.append(interval)
        
        self.last_prediction_time = current_time
        self.debug_prediction_count += 1
        
        # 调用原始回调
        super().prediction_callback(real_value, prediction, timestamp, data_type)
        
        # 每1000次预测输出调试信息
        if self.debug_prediction_count % 1000 == 0:
            if self.prediction_intervals:
                avg_interval = sum(self.prediction_intervals[-100:]) / min(100, len(self.prediction_intervals))
                freq = 1.0 / avg_interval if avg_interval > 0 else 0
                print(f"🔍 调试信息 - 预测#{self.debug_prediction_count}: 平均间隔={avg_interval:.6f}s, 频率={freq:.1f}Hz")

async def debug_prediction_test():
    """调试预测测试"""
    print("🔍 调试预测停止问题")
    print("="*60)
    
    # DLL 路径
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    # 检查DLL文件
    if not os.path.exists(dll_path):
        print(f"❌ 找不到DLL文件: {dll_path}")
        return False
    
    # 创建调试版预测系统
    system = DebugPredictionSystem(
        dll_path=dll_path,
        model_path="displacement_model.pth",
        scaler_path="displacement_scaler.pkl"
    )
    
    print("🚀 开始调试测试...")
    print("📊 测试目标:")
    print("   - 运行5秒短期测试")
    print("   - 监控预测频率变化")
    print("   - 检测预测停止时刻")
    print("-" * 60)
    
    # 记录开始时间
    start_time = time.time()
    
    # 启动5秒调试测试
    success = await system.start_real_time_prediction(duration_seconds=5)
    
    # 记录结束时间
    end_time = time.time()
    actual_duration = end_time - start_time
    
    if success:
        print("\n" + "="*60)
        print("🔍 调试测试结果")
        print("="*60)
        
        print(f"⏱️  实际运行时间: {actual_duration:.2f} 秒")
        print(f"📈 总预测次数: {system.debug_prediction_count}")
        print(f"📊 收集的数据点: {len(system.real_time_data)}")
        
        if system.prediction_intervals:
            intervals = system.prediction_intervals
            print(f"📊 预测间隔统计:")
            print(f"   最小间隔: {min(intervals):.6f}s")
            print(f"   最大间隔: {max(intervals):.6f}s")
            print(f"   平均间隔: {sum(intervals)/len(intervals):.6f}s")
            print(f"   平均频率: {len(intervals)/sum(intervals):.1f}Hz")
            
            # 检查是否有异常大的间隔
            large_intervals = [i for i in intervals if i > 0.1]  # 大于100ms的间隔
            if large_intervals:
                print(f"⚠️ 发现 {len(large_intervals)} 个异常大间隔 (>100ms)")
                print(f"   最大异常间隔: {max(large_intervals):.3f}s")
            
            # 分析预测停止时刻
            if len(intervals) > 10:
                last_10_intervals = intervals[-10:]
                first_10_intervals = intervals[:10]
                
                avg_last = sum(last_10_intervals) / len(last_10_intervals)
                avg_first = sum(first_10_intervals) / len(first_10_intervals)
                
                print(f"📊 间隔变化分析:")
                print(f"   前10次平均间隔: {avg_first:.6f}s")
                print(f"   后10次平均间隔: {avg_last:.6f}s")
                
                if avg_last > avg_first * 2:
                    print("⚠️ 预测频率明显下降")
                elif avg_last < avg_first * 0.5:
                    print("✅ 预测频率保持稳定或提升")
                else:
                    print("✅ 预测频率基本稳定")
        
        # 分析预测数据的时间分布
        if system.real_time_data:
            times = [d['time'] for d in system.real_time_data]
            print(f"📊 预测时间分布:")
            print(f"   第一次预测: {min(times):.3f}s")
            print(f"   最后一次预测: {max(times):.3f}s")
            print(f"   预测时间跨度: {max(times) - min(times):.3f}s")
            
            # 检查预测是否在某个时刻停止
            if max(times) < actual_duration * 0.8:
                print(f"⚠️ 预测可能在 {max(times):.3f}s 时停止")
                print(f"   但数据收集持续到 {actual_duration:.3f}s")
            else:
                print("✅ 预测持续到接近结束时刻")
        
        return True
    else:
        print("❌ 调试测试失败")
        return False

def main():
    """主函数"""
    print("🎯 DSA预测停止问题调试")
    print("目标: 找出预测在30秒内停止的原因")
    print("="*60)
    
    try:
        result = asyncio.run(debug_prediction_test())
        
        if result:
            print("\n🎉 调试测试完成！")
            print("📋 请检查上述调试信息，找出预测停止的原因")
        else:
            print("\n❌ 调试测试失败")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 调试测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
