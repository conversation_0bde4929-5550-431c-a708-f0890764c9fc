"""
验证30秒预测时长修改
"""

import os
import re

def check_duration_in_file(filepath, target_duration=30):
    """检查文件中的时长设置"""
    if not os.path.exists(filepath):
        return f"❌ 文件不存在: {filepath}"
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        results = []
        
        # 检查 duration_seconds 参数
        duration_matches = re.findall(r'duration_seconds\s*=\s*(\d+)', content)
        if duration_matches:
            for match in duration_matches:
                if match == str(target_duration):
                    results.append(f"✅ duration_seconds={match}")
                else:
                    results.append(f"⚠️ duration_seconds={match} (不是{target_duration}秒)")
        
        # 检查 duration= 参数
        duration_eq_matches = re.findall(r'duration\s*=\s*(\d+)', content)
        if duration_eq_matches:
            for match in duration_eq_matches:
                if match == str(target_duration):
                    results.append(f"✅ duration={match}")
                else:
                    results.append(f"⚠️ duration={match} (不是{target_duration}秒)")
        
        # 检查默认值提示
        default_matches = re.findall(r'默认(\d+)', content)
        if default_matches:
            for match in default_matches:
                if match == str(target_duration):
                    results.append(f"✅ 默认值={match}")
                else:
                    results.append(f"⚠️ 默认值={match} (不是{target_duration}秒)")
        
        if not results:
            return f"ℹ️ 未找到时长设置"
        
        return results
        
    except Exception as e:
        return f"❌ 读取文件失败: {e}"

def main():
    """主函数"""
    print("🔍 验证30秒预测时长修改")
    print("="*60)
    
    # 需要检查的文件列表
    files_to_check = [
        "start_real_time_prediction.py",
        "real_time_callback_prediction.py",
        "advanced_real_time_prediction.py", 
        "run_prediction_and_plot.py"
    ]
    
    all_correct = True
    
    for filepath in files_to_check:
        print(f"\n📄 检查文件: {filepath}")
        print("-" * 50)
        
        results = check_duration_in_file(filepath, 30)
        if isinstance(results, list):
            for result in results:
                print(f"   {result}")
                if "⚠️" in result:
                    all_correct = False
        else:
            print(f"   {results}")
            if "❌" in results:
                all_correct = False
    
    print("\n" + "="*60)
    if all_correct:
        print("🎉 所有文件都已正确设置为30秒！")
        print("✅ 修改验证通过")
        
        print("\n📋 修改总结:")
        print("   • start_real_time_prediction.py - 标准预测: 30秒")
        print("   • start_real_time_prediction.py - 高级预测: 30秒") 
        print("   • start_real_time_prediction.py - 快速测试: 30秒")
        print("   • real_time_callback_prediction.py - 主程序: 30秒")
        print("   • advanced_real_time_prediction.py - 高级程序: 30秒")
        print("   • run_prediction_and_plot.py - 默认值: 30秒")
        
        print("\n🚀 现在可以运行30秒预测测试:")
        print("   python start_real_time_prediction.py")
        print("   选择选项2进行标准实时预测")
        
    else:
        print("⚠️ 部分文件可能需要手动检查")
        print("❌ 修改验证未完全通过")

if __name__ == "__main__":
    main()
