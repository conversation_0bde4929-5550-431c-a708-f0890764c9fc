"""
测试缓冲区大小修复效果
"""

import asyncio
import os
import sys
import time

# 添加当前目录到路径
sys.path.insert(0, '.')

from real_time_callback_prediction import RealTimePredictionSystem

async def test_buffer_fix():
    """测试缓冲区大小修复效果"""
    print("🧪 测试缓冲区大小修复效果")
    print("="*60)
    
    # DLL 路径
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    # 检查DLL文件
    if not os.path.exists(dll_path):
        print(f"❌ 找不到DLL文件: {dll_path}")
        return False
    
    # 创建实时预测系统
    system = RealTimePredictionSystem(
        dll_path=dll_path,
        model_path="displacement_model.pth",
        scaler_path="displacement_scaler.pkl"
    )
    
    print("🚀 开始缓冲区修复测试...")
    print("📊 测试目标:")
    print("   - 运行10秒测试")
    print("   - 验证预测能否持续整个期间")
    print("   - 检查预测次数是否超过1000次")
    print("   - 预期预测次数: ~10,000次")
    print("-" * 60)
    
    # 记录开始时间
    start_time = time.time()
    
    # 启动10秒测试
    success = await system.start_real_time_prediction(duration_seconds=10)
    
    # 记录结束时间
    end_time = time.time()
    actual_duration = end_time - start_time
    
    if success:
        print("\n" + "="*60)
        print("📊 缓冲区修复测试结果")
        print("="*60)
        
        # 分析预测数据
        if hasattr(system, 'real_time_data') and system.real_time_data:
            prediction_count = len(system.real_time_data)
            actual_frequency = prediction_count / actual_duration
            
            print(f"⏱️  实际运行时间: {actual_duration:.2f} 秒")
            print(f"📈 实际预测次数: {prediction_count}")
            print(f"🎯 实际预测频率: {actual_frequency:.1f} Hz")
            
            # 计算理论值
            theoretical_predictions = 10 * 1000  # 10秒 × 1000Hz
            frequency_ratio = actual_frequency / 1000 * 100
            
            print(f"📊 理论预测次数: {theoretical_predictions}")
            print(f"📊 频率达成率: {frequency_ratio:.1f}%")
            
            # 判断修复效果
            if prediction_count > 1000:
                print("✅ 成功突破1000次预测限制")
            else:
                print("⚠️ 仍然受到1000次预测限制")
            
            if prediction_count > 5000:
                print("✅ 预测次数达到预期范围")
            else:
                print("⚠️ 预测次数仍然偏低")
            
            # 检查预测时间分布
            times = [d['time'] for d in system.real_time_data]
            print(f"\n📊 预测时间分布:")
            print(f"   第一次预测: {min(times):.3f}s")
            print(f"   最后一次预测: {max(times):.3f}s")
            print(f"   预测时间跨度: {max(times) - min(times):.3f}s")
            
            # 检查预测是否持续到结束
            if max(times) > actual_duration * 0.8:
                print("✅ 预测持续到接近结束时刻")
                print("✅ 缓冲区大小修复成功")
            else:
                print(f"⚠️ 预测在 {max(times):.3f}s 时停止")
                print(f"   但测试持续到 {actual_duration:.3f}s")
                print("❌ 缓冲区大小修复可能不完全")
            
            # 分析预测质量
            if prediction_count > 0:
                errors = [d['error'] for d in system.real_time_data]
                avg_error = sum(errors) / len(errors)
                max_error = max(errors)
                min_error = min(errors)
                
                print(f"\n📊 预测质量分析:")
                print(f"   平均误差: {avg_error:.6f} μm")
                print(f"   最大误差: {max_error:.6f} μm")
                print(f"   最小误差: {min_error:.6f} μm")
                
                # 检查误差稳定性
                if len(errors) > 100:
                    early_errors = errors[:100]
                    late_errors = errors[-100:]
                    early_avg = sum(early_errors) / len(early_errors)
                    late_avg = sum(late_errors) / len(late_errors)
                    
                    print(f"   前100次平均误差: {early_avg:.6f} μm")
                    print(f"   后100次平均误差: {late_avg:.6f} μm")
                    
                    if abs(early_avg - late_avg) / early_avg < 0.2:
                        print("✅ 预测质量保持稳定")
                    else:
                        print("⚠️ 预测质量有变化")
        else:
            print("❌ 未收集到预测数据")
            return False
        
        print("\n✅ 缓冲区修复测试完成")
        return True
    else:
        print("❌ 缓冲区修复测试失败")
        return False

def main():
    """主函数"""
    print("🎯 DSA缓冲区大小修复测试")
    print("测试目标: 验证将deque(maxlen=1000)改为deque(maxlen=100000)的效果")
    print("="*60)
    
    try:
        result = asyncio.run(test_buffer_fix())
        
        if result:
            print("\n🎉 缓冲区修复测试通过！")
            print("📋 修复效果:")
            print("   ✅ 预测次数不再受1000次限制")
            print("   ✅ 预测能够持续整个测试期间")
            print("   ✅ 预测质量保持稳定")
            print("\n🚀 现在可以进行长时间预测测试:")
            print("   python test_30s_prediction.py")
        else:
            print("\n❌ 缓冲区修复测试失败")
            print("可能需要进一步调查其他限制因素")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
