"""
简单健壮的位移预测数据绘图工具
专门处理重复时间戳和数据质量问题
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import glob

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def robust_load_and_filter(filename, time_threshold=0.0001):
    """健壮地加载和过滤数据"""
    print(f"📊 加载文件: {filename}")
    
    try:
        # 读取数据
        df = pd.read_csv(filename)
        print(f"✅ 成功加载 {len(df)} 行数据")
        
        # 显示原始列名
        print(f"📋 原始列名: {list(df.columns)}")
        
        # 自动识别列
        time_col = None
        real_col = None
        pred_col = None
        
        for col in df.columns:
            col_lower = str(col).lower()
            if 'time' in col_lower and time_col is None:
                time_col = col
            elif any(word in col_lower for word in ['real', '真实', '实际']) and real_col is None:
                real_col = col
            elif any(word in col_lower for word in ['pred', '预测']) and pred_col is None:
                pred_col = col
        
        print(f"🔍 识别的列:")
        print(f"   时间列: {time_col}")
        print(f"   真实值列: {real_col}")
        print(f"   预测值列: {pred_col}")
        
        if not all([time_col, real_col, pred_col]):
            print("❌ 无法识别必要的数据列")
            return None
        
        # 提取必要的列
        data = {
            'time': df[time_col].astype(float),
            'real_value': df[real_col].astype(float),
            'prediction': df[pred_col].astype(float)
        }
        
        # 创建新的DataFrame
        df_clean = pd.DataFrame(data)
        
        # 计算误差
        df_clean['error'] = df_clean['prediction'] - df_clean['real_value']
        
        print(f"📊 数据范围:")
        print(f"   时间: {df_clean['time'].min():.6f} - {df_clean['time'].max():.6f} 秒")
        print(f"   真实值: {df_clean['real_value'].min():.6f} - {df_clean['real_value'].max():.6f} μm")
        print(f"   预测值: {df_clean['prediction'].min():.6f} - {df_clean['prediction'].max():.6f} μm")
        
        # 检查重复时间戳
        duplicate_count = df_clean['time'].duplicated().sum()
        print(f"📊 重复时间戳: {duplicate_count} 个")
        
        if duplicate_count > 0:
            print("🔧 处理重复时间戳...")
            # 按时间排序
            df_clean = df_clean.sort_values('time').reset_index(drop=True)
            
            # 为重复时间戳添加微小递增偏移
            time_values = df_clean['time'].values
            for i in range(1, len(time_values)):
                if time_values[i] <= time_values[i-1]:
                    time_values[i] = time_values[i-1] + 1e-8
            
            df_clean['time'] = time_values
            print("   ✅ 重复时间戳已处理")
        
        # 时间过滤
        original_count = len(df_clean)
        df_filtered = df_clean[df_clean['time'] > time_threshold].copy()
        filtered_count = len(df_filtered)
        
        print(f"🔍 时间过滤 (阈值: {time_threshold}s):")
        print(f"   过滤前: {original_count} 行")
        print(f"   过滤后: {filtered_count} 行")
        print(f"   移除: {original_count - filtered_count} 行 ({(original_count - filtered_count)/original_count*100:.1f}%)")
        
        if filtered_count == 0:
            print("❌ 过滤后没有数据！")
            return None
        
        # 重置时间和索引
        df_filtered = df_filtered.reset_index(drop=True)
        time_offset = df_filtered['time'].min()
        df_filtered['time'] = df_filtered['time'] - time_offset
        
        print(f"✅ 数据处理完成，有效数据: {len(df_filtered)} 行")
        
        return df_filtered
        
    except Exception as e:
        print(f"❌ 处理数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_simple_plots(df, filename, time_threshold):
    """创建简单的分析图表"""
    print("🎨 生成分析图表...")
    
    # 提取数据
    times = df['time'].values
    real_values = df['real_value'].values
    predictions = df['prediction'].values
    errors = df['error'].values
    
    # 计算统计指标
    mae = np.mean(np.abs(errors))
    rmse = np.sqrt(np.mean(errors**2))
    correlation = np.corrcoef(real_values, predictions)[0, 1]
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'位移预测分析 (过滤阈值: {time_threshold}s)', fontsize=16, fontweight='bold')
    
    # 1. 位移对比图
    ax1 = axes[0, 0]
    ax1.plot(times, real_values, 'b-', linewidth=1.5, label='实际位移', alpha=0.8)
    ax1.plot(times, predictions, 'r-', linewidth=1.5, label='预测位移', alpha=0.8)
    ax1.set_title('实际位移 vs 预测位移')
    ax1.set_xlabel('时间 (秒)')
    ax1.set_ylabel('位移 (μm)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 添加统计信息
    stats_text = f'MAE: {mae:.6f}μm\nRMSE: {rmse:.6f}μm\n相关系数: {correlation:.6f}\n数据点: {len(df)}'
    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 2. 误差时间序列
    ax2 = axes[0, 1]
    ax2.plot(times, errors, 'g-', linewidth=1, alpha=0.7, label='预测误差')
    ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax2.axhline(y=np.mean(errors), color='red', linestyle='--', alpha=0.7, 
                label=f'平均误差: {np.mean(errors):.6f}μm')
    ax2.fill_between(times, errors, 0, alpha=0.3, color='green')
    ax2.set_title('预测误差随时间变化')
    ax2.set_xlabel('时间 (秒)')
    ax2.set_ylabel('误差 (μm)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 误差分布直方图
    ax3 = axes[1, 0]
    ax3.hist(errors, bins=50, alpha=0.7, color='orange', edgecolor='black', density=True)
    ax3.axvline(x=np.mean(errors), color='red', linestyle='--', linewidth=2, 
                label=f'平均: {np.mean(errors):.6f}μm')
    ax3.axvline(x=np.median(errors), color='blue', linestyle='--', linewidth=2, 
                label=f'中位数: {np.median(errors):.6f}μm')
    ax3.axvline(x=0, color='black', linestyle='-', alpha=0.5)
    ax3.set_title('误差分布直方图')
    ax3.set_xlabel('误差 (μm)')
    ax3.set_ylabel('概率密度')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 散点图 (预测 vs 实际)
    ax4 = axes[1, 1]
    scatter = ax4.scatter(real_values, predictions, alpha=0.6, s=10, c=np.abs(errors), cmap='viridis')
    
    # 添加理想预测线 (y=x)
    min_val = min(np.min(real_values), np.min(predictions))
    max_val = max(np.max(real_values), np.max(predictions))
    ax4.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='理想预测线')
    
    ax4.set_title('预测值 vs 实际值散点图')
    ax4.set_xlabel('实际位移 (μm)')
    ax4.set_ylabel('预测位移 (μm)')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 添加颜色条
    cbar = plt.colorbar(scatter, ax=ax4)
    cbar.set_label('绝对误差 (μm)')
    
    plt.tight_layout()
    
    # 保存图表
    base_name = os.path.splitext(filename)[0]
    plot_filename = f"{base_name}_analysis_{time_threshold}s.png"
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"📊 分析图表已保存到: {plot_filename}")
    
    return fig

def print_statistics(df, time_threshold):
    """打印详细统计信息"""
    times = df['time'].values
    real_values = df['real_value'].values
    predictions = df['prediction'].values
    errors = df['error'].values
    
    print(f"\n📊 过滤后数据统计 (阈值: {time_threshold}s):")
    print(f"="*50)
    
    print(f"📈 基本信息:")
    print(f"   数据点数: {len(df)}")
    print(f"   时间范围: {times.min():.6f} - {times.max():.6f} 秒")
    print(f"   数据时长: {times.max() - times.min():.6f} 秒")
    print(f"   采样频率: {len(df)/(times.max() - times.min()):.1f} Hz")
    
    print(f"\n📊 位移统计:")
    print(f"   实际位移范围: [{real_values.min():.6f}, {real_values.max():.6f}] μm")
    print(f"   预测位移范围: [{predictions.min():.6f}, {predictions.max():.6f}] μm")
    print(f"   位移变化幅度: {real_values.max() - real_values.min():.6f} μm")
    
    print(f"\n📊 误差统计:")
    print(f"   平均误差(ME): {np.mean(errors):.6f} μm")
    print(f"   平均绝对误差(MAE): {np.mean(np.abs(errors)):.6f} μm")
    print(f"   均方根误差(RMSE): {np.sqrt(np.mean(errors**2)):.6f} μm")
    print(f"   误差标准差: {np.std(errors):.6f} μm")
    print(f"   最大正误差: {np.max(errors):.6f} μm")
    print(f"   最大负误差: {np.min(errors):.6f} μm")
    
    # 相关性分析
    correlation = np.corrcoef(real_values, predictions)[0, 1]
    print(f"\n📊 预测质量:")
    print(f"   预测-实际相关系数: {correlation:.6f}")
    
    # 相对误差
    relative_errors = np.abs(errors) / (np.abs(real_values) + 1e-10) * 100
    print(f"   平均相对误差: {np.mean(relative_errors):.4f}%")
    
    # 质量评级
    if correlation > 0.95 and np.mean(np.abs(errors)) < 0.1:
        quality = "优秀"
    elif correlation > 0.9 and np.mean(np.abs(errors)) < 0.5:
        quality = "良好"
    elif correlation > 0.8 and np.mean(np.abs(errors)) < 1.0:
        quality = "一般"
    else:
        quality = "需要改进"
    
    print(f"   预测质量评级: {quality}")

def main():
    """主函数"""
    print("📊 简单健壮的位移预测数据分析工具")
    print("="*50)
    
    # 获取时间阈值
    time_threshold_input = input("请输入时间过滤阈值（秒，默认0.0001）: ").strip()
    try:
        time_threshold = float(time_threshold_input) if time_threshold_input else 0.0001
    except ValueError:
        time_threshold = 0.0001
        print(f"⚠️ 输入无效，使用默认值: {time_threshold} 秒")
    
    # 查找CSV文件
    csv_files = glob.glob("real_time_predictions_*.csv")
    if not csv_files:
        print("❌ 未找到 real_time_predictions_*.csv 文件")
        return
    
    # 使用最新的文件
    csv_files.sort(key=os.path.getmtime, reverse=True)
    filename = csv_files[0]
    print(f"🔍 使用文件: {filename}")
    
    # 加载和过滤数据
    df_filtered = robust_load_and_filter(filename, time_threshold)
    
    if df_filtered is None:
        print("❌ 数据处理失败")
        return
    
    # 生成图表
    try:
        fig = create_simple_plots(df_filtered, filename, time_threshold)
        
        # 打印统计信息
        print_statistics(df_filtered, time_threshold)
        
        # 保存过滤后的数据
        base_name = os.path.splitext(filename)[0]
        output_filename = f"{base_name}_filtered_{time_threshold}s.csv"
        df_filtered.to_csv(output_filename, index=False)
        print(f"💾 过滤后数据已保存到: {output_filename}")
        
        # 显示图表
        plt.show()
        
        print("\n✅ 分析完成！")
        
    except Exception as e:
        print(f"❌ 生成图表时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
