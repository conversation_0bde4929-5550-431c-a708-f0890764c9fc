"""
DSA实时回调预测系统启动脚本
提供简单的菜单界面，方便用户选择不同的预测模式
"""

import asyncio
import os
import sys

def print_banner():
    """打印系统横幅"""
    print("="*60)
    print("🎯 DSA实时回调预测系统")
    print("每次DSA回调都直接进行预测，确保真正的实时性")
    print("="*60)

def check_prerequisites():
    """检查系统前提条件"""
    print("🔍 检查系统前提条件...")
    
    # 检查DLL文件
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    if not os.path.exists(dll_path):
        print(f"❌ 找不到DLL文件: {dll_path}")
        print("请确保DSA SDK已正确安装")
        return False
    else:
        print(f"✅ DLL文件存在: {dll_path}")
    
    # 检查训练数据
    if os.path.exists('v1.txt'):
        print("✅ 训练数据文件存在: v1.txt")
    else:
        print("⚠️ 训练数据文件不存在: v1.txt")
        print("系统将在首次运行时自动收集训练数据")
    
    # 检查Python模块
    required_modules = ['torch', 'numpy', 'pandas', 'matplotlib']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ Python模块存在: {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"❌ Python模块缺失: {module}")
    
    if missing_modules:
        print(f"请安装缺失的模块: pip install {' '.join(missing_modules)}")
        return False
    
    print("✅ 系统前提条件检查完成")
    return True

def show_menu():
    """显示主菜单"""
    print("\n" + "="*50)
    print("📋 请选择预测模式:")
    print("="*50)
    print("1. 🧪 快速测试模式 (15秒测试)")
    print("2. 🎯 标准实时预测 (60秒预测)")
    print("3. 🚀 高级双重预测 (位移+速度)")
    print("4. ⚙️ 自定义预测时长")
    print("5. 📊 查看历史预测结果")
    print("6. 🔧 系统诊断")
    print("0. 🚪 退出系统")
    print("="*50)

async def run_quick_test():
    """运行快速测试"""
    print("\n🧪 启动快速测试模式...")
    try:
        from test_real_time_callback import SimpleRealTimeTest
        
        dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
        test = SimpleRealTimeTest(dll_path)
        success = await test.run_test(duration=30)
        
        if success:
            print("✅ 快速测试完成")
        else:
            print("❌ 快速测试失败")
    except Exception as e:
        print(f"❌ 快速测试出错: {e}")

async def run_standard_prediction():
    """运行标准实时预测"""
    print("\n🎯 启动标准实时预测...")
    try:
        from real_time_callback_prediction import RealTimePredictionSystem
        
        dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
        system = RealTimePredictionSystem(
            dll_path=dll_path,
            model_path="displacement_model.pth",
            scaler_path="displacement_scaler.pkl"
        )
        
        success = await system.start_real_time_prediction(duration_seconds=30)
        
        if success:
            print("✅ 标准实时预测完成")
        else:
            print("❌ 标准实时预测失败")
    except Exception as e:
        print(f"❌ 标准实时预测出错: {e}")

async def run_advanced_prediction():
    """运行高级双重预测"""
    print("\n🚀 启动高级双重预测...")
    try:
        from advanced_real_time_prediction import AdvancedRealTimePredictionSystem
        
        dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
        system = AdvancedRealTimePredictionSystem(dll_path)
        
        success = await system.start_dual_prediction(duration_seconds=30)
        
        if success:
            print("✅ 高级双重预测完成")
        else:
            print("❌ 高级双重预测失败")
    except Exception as e:
        print(f"❌ 高级双重预测出错: {e}")

async def run_custom_prediction():
    """运行自定义时长预测"""
    print("\n⚙️ 自定义预测设置...")
    
    try:
        duration = int(input("请输入预测时长（秒，建议10-300）: "))
        if duration < 5:
            print("⚠️ 预测时长太短，设置为最小值5秒")
            duration = 5
        elif duration > 600:
            print("⚠️ 预测时长太长，设置为最大值600秒")
            duration = 600
        
        print(f"🎯 启动{duration}秒自定义实时预测...")
        
        from real_time_callback_prediction import RealTimePredictionSystem
        
        dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
        system = RealTimePredictionSystem(
            dll_path=dll_path,
            model_path="displacement_model.pth",
            scaler_path="displacement_scaler.pkl"
        )
        
        success = await system.start_real_time_prediction(duration_seconds=duration)
        
        if success:
            print("✅ 自定义实时预测完成")
        else:
            print("❌ 自定义实时预测失败")
            
    except ValueError:
        print("❌ 输入无效，请输入数字")
    except Exception as e:
        print(f"❌ 自定义预测出错: {e}")

def show_history():
    """显示历史预测结果"""
    print("\n📊 查看历史预测结果...")
    
    # 查找预测结果文件
    import glob
    
    prediction_files = glob.glob("*predictions*.csv")
    analysis_files = glob.glob("*prediction_analysis*.png")
    
    if prediction_files:
        print("📄 预测数据文件:")
        for i, file in enumerate(prediction_files[-5:], 1):  # 显示最近5个文件
            file_size = os.path.getsize(file) / 1024  # KB
            print(f"  {i}. {file} ({file_size:.1f} KB)")
    else:
        print("📄 没有找到预测数据文件")
    
    if analysis_files:
        print("📊 分析图表文件:")
        for i, file in enumerate(analysis_files[-5:], 1):  # 显示最近5个文件
            file_size = os.path.getsize(file) / 1024  # KB
            print(f"  {i}. {file} ({file_size:.1f} KB)")
    else:
        print("📊 没有找到分析图表文件")

def run_diagnostics():
    """运行系统诊断"""
    print("\n🔧 系统诊断...")
    
    # 检查系统资源
    try:
        import psutil
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('.')
        
        print(f"💻 CPU使用率: {cpu_percent:.1f}%")
        print(f"💾 内存使用率: {memory.percent:.1f}% ({memory.used/1024/1024/1024:.1f}GB/{memory.total/1024/1024/1024:.1f}GB)")
        print(f"💿 磁盘使用率: {disk.percent:.1f}% ({disk.free/1024/1024/1024:.1f}GB可用)")
    except ImportError:
        print("⚠️ psutil模块未安装，无法显示系统资源信息")
    
    # 检查GPU
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            current_gpu = torch.cuda.current_device()
            gpu_name = torch.cuda.get_device_name(current_gpu)
            print(f"🎮 GPU可用: {gpu_count}个设备")
            print(f"🎮 当前GPU: {gpu_name}")
        else:
            print("🎮 GPU不可用，使用CPU计算")
    except Exception as e:
        print(f"🎮 GPU检查失败: {e}")
    
    # 检查模型文件
    model_files = ['displacement_model.pth', 'displacement_scaler.pkl', 'v1.txt']
    for file in model_files:
        if os.path.exists(file):
            size = os.path.getsize(file) / 1024  # KB
            print(f"✅ 模型文件存在: {file} ({size:.1f} KB)")
        else:
            print(f"❌ 模型文件缺失: {file}")

async def main():
    """主函数"""
    print_banner()
    
    # 检查前提条件
    if not check_prerequisites():
        print("\n❌ 系统前提条件不满足，请解决后重试")
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("\n请输入选项 (0-6): ").strip()
            
            if choice == '0':
                print("👋 感谢使用DSA实时回调预测系统！")
                break
            elif choice == '1':
                await run_quick_test()
            elif choice == '2':
                await run_standard_prediction()
            elif choice == '3':
                await run_advanced_prediction()
            elif choice == '4':
                await run_custom_prediction()
            elif choice == '5':
                show_history()
            elif choice == '6':
                run_diagnostics()
            else:
                print("❌ 无效选项，请重新选择")
            
            # 等待用户确认继续
            if choice in ['1', '2', '3', '4']:
                input("\n按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n\n⚠️ 用户中断操作")
            break
        except Exception as e:
            print(f"\n❌ 操作出错: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 程序异常退出: {e}")
        input("按回车键退出...")
