# 预测速率优化改进

## 问题诊断
- **现象**：每秒只能预测 60 个点
- **目标**：达到 2000 Hz 采样率
- **根本原因**：
  1. 目标采样率设置过低（默认 100 点/秒）
  2. 异步等待时间过长（50-100μs）
  3. 动态调整策略不够激进

## 实施的优化

### 1. 提高目标采样率（第 693-705 行）
**修改前：**
```python
three_seconds_data = await stream_predict_for_duration(
    processor=processor,
    displacement_source=displacement_source,
    velocity_source=velocity_source,
    duration_seconds=prediction_duration,
    on_sample=on_sample
    # 使用默认值 target_points_per_second=100
)
```

**修改后：**
```python
target_sampling_rate = 2000  # 与 DSA 采样率一致

three_seconds_data = await stream_predict_for_duration(
    processor=processor,
    displacement_source=displacement_source,
    velocity_source=velocity_source,
    duration_seconds=prediction_duration,
    on_sample=on_sample,
    target_points_per_second=target_sampling_rate  # 提高 20 倍
)
```

**预期效果：** 理论上可提升 20 倍（但受处理时间限制）

### 2. 优化异步等待时间（第 241-248 行）
**修改前：**
```python
await asyncio.sleep(0.00005)  # 50μs
await asyncio.sleep(0.0001)   # 100μs
```

**修改后：**
```python
await asyncio.sleep(0.00001)  # 10μs（减少 80%）
dynamic_sleep = min(0.00005, 1.0 / (target_points_per_second * 10))
await asyncio.sleep(dynamic_sleep)  # 动态调整
```

**预期效果：** 减少 80% 的等待开销，提升 5-10%

### 3. 优化 collect_one_second_data 函数（第 102-115 行）
**修改前：**
```python
await asyncio.sleep(0.0001)  # 100μs
# 动态调整：0.0001 ~ 0.0005 秒
```

**修改后：**
```python
await asyncio.sleep(0.00001)  # 10μs（统一低延迟）
# 动态调整：全部使用 10μs（更激进）
```

**预期效果：** 提升 5-10%

## 性能预期

### 理论分析
- **处理时间瓶颈**：3.5ms/次 → 最大 286 点/秒
- **异步等待优化**：减少 ~50μs/次 → 可能提升 10-15%
- **综合预期**：60 点/秒 → **150-200 点/秒**

### 实际测试建议
1. 运行优化后的代码
2. 检查 `three_seconds_data.txt` 中的数据点数
3. 计算实际采样率：`总点数 / 时间范围`
4. 对比优化前后的性能

## 进一步优化方向

### 短期（可立即实施）
- [ ] 启用 PyTorch 的 JIT 编译
- [ ] 使用 `torch.jit.script` 加速 LSTM 推理
- [ ] 减少数据复制操作

### 中期（需要代码重构）
- [ ] 实现批量预测（每次预测多个点）
- [ ] 使用线程池处理预测
- [ ] 分离数据读取和预测逻辑

### 长期（需要架构改进）
- [ ] 使用 ONNX Runtime 加速推理
- [ ] 实现 GPU 推理（如果硬件支持）
- [ ] 使用更轻量的模型架构
- [ ] 实现多进程处理

## 验证方法

### 检查点 1：数据点数增加
```bash
# 运行优化后的代码
python integrated_real_time_demo.py

# 检查输出的数据点数
# 预期：从 ~60 点/秒 提升到 150-200 点/秒
```

### 检查点 2：处理时间分析
```python
# 在 three_seconds_data.txt 中查看处理时间列
# 预期：平均处理时间保持不变（~3.5ms）
# 说明：改进来自于减少等待时间，而非加快处理
```

### 检查点 3：采样率计算
```python
import pandas as pd
data = pd.read_csv('three_seconds_data.txt', sep='\t')
time_range = data['时间[s]'].max() - data['时间[s]'].min()
sampling_rate = len(data) / time_range
print(f"实际采样率: {sampling_rate:.1f} Hz")
```

## 注意事项

1. **CPU 占用**：减少等待时间会增加 CPU 占用，需要监控
2. **数据丢失**：如果采样率提升但处理不过来，可能导致数据丢失
3. **系统负载**：在高负载情况下，实际采样率可能低于预期
4. **硬件限制**：最终的采样率受硬件性能限制

## 相关文件
- `PERFORMANCE_ANALYSIS.md` - 详细的性能分析
- `integrated_real_time_demo.py` - 优化后的主程序

