"""
隔离1000次预测限制问题
创建一个最小化的测试来找出根本原因
"""

import asyncio
import os
import sys
import time

# 添加当前目录到路径
sys.path.insert(0, '.')

from real_time_callback_prediction import RealTimePredictionSystem

class DebugPredictionSystem(RealTimePredictionSystem):
    """调试版本的预测系统"""
    
    def __init__(self, dll_path, model_path=None, scaler_path=None):
        super().__init__(dll_path, model_path, scaler_path)
        self.prediction_call_count = 0
        self.last_prediction_time = None
        
    def prediction_callback(self, real_value, prediction, timestamp, data_type):
        """增强的预测回调函数"""
        self.prediction_call_count += 1
        self.last_prediction_time = timestamp
        
        if self.start_time is None:
            self.start_time = timestamp
            print("🚀 开始调试预测...")
        
        # 计算相对时间
        relative_time = timestamp - self.start_time
        
        # 在关键点输出调试信息
        if self.prediction_call_count in [1, 100, 500, 900, 995, 996, 997, 998, 999, 1000, 1001, 1002, 1005, 1010]:
            print(f"🔍 预测#{self.prediction_call_count}: 时间={relative_time:.6f}s, 真实值={real_value:.6f}μm, 预测值={prediction:.6f}μm")
        
        # 计算预测误差
        error = abs(real_value - prediction)
        self.prediction_errors.append(error)
        
        # 存储实时数据
        data_record = {
            'time': relative_time,
            'real_value': real_value,
            'prediction': prediction,
            'error': error,
            'timestamp': timestamp,
            'data_type': data_type
        }
        self.real_time_data.append(data_record)
        
        # 检查是否到达1000次
        if self.prediction_call_count == 1000:
            print("🎯 到达1000次预测！检查系统状态...")
            print(f"   预测器状态: {hasattr(self.predictor, 'is_initialized') and self.predictor.is_initialized}")
            print(f"   收集器状态: {self.collector.is_running}")
            print(f"   预测器计数: {getattr(self.predictor, 'prediction_count', 'N/A')}")
        
        # 在1000次后继续监控
        if self.prediction_call_count > 1000 and self.prediction_call_count <= 1010:
            print(f"🔍 1000次后预测#{self.prediction_call_count}: 时间={relative_time:.6f}s")

async def test_1000_limit():
    """测试1000次预测限制"""
    print("🔍 隔离1000次预测限制问题")
    print("="*60)
    
    # DLL 路径
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    # 检查DLL文件
    if not os.path.exists(dll_path):
        print(f"❌ 找不到DLL文件: {dll_path}")
        return False
    
    # 创建调试预测系统
    system = DebugPredictionSystem(
        dll_path=dll_path,
        model_path="displacement_model.pth",
        scaler_path="displacement_scaler.pkl"
    )
    
    print("🚀 开始隔离测试...")
    print("📊 测试目标:")
    print("   - 运行5秒测试")
    print("   - 监控1000次预测前后的系统状态")
    print("   - 检查预测是否在1000次后停止")
    print("-" * 60)
    
    # 记录开始时间
    start_time = time.time()
    
    # 启动5秒测试
    success = await system.start_real_time_prediction(duration_seconds=5)
    
    # 记录结束时间
    end_time = time.time()
    actual_duration = end_time - start_time
    
    print("\n" + "="*60)
    print("🔍 隔离测试结果")
    print("="*60)
    
    print(f"⏱️  实际运行时间: {actual_duration:.2f} 秒")
    print(f"📈 总预测调用次数: {system.prediction_call_count}")
    
    if system.last_prediction_time and system.start_time:
        last_relative_time = system.last_prediction_time - system.start_time
        print(f"📊 最后一次预测时间: {last_relative_time:.6f} 秒")
        
        if system.prediction_call_count >= 1000:
            if system.prediction_call_count == 1000:
                print("⚠️ 预测恰好停止在1000次")
            elif system.prediction_call_count > 1000:
                print(f"✅ 预测超过1000次，达到{system.prediction_call_count}次")
            
            # 检查预测是否持续到接近结束
            if last_relative_time < actual_duration * 0.5:
                print(f"❌ 预测在 {last_relative_time:.3f}s 停止，但测试持续到 {actual_duration:.3f}s")
                print("🔍 确认存在1000次预测限制问题")
            else:
                print("✅ 预测持续到接近测试结束")
        else:
            print(f"⚠️ 预测次数未达到1000次，只有{system.prediction_call_count}次")
    
    # 分析预测数据
    if hasattr(system, 'real_time_data') and system.real_time_data:
        prediction_count = len(system.real_time_data)
        print(f"\n📊 数据分析:")
        print(f"   收集的预测数据: {prediction_count} 条")
        
        if prediction_count > 0:
            times = [d['time'] for d in system.real_time_data]
            print(f"   时间范围: {min(times):.6f}s - {max(times):.6f}s")
            print(f"   时间跨度: {max(times) - min(times):.6f}s")
            
            # 检查预测频率
            if len(times) > 1:
                intervals = [times[i] - times[i-1] for i in range(1, len(times))]
                avg_interval = sum(intervals) / len(intervals)
                avg_frequency = 1.0 / avg_interval if avg_interval > 0 else 0
                print(f"   平均预测间隔: {avg_interval:.6f}s")
                print(f"   平均预测频率: {avg_frequency:.1f} Hz")
    
    # 检查系统状态
    print(f"\n🔧 系统状态检查:")
    if hasattr(system, 'collector') and system.collector:
        print(f"   收集器运行状态: {system.collector.is_running}")
        if hasattr(system.collector, 'total_prediction_calls'):
            print(f"   收集器预测调用计数: {system.collector.total_prediction_calls}")
    
    if hasattr(system, 'predictor') and system.predictor:
        print(f"   预测器初始化状态: {getattr(system.predictor, 'is_initialized', 'N/A')}")
        print(f"   预测器计数: {getattr(system.predictor, 'prediction_count', 'N/A')}")
    
    return success

def main():
    """主函数"""
    print("🎯 隔离1000次预测限制问题")
    print("目标: 找出预测在1000次后停止的根本原因")
    print("="*60)
    
    try:
        result = asyncio.run(test_1000_limit())
        
        if result:
            print("\n🔍 隔离测试完成")
            print("📋 关键发现:")
            print("   如果预测恰好停止在1000次，说明存在硬编码限制")
            print("   如果预测超过1000次，说明问题在其他地方")
            print("   如果预测时间跨度很短，说明存在时间相关的停止逻辑")
        else:
            print("\n❌ 隔离测试失败")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
