"""
测试只生成CSV文件的实时预测系统
验证不会生成任何PNG图表文件
"""

import asyncio
import os
import glob
import time

async def test_csv_only_prediction():
    """测试只生成CSV的预测系统"""
    print("🧪 测试只生成CSV文件的实时预测系统")
    print("="*60)
    
    # 记录测试前的文件状态
    csv_files_before = set(glob.glob("real_time_predictions_*.csv"))
    png_files_before = set(glob.glob("*prediction_analysis*.png"))
    
    print(f"📊 测试前状态:")
    print(f"   CSV文件数量: {len(csv_files_before)}")
    print(f"   PNG文件数量: {len(png_files_before)}")
    
    # 导入并运行实时预测系统
    try:
        from real_time_callback_prediction import RealTimePredictionSystem
        
        dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
        
        if not os.path.exists(dll_path):
            print(f"❌ 找不到DLL文件: {dll_path}")
            return False
        
        # 创建系统
        system = RealTimePredictionSystem(
            dll_path=dll_path,
            model_path="displacement_model.pth",
            scaler_path="displacement_scaler.pkl"
        )
        
        print("🚀 开始1秒测试预测...")
        success = await system.start_real_time_prediction(duration_seconds=1)
        
        if not success:
            print("❌ 预测测试失败")
            return False
        
        # 检查测试后的文件状态
        csv_files_after = set(glob.glob("real_time_predictions_*.csv"))
        png_files_after = set(glob.glob("*prediction_analysis*.png"))
        
        new_csv_files = csv_files_after - csv_files_before
        new_png_files = png_files_after - png_files_before
        
        print(f"\n📊 测试后状态:")
        print(f"   新增CSV文件: {len(new_csv_files)}")
        print(f"   新增PNG文件: {len(new_png_files)}")
        
        if new_csv_files:
            print(f"✅ 成功生成CSV文件:")
            for csv_file in new_csv_files:
                file_size = os.path.getsize(csv_file) / 1024  # KB
                print(f"   - {csv_file} ({file_size:.1f} KB)")
        
        if new_png_files:
            print(f"⚠️ 意外生成了PNG文件:")
            for png_file in new_png_files:
                print(f"   - {png_file}")
            return False
        else:
            print(f"✅ 没有生成PNG文件 (符合预期)")
        
        return len(new_csv_files) > 0 and len(new_png_files) == 0
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_plot_saved_data():
    """测试plot_saved_data.py是否能正常工作"""
    print("\n🧪 测试plot_saved_data.py功能")
    print("="*40)
    
    try:
        # 查找最新的CSV文件
        csv_files = glob.glob("real_time_predictions_*.csv")
        if not csv_files:
            print("❌ 没有找到CSV文件进行测试")
            return False
        
        # 使用最新的文件
        latest_csv = max(csv_files, key=os.path.getmtime)
        print(f"📊 测试文件: {latest_csv}")
        
        # 导入plot_saved_data模块
        import plot_saved_data
        
        # 测试加载数据
        df = plot_saved_data.load_and_analyze_data(latest_csv, time_threshold=0.0001)
        
        if df is None:
            print("❌ 数据加载失败")
            return False
        
        print(f"✅ 数据加载成功: {len(df)} 行数据")
        
        # 测试生成图表（但不显示）
        print("📊 测试图表生成...")
        
        # 记录PNG文件数量
        png_files_before = set(glob.glob("*.png"))
        
        # 生成图表
        fig1 = plot_saved_data.create_comprehensive_plots(df, latest_csv)
        fig2 = plot_saved_data.create_detailed_error_analysis(df, latest_csv)
        
        # 检查是否生成了新的PNG文件
        png_files_after = set(glob.glob("*.png"))
        new_png_files = png_files_after - png_files_before
        
        if new_png_files:
            print(f"✅ 成功生成分析图表:")
            for png_file in new_png_files:
                file_size = os.path.getsize(png_file) / 1024  # KB
                print(f"   - {png_file} ({file_size:.1f} KB)")
        
        # 关闭图表以释放内存
        import matplotlib.pyplot as plt
        plt.close('all')
        
        return True
        
    except Exception as e:
        print(f"❌ plot_saved_data测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🧪 CSV文件生成和绘图分离测试")
    print("="*60)
    
    # 测试1: 验证只生成CSV文件
    print("📋 测试1: 验证实时预测只生成CSV文件")
    csv_test_result = await test_csv_only_prediction()
    
    if csv_test_result:
        print("✅ 测试1通过: 实时预测只生成CSV文件")
        
        # 测试2: 验证plot_saved_data.py能正常工作
        print("\n📋 测试2: 验证plot_saved_data.py能正常生成图表")
        plot_test_result = test_plot_saved_data()
        
        if plot_test_result:
            print("✅ 测试2通过: plot_saved_data.py正常工作")
            print("\n🎉 所有测试通过！")
            print("📋 工作流程验证成功:")
            print("   1. 实时预测 → 只生成CSV文件 ✅")
            print("   2. plot_saved_data.py → 生成分析图表 ✅")
        else:
            print("❌ 测试2失败: plot_saved_data.py有问题")
    else:
        print("❌ 测试1失败: 实时预测仍在生成PNG文件")
    
    print("\n📊 测试完成")

if __name__ == "__main__":
    asyncio.run(main())
