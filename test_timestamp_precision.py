"""
测试DSA实时回调预测系统的时间戳精度
专门验证时间戳计算是否正确
"""

import asyncio
import time
import numpy as np
import os
from data_collector_3s import DSADataCollector
from real_time_stream_processor import RealTimeStreamProcessor
from real_time_train import train_displacement_model

class TimestampPrecisionTest:
    """时间戳精度测试"""
    
    def __init__(self, dll_path):
        self.dll_path = dll_path
        self.prediction_count = 0
        self.start_time = None
        self.predictions = []
        self.last_timestamp = None
        
    def prediction_callback(self, real_value, prediction, timestamp, data_type):
        """预测回调函数 - 专注时间戳分析"""
        if self.start_time is None:
            self.start_time = timestamp
            print("🚀 开始接收实时预测结果...")
            print("📊 时间戳精度分析:")
            print("   格式: 预测#N: 绝对时间戳 | 相对时间 | 时间间隔")
        
        relative_time = timestamp - self.start_time
        
        # 计算与上一个预测的时间间隔
        time_interval = 0.0
        if self.last_timestamp is not None:
            time_interval = timestamp - self.last_timestamp
        
        error = abs(real_value - prediction)
        
        self.prediction_count += 1
        self.predictions.append({
            'absolute_timestamp': timestamp,
            'relative_time': relative_time,
            'time_interval': time_interval,
            'real': real_value,
            'pred': prediction,
            'error': error
        })
        
        # 显示详细的时间戳信息
        if self.prediction_count <= 20 or self.prediction_count % 50 == 0:
            print(f"   预测#{self.prediction_count}: {timestamp:.6f} | {relative_time:.6f}s | Δt={time_interval:.6f}s ({time_interval*1000:.3f}ms)")
        
        self.last_timestamp = timestamp
    
    async def run_test(self, duration=10):
        """运行时间戳精度测试"""
        print("="*60)
        print("🧪 DSA实时回调预测系统 - 时间戳精度测试")
        print("="*60)
        
        # 1. 检查训练数据
        if not os.path.exists('v1.txt'):
            print("❌ 未找到训练数据 v1.txt")
            print("请先运行数据收集程序")
            return False
        
        # 2. 快速训练模型
        print("🚀 快速训练模型...")
        model, scaler = train_displacement_model(
            data_file='v1.txt',
            window_size=25,
            hidden_size=64,
            learning_rate=0.01,
            epochs=30,  # 更少的轮数，专注测试时间戳
            train_points=500  # 更少的数据，加快训练
        )
        
        if model is None or scaler is None:
            print("❌ 模型训练失败")
            return False
        
        print("✅ 模型训练完成")
        
        # 3. 创建预测器
        print("🔧 创建预测器...")
        predictor = RealTimeStreamProcessor(
            model=model,
            scaler=scaler,
            window_size=25,
            enable_akf=True
        )
        
        # 4. 创建数据收集器
        print("🔧 创建数据收集器...")
        collector = DSADataCollector(self.dll_path)
        collector.enable_continuous_mode()
        collector.enable_real_time_streaming()
        
        # 5. 收集初始化数据
        print("📊 收集初始化数据...")
        if not collector.initialize_sdk():
            print("❌ SDK初始化失败")
            return False
        
        if not collector.start_collection():
            print("❌ 数据收集启动失败")
            return False
        
        # 收集25个初始化数据点
        historical_data = []
        timeout_count = 0
        
        while len(historical_data) < 25 and timeout_count < 50:
            data = collector.get_real_time_data_by_type('displacement', timeout=0.1)
            if data is not None:
                historical_data.append(data['value'])
                if len(historical_data) % 5 == 0:
                    print(f"收集初始化数据: {len(historical_data)}/25")
            else:
                timeout_count += 1
                await asyncio.sleep(0.1)
        
        if len(historical_data) < 25:
            print(f"❌ 初始化数据不足: {len(historical_data)}/25")
            collector.stop_collection()
            return False
        
        # 初始化预测器
        predictor.initialize_with_historical_data(historical_data)
        print("✅ 预测器初始化完成")
        
        # 6. 设置实时预测
        collector.set_real_time_predictor(predictor)
        collector.set_prediction_callback(self.prediction_callback)
        
        print(f"🚀 开始{duration}秒时间戳精度测试...")
        print("专门分析DSA回调的时间戳精度")
        
        # 7. 等待测试完成
        try:
            await asyncio.sleep(duration)
        except KeyboardInterrupt:
            print("⚠️ 用户中断测试")
        
        # 8. 停止收集
        collector.stop_collection()
        
        # 9. 分析时间戳精度
        self.analyze_timestamp_precision()
        
        return True
    
    def analyze_timestamp_precision(self):
        """分析时间戳精度"""
        print("\n" + "="*60)
        print("📊 时间戳精度分析结果")
        print("="*60)
        
        if len(self.predictions) == 0:
            print("❌ 没有收集到预测数据")
            return
        
        # 提取时间间隔数据
        time_intervals = [p['time_interval'] for p in self.predictions[1:]]  # 跳过第一个（间隔为0）
        relative_times = [p['relative_time'] for p in self.predictions]
        
        if len(time_intervals) == 0:
            print("❌ 时间间隔数据不足")
            return
        
        # 统计分析
        total_time = max(relative_times) - min(relative_times)
        prediction_rate = len(self.predictions) / total_time if total_time > 0 else 0
        
        avg_interval = np.mean(time_intervals)
        std_interval = np.std(time_intervals)
        min_interval = np.min(time_intervals)
        max_interval = np.max(time_intervals)
        
        print(f"⏱️  测试时长: {total_time:.6f} 秒")
        print(f"📈 预测次数: {len(self.predictions)}")
        print(f"🎯 预测频率: {prediction_rate:.2f} Hz")
        print(f"📊 平均时间间隔: {avg_interval:.6f} 秒 ({avg_interval*1000:.3f} ms)")
        print(f"📊 时间间隔标准差: {std_interval:.6f} 秒 ({std_interval*1000:.3f} ms)")
        print(f"📊 最小时间间隔: {min_interval:.6f} 秒 ({min_interval*1000:.3f} ms)")
        print(f"📊 最大时间间隔: {max_interval:.6f} 秒 ({max_interval*1000:.3f} ms)")
        
        # 分析时间戳分布
        print(f"\n🔍 时间戳分布分析:")
        
        # 检查是否有重复时间戳
        unique_timestamps = len(set([p['absolute_timestamp'] for p in self.predictions]))
        print(f"   唯一时间戳数量: {unique_timestamps}/{len(self.predictions)}")
        
        if unique_timestamps < len(self.predictions):
            duplicate_count = len(self.predictions) - unique_timestamps
            print(f"   ⚠️ 发现 {duplicate_count} 个重复时间戳")
        else:
            print(f"   ✅ 所有时间戳都是唯一的")
        
        # 检查时间戳单调性
        timestamps = [p['absolute_timestamp'] for p in self.predictions]
        is_monotonic = all(timestamps[i] <= timestamps[i+1] for i in range(len(timestamps)-1))
        print(f"   时间戳单调性: {'✅ 单调递增' if is_monotonic else '❌ 非单调'}")
        
        # 分析时间间隔的一致性
        if len(time_intervals) > 1:
            # 计算期望的时间间隔（基于DSA采样率）
            expected_interval = 1.0 / 10000  # 假设10kHz采样率
            interval_deviation = abs(avg_interval - expected_interval)
            
            print(f"   期望时间间隔: {expected_interval:.6f} 秒 ({expected_interval*1000:.3f} ms)")
            print(f"   实际平均间隔: {avg_interval:.6f} 秒 ({avg_interval*1000:.3f} ms)")
            print(f"   间隔偏差: {interval_deviation:.6f} 秒 ({interval_deviation*1000:.3f} ms)")
            
            # 判断时间戳精度是否合理
            if interval_deviation < expected_interval * 0.1:  # 偏差小于10%
                print(f"   ✅ 时间戳精度良好")
            else:
                print(f"   ⚠️ 时间戳精度可能有问题")
        
        # 显示前10个和后10个预测的详细时间戳
        print(f"\n📋 前10个预测的详细时间戳:")
        for i, pred in enumerate(self.predictions[:10]):
            print(f"  {i+1:2d}. 绝对时间戳={pred['absolute_timestamp']:.6f}, "
                  f"相对时间={pred['relative_time']:.6f}s, "
                  f"间隔={pred['time_interval']:.6f}s")
        
        if len(self.predictions) > 10:
            print(f"\n📋 后10个预测的详细时间戳:")
            for i, pred in enumerate(self.predictions[-10:], len(self.predictions)-9):
                print(f"  {i:2d}. 绝对时间戳={pred['absolute_timestamp']:.6f}, "
                      f"相对时间={pred['relative_time']:.6f}s, "
                      f"间隔={pred['time_interval']:.6f}s")

async def main():
    """主函数"""
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    # 检查DLL文件
    if not os.path.exists(dll_path):
        print(f"❌ 找不到DLL文件: {dll_path}")
        return
    
    # 创建测试实例
    test = TimestampPrecisionTest(dll_path)
    
    # 运行测试
    success = await test.run_test(duration=10)  # 10秒测试
    
    if success:
        print("✅ 时间戳精度测试完成")
    else:
        print("❌ 时间戳精度测试失败")

if __name__ == "__main__":
    print("🧪 DSA实时回调预测系统 - 时间戳精度测试程序")
    print("专门验证时间戳计算的精度和一致性")
    asyncio.run(main())
