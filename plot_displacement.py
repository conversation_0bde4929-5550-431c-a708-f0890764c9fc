#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绘制真实位移和预测位移对比图
"""

import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

def load_data(filename):
    """
    加载数据文件
    """
    try:
        # 读取数据，跳过空行
        data = pd.read_csv(filename, sep='\t', encoding='utf-8')
        # 移除可能的空行
        data = data.dropna()
        return data
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def plot_displacement_comparison(data):
    """
    绘制真实位移和预测位移对比图
    """
    # 创建图形和子图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # 提取数据
    time = data['时间[s]']
    real_displacement = data['位移[μm]']
    predicted_displacement = data['预测位移[μm]']
    
    # 第一个子图：时间序列对比
    ax1.plot(time, real_displacement, 'b-', linewidth=1.5, label='真实位移', alpha=0.8)
    ax1.plot(time, predicted_displacement, 'r--', linewidth=1.5, label='预测位移', alpha=0.8)
    ax1.set_xlabel('时间 [s]')
    ax1.set_ylabel('位移 [μm]')
    ax1.set_title('真实位移 vs 预测位移 时间序列对比')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 添加垂直线标记不同秒数
    for second in [1.0, 2.0]:
        ax1.axvline(x=second, color='gray', linestyle=':', alpha=0.5)
        ax1.text(second, ax1.get_ylim()[1], f'第{int(second)+1}秒开始', 
                rotation=90, verticalalignment='top', fontsize=8)
    
    # 第二个子图：散点图对比
    ax2.scatter(real_displacement, predicted_displacement, alpha=0.6, s=20)
    
    # 添加理想预测线 (y=x)
    min_val = min(min(real_displacement), min(predicted_displacement))
    max_val = max(max(real_displacement), max(predicted_displacement))
    ax2.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='理想预测线 (y=x)')
    
    ax2.set_xlabel('真实位移 [μm]')
    ax2.set_ylabel('预测位移 [μm]')
    ax2.set_title('真实位移 vs 预测位移 散点图')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_aspect('equal', adjustable='box')
    
    plt.tight_layout()
    return fig

def calculate_metrics(data):
    """
    计算预测精度指标
    """
    real_displacement = data['位移[μm]']
    predicted_displacement = data['预测位移[μm]']
    
    # 计算误差
    error = real_displacement - predicted_displacement
    
    # 计算各种指标
    mae = np.mean(np.abs(error))  # 平均绝对误差
    mse = np.mean(error**2)       # 均方误差
    rmse = np.sqrt(mse)           # 均方根误差
    max_error = np.max(np.abs(error))  # 最大绝对误差
    
    # 计算相关系数
    correlation = np.corrcoef(real_displacement, predicted_displacement)[0, 1]
    
    return {
        'MAE': mae,
        'MSE': mse,
        'RMSE': rmse,
        'Max_Error': max_error,
        'Correlation': correlation
    }

def plot_error_analysis(data):
    """
    绘制误差分析图
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
    
    # 计算误差
    time = data['时间[s]']
    real_displacement = data['位移[μm]']
    predicted_displacement = data['预测位移[μm]']
    error = real_displacement - predicted_displacement
    
    # 误差时间序列
    ax1.plot(time, error, 'g-', linewidth=1, alpha=0.7)
    ax1.axhline(y=0, color='r', linestyle='--', alpha=0.5)
    ax1.set_xlabel('时间 [s]')
    ax1.set_ylabel('预测误差 [μm]')
    ax1.set_title('预测误差随时间变化')
    ax1.grid(True, alpha=0.3)
    
    # 误差直方图
    ax2.hist(error, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    ax2.axvline(x=np.mean(error), color='r', linestyle='--', label=f'平均误差: {np.mean(error):.6f}')
    ax2.set_xlabel('预测误差 [μm]')
    ax2.set_ylabel('频次')
    ax2.set_title('预测误差分布')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    return fig

def main():
    """
    主函数
    """
    # 加载数据
    print("正在加载数据...")
    data = load_data('three_seconds_data.txt')
    
    if data is None:
        print("无法加载数据文件")
        return
    
    print(f"成功加载 {len(data)} 行数据")
    print(f"数据列: {list(data.columns)}")
    
    # 绘制对比图
    print("正在绘制位移对比图...")
    fig1 = plot_displacement_comparison(data)
    plt.savefig('displacement_comparison.png', dpi=300, bbox_inches='tight')
    print("位移对比图已保存为 displacement_comparison.png")
    plt.close(fig1)

    # 绘制误差分析图
    print("正在绘制误差分析图...")
    fig2 = plot_error_analysis(data)
    plt.savefig('error_analysis.png', dpi=300, bbox_inches='tight')
    print("误差分析图已保存为 error_analysis.png")
    plt.close(fig2)
    
    # 计算并显示精度指标
    print("\n=== 预测精度指标 ===")
    metrics = calculate_metrics(data)
    for key, value in metrics.items():
        print(f"{key}: {value:.6f}")
    
    # 按秒数分组分析
    print("\n=== 分秒数据分析 ===")
    for second in data['数据秒数'].unique():
        if pd.notna(second):
            subset = data[data['数据秒数'] == second]
            subset_metrics = calculate_metrics(subset)
            print(f"\n{second}:")
            print(f"  数据点数: {len(subset)}")
            print(f"  RMSE: {subset_metrics['RMSE']:.6f}")
            print(f"  相关系数: {subset_metrics['Correlation']:.6f}")

if __name__ == "__main__":
    main()
