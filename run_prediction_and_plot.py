"""
运行实时预测并自动调用绘图分析
只生成CSV数据文件，然后使用plot_saved_data.py进行可视化分析
"""

import asyncio
import os
import sys
import subprocess
import glob
import time

async def run_real_time_prediction(duration=30):
    """运行实时预测"""
    print("🚀 启动实时预测系统...")
    print(f"⏱️  预测时长: {duration} 秒")
    print("📊 只生成CSV数据文件，不生成PNG图表")
    print("="*60)
    
    # 导入实时预测系统
    try:
        from real_time_callback_prediction import RealTimePredictionSystem
    except ImportError as e:
        print(f"❌ 导入实时预测系统失败: {e}")
        return None
    
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    # 检查DLL文件
    if not os.path.exists(dll_path):
        print(f"❌ 找不到DLL文件: {dll_path}")
        return None
    
    # 创建实时预测系统
    system = RealTimePredictionSystem(
        dll_path=dll_path,
        model_path="displacement_model.pth",
        scaler_path="displacement_scaler.pkl"
    )
    
    # 记录开始前的CSV文件
    csv_files_before = set(glob.glob("real_time_predictions_*.csv"))
    
    # 启动实时预测
    success = await system.start_real_time_prediction(duration_seconds=duration)
    
    if not success:
        print("❌ 实时预测失败")
        return None
    
    # 查找新生成的CSV文件
    csv_files_after = set(glob.glob("real_time_predictions_*.csv"))
    new_csv_files = csv_files_after - csv_files_before
    
    if new_csv_files:
        # 返回最新的CSV文件
        latest_csv = max(new_csv_files, key=os.path.getmtime)
        print(f"✅ 实时预测完成，生成数据文件: {latest_csv}")
        return latest_csv
    else:
        print("⚠️ 未找到新生成的CSV文件")
        return None

def run_plot_analysis(csv_file=None, time_threshold=0.0001):
    """运行绘图分析"""
    print("\n" + "="*60)
    print("📊 启动数据分析和可视化...")
    print("="*60)
    
    if csv_file:
        print(f"🔍 分析文件: {csv_file}")
    else:
        print("🔍 将分析最新的CSV文件")
    
    try:
        # 直接导入并运行plot_saved_data模块
        import plot_saved_data
        
        # 如果指定了文件，临时修改查找逻辑
        if csv_file and os.path.exists(csv_file):
            print(f"📊 分析指定文件: {csv_file}")
            df = plot_saved_data.load_and_analyze_data(csv_file, time_threshold)
            if df is not None:
                # 生成图表
                fig1 = plot_saved_data.create_comprehensive_plots(df, csv_file)
                fig2 = plot_saved_data.create_detailed_error_analysis(df, csv_file)
                print("✅ 可视化分析完成")
                return True
        else:
            # 使用默认逻辑查找最新文件
            filename = plot_saved_data.find_latest_data_file()
            if filename:
                print(f"📊 分析最新文件: {filename}")
                df = plot_saved_data.load_and_analyze_data(filename, time_threshold)
                if df is not None:
                    # 生成图表
                    fig1 = plot_saved_data.create_comprehensive_plots(df, filename)
                    fig2 = plot_saved_data.create_detailed_error_analysis(df, filename)
                    print("✅ 可视化分析完成")
                    return True
            else:
                print("❌ 未找到CSV数据文件")
                return False
                
    except Exception as e:
        print(f"❌ 绘图分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 DSA实时预测 + 自动可视化分析系统")
    print("="*60)
    print("📋 工作流程:")
    print("   1. 运行实时预测，只生成CSV数据文件")
    print("   2. 自动调用plot_saved_data.py进行可视化分析")
    print("   3. 生成完整的分析图表")
    print("="*60)
    
    # 获取用户输入
    try:
        duration_input = input("请输入预测时长（秒，默认30）: ").strip()
        duration = int(duration_input) if duration_input else 30
    except ValueError:
        duration = 30
        print(f"⚠️ 输入无效，使用默认值: {duration} 秒")
    
    try:
        threshold_input = input("请输入时间过滤阈值（秒，默认0.0001）: ").strip()
        time_threshold = float(threshold_input) if threshold_input else 0.0001
    except ValueError:
        time_threshold = 0.0001
        print(f"⚠️ 输入无效，使用默认值: {time_threshold} 秒")
    
    print(f"\n🚀 开始执行...")
    print(f"   预测时长: {duration} 秒")
    print(f"   时间过滤阈值: {time_threshold} 秒")
    
    # 第一步：运行实时预测
    try:
        csv_file = asyncio.run(run_real_time_prediction(duration))
        
        if csv_file is None:
            print("❌ 实时预测失败，无法继续")
            return
        
        # 等待一下确保文件写入完成
        time.sleep(1)
        
        # 第二步：运行绘图分析
        success = run_plot_analysis(csv_file, time_threshold)
        
        if success:
            print("\n🎉 完整流程执行成功！")
            print("📊 请查看生成的PNG图表文件")
        else:
            print("\n⚠️ 预测成功但绘图分析失败")
            print(f"💡 您可以手动运行: python plot_saved_data.py")
            print(f"   来分析数据文件: {csv_file}")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 执行过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
