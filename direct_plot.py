"""
直接绘制位移预测数据的简单工具
避免复杂的数据处理，直接读取和绘图
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def direct_plot(filename="real_time_predictions_1762248437.csv", time_threshold=0.0001):
    """直接绘制数据"""
    print(f"📊 直接分析文件: {filename}")
    print(f"🔍 时间过滤阈值: {time_threshold} 秒")
    
    try:
        # 直接读取CSV
        data = pd.read_csv(filename)
        print(f"✅ 读取 {len(data)} 行数据")
        print(f"📋 列名: {list(data.columns)}")
        
        # 显示数据范围
        print(f"📊 数据范围:")
        print(f"   时间: {data['time'].min():.6f} - {data['time'].max():.6f} 秒")
        print(f"   真实值: {data['real_value'].min():.6f} - {data['real_value'].max():.6f} μm")
        print(f"   预测值: {data['prediction'].min():.6f} - {data['prediction'].max():.6f} μm")
        
        # 检查异常值
        pred_range = data['prediction'].max() - data['prediction'].min()
        real_range = data['real_value'].max() - data['real_value'].min()
        print(f"📊 数据质量:")
        print(f"   预测值范围: {pred_range:.2f} μm")
        print(f"   真实值范围: {real_range:.2f} μm")
        
        if pred_range > 1000:  # 如果预测值范围过大
            print("⚠️ 检测到异常的预测值，可能是模型初始化问题")
        
        # 时间过滤
        mask = data['time'] > time_threshold
        filtered_data = data[mask].copy()
        
        print(f"🔍 时间过滤结果:")
        print(f"   过滤前: {len(data)} 行")
        print(f"   过滤后: {len(filtered_data)} 行")
        print(f"   移除: {len(data) - len(filtered_data)} 行")
        
        if len(filtered_data) == 0:
            print("❌ 过滤后没有数据")
            return
        
        # 重置时间
        filtered_data = filtered_data.reset_index(drop=True)
        time_offset = filtered_data['time'].min()
        filtered_data['time'] = filtered_data['time'] - time_offset
        
        # 重新计算误差
        filtered_data['error'] = filtered_data['prediction'] - filtered_data['real_value']
        
        # 提取数组
        times = filtered_data['time'].values
        real_values = filtered_data['real_value'].values
        predictions = filtered_data['prediction'].values
        errors = filtered_data['error'].values
        
        # 计算统计指标
        mae = np.mean(np.abs(errors))
        rmse = np.sqrt(np.mean(errors**2))
        
        print(f"📊 过滤后统计:")
        print(f"   时间范围: {times.min():.6f} - {times.max():.6f} 秒")
        print(f"   真实值范围: {real_values.min():.6f} - {real_values.max():.6f} μm")
        print(f"   预测值范围: {predictions.min():.6f} - {predictions.max():.6f} μm")
        print(f"   平均绝对误差: {mae:.6f} μm")
        print(f"   均方根误差: {rmse:.6f} μm")
        
        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'位移预测分析 (过滤阈值: {time_threshold}s)', fontsize=16, fontweight='bold')
        
        # 1. 位移对比图
        ax1 = axes[0, 0]
        ax1.plot(times, real_values, 'b-', linewidth=1.5, label='实际位移', alpha=0.8)
        ax1.plot(times, predictions, 'r-', linewidth=1.5, label='预测位移', alpha=0.8)
        ax1.set_title('实际位移 vs 预测位移')
        ax1.set_xlabel('时间 (秒)')
        ax1.set_ylabel('位移 (μm)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 添加统计信息
        try:
            correlation = np.corrcoef(real_values, predictions)[0, 1]
        except:
            correlation = 0.0
        
        stats_text = f'MAE: {mae:.6f}μm\nRMSE: {rmse:.6f}μm\n相关系数: {correlation:.6f}\n数据点: {len(filtered_data)}'
        ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, 
                 verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        
        # 2. 误差时间序列
        ax2 = axes[0, 1]
        ax2.plot(times, errors, 'g-', linewidth=1, alpha=0.7, label='预测误差')
        ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax2.axhline(y=np.mean(errors), color='red', linestyle='--', alpha=0.7, 
                    label=f'平均误差: {np.mean(errors):.6f}μm')
        ax2.fill_between(times, errors, 0, alpha=0.3, color='green')
        ax2.set_title('预测误差随时间变化')
        ax2.set_xlabel('时间 (秒)')
        ax2.set_ylabel('误差 (μm)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 误差分布直方图
        ax3 = axes[1, 0]
        ax3.hist(errors, bins=50, alpha=0.7, color='orange', edgecolor='black', density=True)
        ax3.axvline(x=np.mean(errors), color='red', linestyle='--', linewidth=2, 
                    label=f'平均: {np.mean(errors):.6f}μm')
        ax3.axvline(x=np.median(errors), color='blue', linestyle='--', linewidth=2, 
                    label=f'中位数: {np.median(errors):.6f}μm')
        ax3.axvline(x=0, color='black', linestyle='-', alpha=0.5)
        ax3.set_title('误差分布直方图')
        ax3.set_xlabel('误差 (μm)')
        ax3.set_ylabel('概率密度')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 散点图 (预测 vs 实际)
        ax4 = axes[1, 1]
        
        # 如果数据点太多，进行采样
        if len(real_values) > 5000:
            indices = np.random.choice(len(real_values), 5000, replace=False)
            scatter_real = real_values[indices]
            scatter_pred = predictions[indices]
            scatter_errors = np.abs(errors[indices])
        else:
            scatter_real = real_values
            scatter_pred = predictions
            scatter_errors = np.abs(errors)
        
        scatter = ax4.scatter(scatter_real, scatter_pred, alpha=0.6, s=10, 
                             c=scatter_errors, cmap='viridis')
        
        # 添加理想预测线 (y=x)
        min_val = min(np.min(scatter_real), np.min(scatter_pred))
        max_val = max(np.max(scatter_real), np.max(scatter_pred))
        ax4.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='理想预测线')
        
        ax4.set_title('预测值 vs 实际值散点图')
        ax4.set_xlabel('实际位移 (μm)')
        ax4.set_ylabel('预测位移 (μm)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=ax4)
        cbar.set_label('绝对误差 (μm)')
        
        plt.tight_layout()
        
        # 保存图表
        base_name = os.path.splitext(filename)[0]
        plot_filename = f"{base_name}_direct_analysis_{time_threshold}s.png"
        plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
        print(f"📊 分析图表已保存到: {plot_filename}")
        
        # 保存过滤后的数据
        output_filename = f"{base_name}_direct_filtered_{time_threshold}s.csv"
        filtered_data.to_csv(output_filename, index=False)
        print(f"💾 过滤后数据已保存到: {output_filename}")
        
        # 显示图表
        plt.show()
        
        # 打印详细统计
        print(f"\n📊 详细统计信息:")
        print(f"   数据点数: {len(filtered_data)}")
        print(f"   时间跨度: {times.max() - times.min():.6f} 秒")
        print(f"   采样频率: {len(filtered_data)/(times.max() - times.min()):.1f} Hz")
        print(f"   位移变化幅度: {real_values.max() - real_values.min():.6f} μm")
        print(f"   预测变化幅度: {predictions.max() - predictions.min():.6f} μm")
        print(f"   误差标准差: {np.std(errors):.6f} μm")
        print(f"   最大正误差: {np.max(errors):.6f} μm")
        print(f"   最大负误差: {np.min(errors):.6f} μm")
        
        # 误差百分位数
        percentiles = [5, 25, 50, 75, 95]
        print(f"\n📊 误差百分位数:")
        for p in percentiles:
            value = np.percentile(np.abs(errors), p)
            print(f"   {p}%: {value:.6f} μm")
        
        return filtered_data
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("📊 直接位移预测数据分析工具")
    print("="*50)
    
    # 获取参数
    filename = input("请输入CSV文件名（默认: real_time_predictions_1762248437.csv）: ").strip()
    if not filename:
        filename = "real_time_predictions_1762248437.csv"
    
    time_threshold_input = input("请输入时间过滤阈值（秒，默认0.0001）: ").strip()
    try:
        time_threshold = float(time_threshold_input) if time_threshold_input else 0.0001
    except ValueError:
        time_threshold = 0.0001
        print(f"⚠️ 输入无效，使用默认值: {time_threshold} 秒")
    
    # 检查文件
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        
        # 查找其他文件
        import glob
        csv_files = glob.glob("real_time_predictions_*.csv")
        if csv_files:
            csv_files.sort(key=os.path.getmtime, reverse=True)
            filename = csv_files[0]
            print(f"🔍 使用最新文件: {filename}")
        else:
            print("❌ 未找到任何CSV文件")
            return
    
    # 分析数据
    result = direct_plot(filename, time_threshold)
    
    if result is not None:
        print("✅ 分析完成！")
    else:
        print("❌ 分析失败！")

if __name__ == "__main__":
    main()
