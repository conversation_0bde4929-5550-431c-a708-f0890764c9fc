#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LSTM 模型 GPU 加速实现
包含完整的 GPU 加速代码示例
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Tuple, Optional
import time


# ============================================================================
# 第 1 部分：环境检查
# ============================================================================

def check_gpu_environment():
    """检查 GPU 环境"""
    print("=" * 70)
    print("GPU 环境检查")
    print("=" * 70)
    
    # 检查 CUDA 可用性
    cuda_available = torch.cuda.is_available()
    print(f"✓ CUDA 可用: {cuda_available}")
    
    if cuda_available:
        print(f"✓ CUDA 版本: {torch.version.cuda}")
        print(f"✓ GPU 数量: {torch.cuda.device_count()}")
        
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            print(f"✓ GPU {i}: {props.name}")
            print(f"  - 显存: {props.total_memory / 1e9:.2f} GB")
            print(f"  - 计算能力: {props.major}.{props.minor}")
    
    print(f"✓ PyTorch 版本: {torch.__version__}")
    print()
    
    return cuda_available


def get_device() -> torch.device:
    """获取最优设备"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    return device


# ============================================================================
# 第 2 部分：模型定义（支持 GPU）
# ============================================================================

class DisplacementLSTM(nn.Module):
    """位移预测 LSTM 模型（GPU 优化版）"""
    
    def __init__(self, input_size=1, hidden_size=96, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.linear = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.1)
    
    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        out = lstm_out[:, -1, :]
        out = self.dropout(out)
        prediction = self.linear(out)
        return prediction


# ============================================================================
# 第 3 部分：GPU 加速训练
# ============================================================================

def train_with_gpu(
    model: nn.Module,
    X: torch.Tensor,
    y: torch.Tensor,
    epochs: int = 100,
    learning_rate: float = 0.008,
    device: torch.device = None
) -> Tuple[nn.Module, list]:
    """
    使用 GPU 加速训练模型
    
    Args:
        model: LSTM 模型
        X: 训练输入数据
        y: 训练目标数据
        epochs: 训练轮数
        learning_rate: 学习率
        device: 计算设备
    
    Returns:
        tuple: (训练好的模型, 损失历史)
    """
    if device is None:
        device = get_device()
    
    print("=" * 70)
    print("GPU 加速训练")
    print("=" * 70)
    
    # 1. 模型移到 GPU
    model = model.to(device)
    print(f"✓ 模型已移到 {device}")
    
    # 2. 数据移到 GPU
    X = X.to(device)
    y = y.to(device)
    print(f"✓ 数据已移到 {device}")
    print(f"  - 输入形状: {X.shape}")
    print(f"  - 目标形状: {y.shape}")
    
    # 3. 设置优化器和损失函数
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
    
    # 4. 训练循环
    loss_history = []
    start_time = time.time()
    
    print(f"\n开始训练（{epochs} 个 epoch）...")
    for epoch in range(epochs):
        model.train()
        optimizer.zero_grad()
        
        # 前向传播（在 GPU 上）
        outputs = model(X).squeeze()
        loss = criterion(outputs, y)
        
        # 反向传播
        loss.backward()
        optimizer.step()
        
        loss_history.append(loss.item())
        
        if epoch % 25 == 0:
            print(f'Epoch {epoch:3d}: Loss = {loss.item():.6f}')
    
    elapsed_time = time.time() - start_time
    print(f"\n✓ 训练完成！耗时: {elapsed_time:.2f}s")
    print(f"✓ 最终损失: {loss_history[-1]:.6f}")
    
    # 5. 模型移回 CPU（可选）
    model = model.cpu()
    print(f"✓ 模型已移回 CPU")
    
    return model, loss_history


# ============================================================================
# 第 4 部分：GPU 加速推理
# ============================================================================

class GPUAcceleratedPredictor:
    """GPU 加速预测器"""
    
    def __init__(self, model: nn.Module, scaler, window_size: int = 25):
        """
        初始化预测器
        
        Args:
            model: LSTM 模型
            scaler: 数据标准化器
            window_size: 窗口大小
        """
        self.device = get_device()
        self.model = model.to(self.device)
        self.model.eval()
        self.scaler = scaler
        self.window_size = window_size
        self.data_buffer = []
    
    def predict_single(self, value: float) -> float:
        """
        单点预测（GPU 加速）
        
        Args:
            value: 输入值
        
        Returns:
            float: 预测值
        """
        # 标准化
        normalized = self.scaler.transform([[value]])[0, 0]
        self.data_buffer.append(normalized)
        
        if len(self.data_buffer) > self.window_size:
            self.data_buffer.pop(0)
        
        if len(self.data_buffer) < self.window_size:
            return None
        
        # 准备输入
        input_seq = torch.FloatTensor(self.data_buffer).unsqueeze(0).unsqueeze(-1)
        input_seq = input_seq.to(self.device)
        
        # 推理
        with torch.no_grad():
            normalized_pred = self.model(input_seq).item()
        
        # 反标准化
        prediction = self.scaler.inverse_transform([[normalized_pred]])[0, 0]
        return prediction
    
    def predict_batch(self, values: np.ndarray) -> np.ndarray:
        """
        批量预测（更快）
        
        Args:
            values: 输入值数组
        
        Returns:
            np.ndarray: 预测值数组
        """
        predictions = []
        
        for value in values:
            pred = self.predict_single(value)
            if pred is not None:
                predictions.append(pred)
        
        return np.array(predictions)


# ============================================================================
# 第 5 部分：性能对比
# ============================================================================

def benchmark_gpu_vs_cpu(
    model: nn.Module,
    X: torch.Tensor,
    num_iterations: int = 100
):
    """
    对比 GPU 和 CPU 的性能
    
    Args:
        model: LSTM 模型
        X: 测试数据
        num_iterations: 迭代次数
    """
    print("=" * 70)
    print("性能对比：GPU vs CPU")
    print("=" * 70)
    
    model.eval()
    
    # CPU 推理
    print("\n📊 CPU 推理...")
    model_cpu = model.cpu()
    X_cpu = X.cpu()
    
    torch.cuda.synchronize() if torch.cuda.is_available() else None
    start_time = time.time()
    
    with torch.no_grad():
        for _ in range(num_iterations):
            _ = model_cpu(X_cpu)
    
    cpu_time = time.time() - start_time
    cpu_avg = cpu_time / num_iterations * 1000  # 转换为 ms
    print(f"✓ CPU 总耗时: {cpu_time:.3f}s")
    print(f"✓ CPU 平均耗时: {cpu_avg:.3f}ms")
    
    # GPU 推理
    if torch.cuda.is_available():
        print("\n📊 GPU 推理...")
        device = torch.device("cuda")
        model_gpu = model.to(device)
        X_gpu = X.to(device)
        
        torch.cuda.synchronize()
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(num_iterations):
                _ = model_gpu(X_gpu)
        
        torch.cuda.synchronize()
        gpu_time = time.time() - start_time
        gpu_avg = gpu_time / num_iterations * 1000  # 转换为 ms
        print(f"✓ GPU 总耗时: {gpu_time:.3f}s")
        print(f"✓ GPU 平均耗时: {gpu_avg:.3f}ms")
        
        # 计算加速比
        speedup = cpu_avg / gpu_avg
        print(f"\n🚀 加速比: {speedup:.1f}x")
        print(f"📈 性能提升: {(1 - gpu_avg/cpu_avg) * 100:.1f}%")
    else:
        print("\n⚠️  GPU 不可用")


# ============================================================================
# 第 6 部分：主程序示例
# ============================================================================

if __name__ == "__main__":
    print("\n" + "=" * 70)
    print("LSTM GPU 加速完整示例")
    print("=" * 70 + "\n")
    
    # 1. 检查 GPU 环境
    cuda_available = check_gpu_environment()
    device = get_device()
    
    # 2. 创建示例数据
    print("=" * 70)
    print("创建示例数据")
    print("=" * 70)
    
    batch_size = 32
    window_size = 25
    num_samples = 1000
    
    X = torch.randn(num_samples, window_size, 1)
    y = torch.randn(num_samples)
    
    print(f"✓ 输入形状: {X.shape}")
    print(f"✓ 目标形状: {y.shape}")
    print()
    
    # 3. 创建模型
    print("=" * 70)
    print("创建模型")
    print("=" * 70)
    
    model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"✓ 模型参数数量: {total_params}")
    print()
    
    # 4. GPU 加速训练
    model, loss_history = train_with_gpu(
        model=model,
        X=X,
        y=y,
        epochs=50,
        learning_rate=0.008,
        device=device
    )
    print()
    
    # 5. 性能对比
    benchmark_gpu_vs_cpu(model, X[:batch_size], num_iterations=100)
    print()
    
    # 6. GPU 加速推理示例
    print("=" * 70)
    print("GPU 加速推理示例")
    print("=" * 70)
    
    from sklearn.preprocessing import MinMaxScaler
    scaler = MinMaxScaler(feature_range=(-1, 1))
    scaler.fit(np.random.randn(1000, 1))
    
    predictor = GPUAcceleratedPredictor(model, scaler, window_size=25)
    
    # 单点预测
    test_values = np.random.randn(10)
    print(f"\n单点预测示例:")
    for i, val in enumerate(test_values):
        pred = predictor.predict_single(val)
        if pred is not None:
            print(f"  输入: {val:.4f} → 预测: {pred:.4f}")
    
    print("\n✅ 所有示例完成！")

