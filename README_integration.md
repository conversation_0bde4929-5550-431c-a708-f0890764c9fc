# DSA数据收集器与实时可视化集成

## 概述

本项目实现了 `data_collector_3s.py` 与 `real_time_visualization_demo.py` 的集成，使DSA数据收集器的实时位移和速度数据能够作为LSTM+AKF预测系统的输入。

## 文件结构

### 核心文件
- `data_collector_3s.py` - 修改后的DSA数据收集器，支持实时数据流
- `real_time_data_bridge.py` - 数据桥接器，连接DSA收集器和可视化系统
- `integrated_real_time_demo.py` - 集成的实时可视化演示程序
- `test_integration.py` - 集成测试脚本

### 依赖文件
- `real_time_stream_processor.py` - 实时数据流处理器
- `real_time_visualization_demo.py` - 原始可视化演示程序

## 主要功能

### 1. 实时数据流支持
- DSA数据收集器现在支持4000Hz的实时数据流输出
- 位移和速度数据分别缓存到独立队列
- 非阻塞数据读取接口

### 2. 数据桥接
- `DSADataSource` 类适配DSA收集器为标准数据源接口
- 支持位移和速度数据的独立访问
- 自动处理数据类型过滤和队列管理

### 3. 实时预测
- 使用LSTM+AKF模型对DSA实时数据进行预测
- 支持4000Hz高频数据处理
- 实时性能监控和统计

## 使用方法

### 1. 环境准备
确保以下文件存在：
```
SDK发布20200723/x64/DSANetSDK.dll  # DSA SDK动态库
v1.txt                              # 训练数据（用于LSTM模型训练）
```

### 2. 测试集成
首先运行集成测试：
```bash
python test_integration.py
```

测试内容：
- DSA数据收集器启动测试
- 实时数据读取测试
- 位移和速度数据同时收集测试

### 3. 运行完整演示
```bash
python integrated_real_time_demo.py
```

演示流程：
1. 使用v1.txt训练LSTM模型
2. 启动DSA数据收集器
3. 收集初始化数据（25个点）
4. 开始实时预测（2000个点）
5. 生成可视化图表和统计报告

## 输出文件

### 可视化图表
- `dsa_real_time_visualization.png` - 实时预测效果图表
  - 真实值vs预测值对比
  - 预测误差分析
  - 处理性能统计
  - 误差分布直方图

### 数据文件
- `dsa_real_time_predictions.csv` - 预测结果数据
  - 时间戳
  - DSA真实位移值
  - LSTM+AKF预测值
  - 处理时间

## 技术特点

### 1. 高频数据处理
- 支持4000Hz采样率
- 平均处理时间 < 1ms
- 实时队列缓存机制

### 2. 数据质量保证
- 异常值检测和处理
- 数据类型自动识别
- 队列溢出保护

### 3. 性能监控
- 实时处理时间统计
- 数据丢失监控
- 队列使用率监控

## 配置参数

### DSA数据收集器
```python
target_points_per_second = 4000  # 每秒采样点数
total_duration = 3               # 运行时长（秒）
real_time_queue_size = 10000     # 实时队列大小
```

### 实时处理器
```python
window_size = 25                 # LSTM窗口大小
buffer_size = 10000             # 数据缓冲区大小
sampling_interval = 0.00025     # 采样间隔（4000Hz）
```

## 故障排除

### 1. DLL文件未找到
确保 `SDK发布20200723/x64/DSANetSDK.dll` 文件存在且路径正确。

### 2. 训练数据缺失
确保 `v1.txt` 文件存在，用于LSTM模型训练。

### 3. 数据收集失败
- 检查DSA设备连接
- 确认网络配置正确
- 验证设备权限

### 4. 预测精度低
- 增加训练数据量
- 调整LSTM模型参数
- 优化卡尔曼滤波器参数

## 性能指标

### 典型性能
- 处理速率: >1000 次/秒
- 平均延迟: <1ms
- 10%准确率: >85%
- MAE: <0.001μm

### 系统要求
- Python 3.7+
- PyTorch
- NumPy, Pandas
- Matplotlib
- scikit-learn

## 扩展功能

### 1. 多设备支持
可以扩展支持多个DSA设备同时采集。

### 2. 数据存储
可以添加数据库存储功能，保存历史数据。

### 3. Web界面
可以开发Web界面进行远程监控。

### 4. 报警系统
可以添加异常检测和报警功能。

## 联系信息

如有问题或建议，请联系开发团队。
