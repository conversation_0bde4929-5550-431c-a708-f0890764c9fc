"""
测试 error_comparison_over_time 图表更新
验证新增的位移对比图是否正确生成
"""

import numpy as np
import matplotlib.pyplot as plt
import sys

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
except:
    plt.rcParams['axes.unicode_minus'] = False

# 导入函数
from integrated_real_time_demo import create_error_comparison_figures

def generate_test_records(num_points=500):
    """生成测试数据记录"""
    times = np.linspace(0, 3, num_points)
    
    # 生成真实位移（模拟正弦波）
    displacements = 10 + 5 * np.sin(2 * np.pi * times / 3) + np.random.normal(0, 0.1, num_points)
    
    # 生成纠偏后LSTM预测（有一定偏差）
    corrected_predictions = 10 + 4.8 * np.sin(2 * np.pi * times / 3 + 0.1) + np.random.normal(0, 0.15, num_points)
    
    # 生成LSTM+AKF预测（更接近真实值）
    akf_predictions = 10 + 4.95 * np.sin(2 * np.pi * times / 3 + 0.05) + np.random.normal(0, 0.08, num_points)
    
    records = []
    for i in range(num_points):
        displacement = displacements[i]
        corrected_pred = corrected_predictions[i]
        akf_pred = akf_predictions[i]
        
        corrected_error = abs(displacement - corrected_pred)
        akf_error = abs(displacement - akf_pred)
        
        record = {
            'time': times[i],
            'displacement': displacement,
            'velocity': 0.0,
            'prediction': akf_pred,  # AKF后的预测
            'lstm_prediction': None,
            'corrected_prediction': corrected_pred,
            'lstm_error': None,
            'corrected_error': corrected_error,
            'akf_error': akf_error,
            'error_improvement': None,
            'error_improvement_pct': None,
            'akf_vs_corrected_improvement': corrected_error - akf_error,
            'akf_vs_corrected_improvement_pct': (corrected_error - akf_error) / corrected_error * 100.0 if corrected_error > 0 else 0.0,
            'processing_time': 0.001
        }
        records.append(record)
    
    return records

def test_error_comparison_figures():
    """测试误差对比图表生成"""
    print("=" * 60)
    print("测试 error_comparison_over_time 图表更新")
    print("=" * 60)
    
    # 生成测试数据
    print("\n📊 生成测试数据...")
    records = generate_test_records(num_points=500)
    print(f"✅ 生成了 {len(records)} 个测试数据点")
    
    # 生成图表
    print("\n📈 生成误差对比图表...")
    try:
        over_time_png, hist_png = create_error_comparison_figures(records, save_prefix="test_error_comparison")
        
        if over_time_png and hist_png:
            print(f"✅ 成功生成图表:")
            print(f"   - {over_time_png}")
            print(f"   - {hist_png}")
            print("\n📋 图表内容说明:")
            print("   error_comparison_over_time.png 包含3行图表:")
            print("   1. 第一行：位移对比（真实位移 vs 纠偏后LSTM预测 vs LSTM+AKF预测）")
            print("   2. 第二行：误差对比（纠偏后LSTM误差 vs LSTM+AKF误差）")
            print("   3. 第三行：改进百分比（AKF相对纠偏后LSTM的逐点改进）")
            print("\n   error_comparison_hist.png 包含:")
            print("   - 误差分布直方图（纠偏后LSTM vs LSTM+AKF）")
            print("   - 关键指标统计信息")
            return True
        else:
            print("❌ 图表生成失败")
            return False
    except Exception as e:
        print(f"❌ 生成图表时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_error_comparison_figures()
    sys.exit(0 if success else 1)

