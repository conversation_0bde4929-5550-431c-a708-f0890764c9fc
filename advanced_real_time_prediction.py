"""
高级实时预测系统
支持同时处理位移和速度数据，每次DSA回调都进行预测
"""

import asyncio
import time
import numpy as np
import pandas as pd
import torch
import os
import matplotlib.pyplot as plt
from collections import deque
from data_collector_3s import DSADataCollector
from real_time_stream_processor import RealTimeStreamProcessor
from real_time_train import train_displacement_model

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class AdvancedRealTimePredictionSystem:
    """高级实时预测系统"""
    
    def __init__(self, dll_path):
        self.dll_path = dll_path
        
        # 预测器
        self.displacement_predictor = None
        self.velocity_predictor = None
        
        # 数据收集器
        self.displacement_collector = None
        self.velocity_collector = None
        
        # 实时数据存储
        self.displacement_data = deque(maxlen=10000)
        self.velocity_data = deque(maxlen=10000)
        self.combined_data = deque(maxlen=10000)
        
        # 统计信息
        self.displacement_predictions = 0
        self.velocity_predictions = 0
        self.start_time = None
        
        # 同步机制
        self.latest_displacement = None
        self.latest_velocity = None
        self.latest_displacement_time = None
        self.latest_velocity_time = None
    
    def displacement_callback(self, real_value, prediction, timestamp, data_type):
        """位移预测回调"""
        if self.start_time is None:
            self.start_time = timestamp
            print("🚀 开始接收位移预测...")
        
        relative_time = timestamp - self.start_time
        error = abs(real_value - prediction)
        
        # 存储位移数据
        displacement_record = {
            'time': relative_time,
            'real_displacement': real_value,
            'pred_displacement': prediction,
            'displacement_error': error,
            'timestamp': timestamp
        }
        self.displacement_data.append(displacement_record)
        
        # 更新最新位移数据
        self.latest_displacement = real_value
        self.latest_displacement_time = timestamp
        
        self.displacement_predictions += 1
        
        # 尝试创建组合记录
        self._create_combined_record(displacement_record, None)
        
        # 每50个预测显示一次
        if self.displacement_predictions % 50 == 0:
            print(f"📍 位移预测#{self.displacement_predictions}: t={relative_time:.3f}s, "
                  f"真实={real_value:.6f}μm, 预测={prediction:.6f}μm, 误差={error:.6f}μm")
    
    def velocity_callback(self, real_value, prediction, timestamp, data_type):
        """速度预测回调"""
        if self.start_time is None:
            self.start_time = timestamp
            print("🚀 开始接收速度预测...")
        
        relative_time = timestamp - self.start_time
        error = abs(real_value - prediction)
        
        # 存储速度数据
        velocity_record = {
            'time': relative_time,
            'real_velocity': real_value,
            'pred_velocity': prediction,
            'velocity_error': error,
            'timestamp': timestamp
        }
        self.velocity_data.append(velocity_record)
        
        # 更新最新速度数据
        self.latest_velocity = real_value
        self.latest_velocity_time = timestamp
        
        self.velocity_predictions += 1
        
        # 尝试创建组合记录
        self._create_combined_record(None, velocity_record)
        
        # 每50个预测显示一次
        if self.velocity_predictions % 50 == 0:
            print(f"🏃 速度预测#{self.velocity_predictions}: t={relative_time:.3f}s, "
                  f"真实={real_value:.6f}μm/s, 预测={prediction:.6f}μm/s, 误差={error:.6f}μm/s")
    
    def _create_combined_record(self, displacement_record, velocity_record):
        """创建组合记录（当位移和速度数据都可用时）"""
        # 简单的时间同步策略：使用最新的数据
        if displacement_record is not None:
            current_time = displacement_record['timestamp']
            # 如果有最近的速度数据（1秒内），创建组合记录
            if (self.latest_velocity is not None and 
                self.latest_velocity_time is not None and
                abs(current_time - self.latest_velocity_time) < 1.0):
                
                combined_record = {
                    'time': displacement_record['time'],
                    'real_displacement': displacement_record['real_displacement'],
                    'pred_displacement': displacement_record['pred_displacement'],
                    'real_velocity': self.latest_velocity,
                    'pred_velocity': None,  # 需要速度预测器
                    'timestamp': current_time
                }
                self.combined_data.append(combined_record)
        
        elif velocity_record is not None:
            current_time = velocity_record['timestamp']
            # 如果有最近的位移数据（1秒内），创建组合记录
            if (self.latest_displacement is not None and 
                self.latest_displacement_time is not None and
                abs(current_time - self.latest_displacement_time) < 1.0):
                
                combined_record = {
                    'time': velocity_record['time'],
                    'real_displacement': self.latest_displacement,
                    'pred_displacement': None,  # 需要位移预测器
                    'real_velocity': velocity_record['real_velocity'],
                    'pred_velocity': velocity_record['pred_velocity'],
                    'timestamp': current_time
                }
                self.combined_data.append(combined_record)
    
    async def initialize_predictors(self):
        """初始化预测器"""
        print("🔧 初始化预测器...")
        
        # 检查训练数据
        if not os.path.exists('v1.txt'):
            print("❌ 未找到训练数据文件 v1.txt")
            return False
        
        # 训练位移预测模型
        print("🚀 训练位移预测模型...")
        displacement_model, displacement_scaler = train_displacement_model(
            data_file='v1.txt',
            window_size=25,
            hidden_size=96,
            learning_rate=0.001,
            epochs=80,
            train_points=2000
        )
        
        if displacement_model is None or displacement_scaler is None:
            print("❌ 位移模型训练失败")
            return False
        
        # 创建位移预测器
        self.displacement_predictor = RealTimeStreamProcessor(
            model=displacement_model,
            scaler=displacement_scaler,
            window_size=25,
            enable_akf=True,
            enable_bias_correction=True
        )
        
        print("✅ 位移预测器创建完成")
        
        # 注意：这里可以扩展为训练专门的速度预测模型
        # 目前使用相同的模型结构
        print("✅ 预测器初始化完成")
        return True
    
    async def start_dual_prediction(self, duration_seconds=30):
        """启动双重实时预测（位移+速度）"""
        print("="*60)
        print("🎯 高级实时预测系统")
        print("同时处理位移和速度数据的实时预测")
        print("="*60)
        
        # 1. 初始化预测器
        if not await self.initialize_predictors():
            return False
        
        # 2. 创建位移数据收集器
        print("🔧 创建位移数据收集器...")
        self.displacement_collector = DSADataCollector(self.dll_path)
        self.displacement_collector.enable_continuous_mode()
        self.displacement_collector.enable_real_time_streaming()
        
        # 3. 收集初始化数据
        print("📊 收集位移初始化数据...")
        if not await self._initialize_displacement_predictor():
            return False
        
        # 4. 设置位移实时预测
        self.displacement_collector.set_real_time_predictor(self.displacement_predictor)
        self.displacement_collector.set_prediction_callback(self.displacement_callback)
        
        # 5. 启动位移数据收集
        if not self.displacement_collector.initialize_sdk():
            print("❌ 位移SDK初始化失败")
            return False
        
        # 设置为位移数据类型
        self.displacement_collector.sdk.setOutDataType(
            self.displacement_collector.OutDataType['odtDisplacement']
        )
        
        if not self.displacement_collector.start_collection():
            print("❌ 位移数据收集启动失败")
            return False
        
        print(f"🚀 开始{duration_seconds}秒高级实时预测...")
        print("每次DSA回调都会触发相应的预测")
        
        # 6. 等待指定时间
        try:
            await asyncio.sleep(duration_seconds)
        except KeyboardInterrupt:
            print("⚠️ 用户中断预测")
        
        # 7. 停止收集
        print("🛑 停止数据收集...")
        if self.displacement_collector:
            self.displacement_collector.stop_collection()
        
        # 8. 生成报告
        await self._generate_advanced_report()
        
        return True
    
    async def _initialize_displacement_predictor(self):
        """初始化位移预测器"""
        if not self.displacement_collector.initialize_sdk():
            print("❌ 临时位移收集器初始化失败")
            return False
        
        if not self.displacement_collector.start_collection():
            print("❌ 临时位移数据收集启动失败")
            return False
        
        # 收集25个位移数据点
        historical_data = []
        timeout_count = 0
        max_timeout = 100
        
        while len(historical_data) < 25 and timeout_count < max_timeout:
            data = self.displacement_collector.get_real_time_data_by_type('displacement', timeout=0.1)
            if data is not None:
                historical_data.append(data['value'])
                if len(historical_data) % 5 == 0:
                    print(f"收集位移初始化数据: {len(historical_data)}/25")
            else:
                timeout_count += 1
                await asyncio.sleep(0.1)
        
        self.displacement_collector.stop_collection()
        
        if len(historical_data) < 25:
            print(f"❌ 位移初始化数据不足: {len(historical_data)}/25")
            return False
        
        # 初始化位移预测器
        self.displacement_predictor.initialize_with_historical_data(historical_data)
        print("✅ 位移预测器初始化完成")
        return True
    
    async def _generate_advanced_report(self):
        """生成高级预测报告"""
        print("\n" + "="*60)
        print("📊 高级实时预测统计报告")
        print("="*60)
        
        # 位移预测统计
        if len(self.displacement_data) > 0:
            displacement_errors = [d['displacement_error'] for d in self.displacement_data]
            displacement_times = [d['time'] for d in self.displacement_data]
            
            total_time = max(displacement_times) - min(displacement_times) if len(displacement_times) > 1 else 0
            displacement_rate = len(self.displacement_data) / total_time if total_time > 0 else 0
            
            print(f"📍 位移预测统计:")
            print(f"   预测次数: {len(self.displacement_data)}")
            print(f"   预测频率: {displacement_rate:.1f} Hz")
            print(f"   平均误差: {np.mean(displacement_errors):.6f} μm")
            print(f"   最大误差: {np.max(displacement_errors):.6f} μm")
        
        # 速度预测统计
        if len(self.velocity_data) > 0:
            velocity_errors = [d['velocity_error'] for d in self.velocity_data]
            velocity_times = [d['time'] for d in self.velocity_data]
            
            total_time = max(velocity_times) - min(velocity_times) if len(velocity_times) > 1 else 0
            velocity_rate = len(self.velocity_data) / total_time if total_time > 0 else 0
            
            print(f"🏃 速度预测统计:")
            print(f"   预测次数: {len(self.velocity_data)}")
            print(f"   预测频率: {velocity_rate:.1f} Hz")
            print(f"   平均误差: {np.mean(velocity_errors):.6f} μm/s")
            print(f"   最大误差: {np.max(velocity_errors):.6f} μm/s")
        
        # 组合数据统计
        if len(self.combined_data) > 0:
            print(f"🔗 组合数据统计:")
            print(f"   组合记录数: {len(self.combined_data)}")
        
        # 保存数据
        self._save_advanced_data()

        # 提示用户使用plot_saved_data.py进行分析
        print("📊 数据已保存，请使用以下命令进行可视化分析:")
        print(f"   python plot_saved_data.py")
        print("   或者直接运行 plot_saved_data.py 来分析最新的CSV文件")
    
    def _save_advanced_data(self):
        """保存高级预测数据"""
        timestamp = int(time.time())
        
        # 保存位移数据
        if len(self.displacement_data) > 0:
            displacement_df = pd.DataFrame(list(self.displacement_data))
            displacement_filename = f"displacement_predictions_{timestamp}.csv"
            displacement_df.to_csv(displacement_filename, index=False, encoding='utf-8')
            print(f"💾 位移预测数据已保存到: {displacement_filename}")
        
        # 保存速度数据
        if len(self.velocity_data) > 0:
            velocity_df = pd.DataFrame(list(self.velocity_data))
            velocity_filename = f"velocity_predictions_{timestamp}.csv"
            velocity_df.to_csv(velocity_filename, index=False, encoding='utf-8')
            print(f"💾 速度预测数据已保存到: {velocity_filename}")
    
    def _create_advanced_visualization(self):
        """创建高级可视化图表"""
        if len(self.displacement_data) < 10:
            print("⚠️ 位移数据点太少，跳过可视化")
            return
        
        # 位移数据可视化
        displacement_data = list(self.displacement_data)
        times = [d['time'] for d in displacement_data]
        real_displacements = [d['real_displacement'] for d in displacement_data]
        pred_displacements = [d['pred_displacement'] for d in displacement_data]
        displacement_errors = [d['displacement_error'] for d in displacement_data]
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('DSA高级实时预测结果分析', fontsize=16, fontweight='bold')
        
        # 1. 位移预测对比
        axes[0, 0].plot(times, real_displacements, 'b-', label='真实位移', alpha=0.7)
        axes[0, 0].plot(times, pred_displacements, 'r-', label='预测位移', alpha=0.7)
        axes[0, 0].set_xlabel('时间 (秒)')
        axes[0, 0].set_ylabel('位移 (μm)')
        axes[0, 0].set_title('位移预测对比')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 2. 位移预测误差
        axes[0, 1].plot(times, displacement_errors, 'g-', alpha=0.7)
        axes[0, 1].set_xlabel('时间 (秒)')
        axes[0, 1].set_ylabel('绝对误差 (μm)')
        axes[0, 1].set_title('位移预测误差')
        axes[0, 1].grid(True)
        
        # 3. 误差分布
        axes[1, 0].hist(displacement_errors, bins=30, alpha=0.7, color='orange')
        axes[1, 0].set_xlabel('绝对误差 (μm)')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].set_title('位移误差分布')
        axes[1, 0].grid(True)
        
        # 4. 预测散点图
        axes[1, 1].scatter(real_displacements, pred_displacements, alpha=0.6, s=10)
        min_val = min(min(real_displacements), min(pred_displacements))
        max_val = max(max(real_displacements), max(pred_displacements))
        axes[1, 1].plot([min_val, max_val], [min_val, max_val], 'r--', label='理想预测线')
        axes[1, 1].set_xlabel('真实位移 (μm)')
        axes[1, 1].set_ylabel('预测位移 (μm)')
        axes[1, 1].set_title('位移预测散点图')
        axes[1, 1].legend()
        axes[1, 1].grid(True)
        
        plt.tight_layout()

        # 图表生成已禁用
        plt.close()
        print("📊 高级可视化图表生成已禁用，避免生成额外文件")

async def main():
    """主函数"""
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    # 检查DLL文件
    if not os.path.exists(dll_path):
        print(f"❌ 找不到DLL文件: {dll_path}")
        return
    
    # 创建高级实时预测系统
    system = AdvancedRealTimePredictionSystem(dll_path)
    
    # 启动双重实时预测
    success = await system.start_dual_prediction(duration_seconds=30)
    
    if success:
        print("✅ 高级实时预测完成")
    else:
        print("❌ 高级实时预测失败")

if __name__ == "__main__":
    print("🎯 DSA高级实时回调预测系统")
    print("支持位移和速度数据的同时实时预测")
    asyncio.run(main())
