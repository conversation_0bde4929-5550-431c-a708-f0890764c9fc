"""
分析实际采样率的真正瓶颈
"""

import numpy as np
import time

print("="*70)
print("实际采样率分析")
print("="*70)

# 读取数据
try:
    with open('three_seconds_data.txt', 'r', encoding='utf-8') as f:
        lines = f.readlines()

    # 跳过标题行
    header = lines[0].strip().split('\t')
    print(f"\n✅ 成功读取数据文件")
    print(f"   总数据行: {len(lines) - 1}")
    print(f"   列名: {header}")

    # 解析数据
    times = []
    processing_times = []

    for line in lines[1:]:
        parts = line.strip().split('\t')
        if len(parts) >= 2:
            try:
                time_val = float(parts[0])
                times.append(time_val)
                if len(parts) >= 10:  # 耗时在第10列
                    proc_time = float(parts[9])
                    processing_times.append(proc_time)
            except:
                pass

    times = np.array(times)
    processing_times = np.array(processing_times)

except Exception as e:
    print(f"❌ 读取失败: {e}")
    exit(1)

# 分析时间戳
if len(times) > 1:
    time_diffs = np.diff(times)

    print(f"\n📊 时间戳分析:")
    print(f"   开始时间: {times[0]:.3f}s")
    print(f"   结束时间: {times[-1]:.3f}s")
    print(f"   总时长: {times[-1] - times[0]:.3f}s")
    print(f"   平均间隔: {np.mean(time_diffs)*1000:.3f}ms")
    print(f"   最小间隔: {np.min(time_diffs)*1000:.3f}ms")
    print(f"   最大间隔: {np.max(time_diffs)*1000:.3f}ms")
    print(f"   间隔标准差: {np.std(time_diffs)*1000:.3f}ms")

    # 计算采样率
    total_duration = times[-1] - times[0]
    actual_rate = len(times) / total_duration
    print(f"\n⏱️  采样率分析:")
    print(f"   实际采样率: {actual_rate:.1f} Hz")
    print(f"   理论最大值: {1/np.min(time_diffs):.1f} Hz")
    print(f"   理论最小值: {1/np.max(time_diffs):.1f} Hz")

    # 分析间隔分布
    print(f"\n📈 间隔分布:")
    interval_ms = time_diffs * 1000
    count_5 = np.sum(interval_ms < 5)
    count_5_10 = np.sum((interval_ms >= 5) & (interval_ms < 10))
    count_10_20 = np.sum((interval_ms >= 10) & (interval_ms < 20))
    count_20 = np.sum(interval_ms >= 20)
    total = len(interval_ms)

    print(f"   < 5ms: {count_5} 个 ({count_5/total*100:.1f}%)")
    print(f"   5-10ms: {count_5_10} 个 ({count_5_10/total*100:.1f}%)")
    print(f"   10-20ms: {count_10_20} 个 ({count_10_20/total*100:.1f}%)")
    print(f"   > 20ms: {count_20} 个 ({count_20/total*100:.1f}%)")

# 分析处理时间
if len(processing_times) > 0:
    print(f"\n⚙️  处理时间分析:")
    print(f"   平均: {np.mean(processing_times):.3f}ms")
    print(f"   最小: {np.min(processing_times):.3f}ms")
    print(f"   最大: {np.max(processing_times):.3f}ms")
    print(f"   标准差: {np.std(processing_times):.3f}ms")

    # 理论最大采样率
    theoretical_max = 1000 / np.mean(processing_times)
    print(f"\n🎯 理论分析:")
    print(f"   平均处理时间: {np.mean(processing_times):.3f}ms")
    print(f"   理论最大采样率: {theoretical_max:.1f} Hz")
    print(f"   实际采样率: {actual_rate:.1f} Hz")
    print(f"   效率: {actual_rate/theoretical_max*100:.1f}%")

# 分析数据源的实际速率
print(f"\n🔍 数据源速率分析:")
print(f"   实际采样率: {actual_rate:.1f} Hz")
print(f"   平均间隔: {1/actual_rate*1000:.3f}ms")

# 关键发现
print(f"\n💡 关键发现:")
if actual_rate < 100:
    print(f"   ⚠️  采样率 < 100 Hz，说明数据源本身速率有限")
    print(f"   ⚠️  即使优化代码，也无法超过数据源的实际速率")
    print(f"   ⚠️  需要检查:")
    print(f"       1. DSA 硬件设备的实际采样率")
    print(f"       2. 网络连接是否稳定")
    print(f"       3. 数据队列是否有溢出")
elif actual_rate < 500:
    print(f"   ⚠️  采样率 < 500 Hz，可能受到以下因素限制:")
    print(f"       1. 数据源读取超时设置（当前 1ms）")
    print(f"       2. 异步等待时间")
    if len(processing_times) > 0:
        print(f"       3. 处理时间（平均 {np.mean(processing_times):.3f}ms）")
else:
    print(f"   ✅ 采样率 > 500 Hz，性能良好")

print(f"\n{'='*70}")
print("分析完成！")

