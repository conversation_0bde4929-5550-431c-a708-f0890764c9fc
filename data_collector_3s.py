import ctypes
from ctypes import *
import time
import os
import queue
import matplotlib.pyplot as plt

class DSADataCollector:
    def __init__(self, dll_path):
        """初始化数据收集器"""
        if not os.path.exists(dll_path):
            raise FileNotFoundError(dll_path)
        self.sdk = ctypes.cdll.LoadLibrary(dll_path)

        # 定义回调函数类型
        self.CALLBACK = CFUNCTYPE(None, c_int, c_int, c_int, c_int, POINTER(c_float), c_int)
        self._callback_func = self.CALLBACK(self._data_callback)

        # 数据缓冲区
        self.training_data = []  # 原始训练数据
        self.start_time = None
        self.total_data_points = 0  # 总数据点计数器
        self.is_running = True
        self.continuous_mode = False  # 连续模式标志

        # 实时数据流 - 分离位移和速度队列
        self.displacement_queue = queue.Queue(maxsize=10000)
        self.velocity_queue = queue.Queue(maxsize=10000)
        self.enable_real_time_stream = True

        # 实时预测回调
        self.prediction_callback = None  # 用于接收每个新数据点的预测回调
        self.real_time_predictor = None  # 实时预测器实例

        # 预测频率控制
        self.prediction_interval = 10  # 每隔10个数据点进行一次预测
        self.prediction_counter = 0  # 预测计数器
        self.total_prediction_calls = 0  # 总预测调用次数（用于调试）
        
        # SDK 枚举
        self.OutputFilter = {'of1k':0x00,'of2k':0x01,'of10k':0x02,'of20k':0x03}
        self.OutDataType = {'odtVelocity':0x01,'odtDisplacement':0x02,'odtAll':0x03,'odtNoOutput':0x04}
        self.VelocityRange = {'vr1':0x01}
        self.DisplacementRange = {'dr1':0x01}
        self.DeviceType = {'DT3M':0x01}
        self.LaserWaveLength = {'LW_632_8':0x01}

    def _data_callback(self, dataType, sRate, vRange, dRange, data_ptr, dataLen):
        """SDK 数据回调 - 实时预测版本"""
        if not self.is_running:
            return

        # 将指针转换为数组
        array_type = c_float * dataLen
        data = cast(data_ptr, POINTER(array_type)).contents
        values = list(data)

        # 初始化开始时间和数据点计数器
        current_time = time.time()
        if self.start_time is None:
            self.start_time = current_time
            self.total_data_points = 0  # 总数据点计数器
            print("开始收集数据...")

        # 检查运行时间或数据点数（如果不是连续模式）
        elapsed_time = current_time - self.start_time
        if not self.continuous_mode:
            # 目标收集300000个原始数据点（每100个点取1个，得到3000个训练数据点）
            target_raw_points = 300000
            if len(self.training_data) >= target_raw_points:
                self.is_running = False
                print(f"\n已收集{len(self.training_data)}个数据点，停止收集")
                return
            # 或者时间超过30秒也停止（300000点 ÷ 10000Hz = 30秒）
            elif elapsed_time >= 30.0:
                self.is_running = False
                print(f"\n已运行30秒，停止收集")
                return

        # SDK 实际采样率
        effective_sRate = sRate if sRate > 0 else 10000  # 默认 10000Hz

        # 处理每个数据点
        for value in values:
            # 基于总数据点数计算连续的时间戳
            point_time_from_start = self.total_data_points / effective_sRate  # 从开始的总时间偏移
            absolute_timestamp = self.start_time + point_time_from_start  # 绝对时间戳
            timestamp_ms = point_time_from_start * 1000  # 相对时间, ms

            # 增加总数据点计数
            self.total_data_points += 1

            # 保存训练数据
            self.training_data.append({'timestamp_ms': timestamp_ms, 'value': value, 'dataType': dataType})

            # 实时数据流处理
            if self.enable_real_time_stream:
                data_point = {
                    'timestamp_ms': timestamp_ms,
                    'value': value,
                    'dataType': dataType,
                    'timestamp': absolute_timestamp  # 使用精确的绝对时间戳
                }

                # 根据数据类型分别放入不同队列
                try:
                    if dataType == 2:  # 位移数据
                        self.displacement_queue.put_nowait(data_point)
                        # 预测频率控制：每隔N个数据点进行一次预测
                        self.prediction_counter += 1
                        if self.real_time_predictor is not None and self.prediction_counter >= self.prediction_interval:
                            self.total_prediction_calls += 1

                            # 检查预测器状态
                            if hasattr(self.real_time_predictor, 'is_initialized') and not self.real_time_predictor.is_initialized:
                                print("⚠️ 预测器未初始化，跳过预测")
                            else:
                                # 在1000次附近添加详细调试
                                if 995 <= self.total_prediction_calls <= 1005:
                                    print(f"🔍 预测调用#{self.total_prediction_calls}: 时间={current_time - self.start_time:.3f}s")

                                # 检查并强制保持运行状态（修复1000次限制问题）
                                if self.total_prediction_calls == 1000:
                                    print("🔧 到达1000次预测，强制保持收集器运行状态")
                                    self.is_running = True  # 强制保持运行

                                self._perform_real_time_prediction(data_point)

                                # 在1000次后继续强制保持运行状态
                                if self.total_prediction_calls > 1000 and self.total_prediction_calls <= 1010:
                                    if not self.is_running:
                                        print(f"🔧 预测#{self.total_prediction_calls}: 检测到收集器停止，强制重启")
                                        self.is_running = True
                            self.prediction_counter = 0  # 重置计数器
                    elif dataType == 1:  # 速度数据
                        self.velocity_queue.put_nowait(data_point)
                    else:  # 其他类型数据
                        self.displacement_queue.put_nowait(data_point)
                        # 对其他类型数据也应用相同的预测频率控制
                        self.prediction_counter += 1
                        if self.real_time_predictor is not None and self.prediction_counter >= self.prediction_interval:
                            self._perform_real_time_prediction(data_point)
                            self.prediction_counter = 0  # 重置计数器
                except queue.Full:
                    # 队列满时，丢弃最旧的数据，添加新数据
                    try:
                        if dataType == 2:  # 位移数据
                            self.displacement_queue.get_nowait()
                            self.displacement_queue.put_nowait(data_point)
                        elif dataType == 1:  # 速度数据
                            self.velocity_queue.get_nowait()
                            self.velocity_queue.put_nowait(data_point)
                    except queue.Empty:
                        pass

        # 每收到一批数据后，打印调试信息
        if len(values) > 0:
            # 创建数据类型映射
            data_type_names = {1: '速度', 2: '位移', 3: '全部'}
            data_type_name = data_type_names.get(dataType, '未知')
            if len(self.training_data) % 5000 == 0:  # 减少打印频率
                print(f"接收到{data_type_name}数据: {len(values)}个点, 已收集训练数据: {len(self.training_data)} 个点")

    def initialize_sdk(self):
        """初始化 SDK"""
        print("初始化SDK...")
        result = self.sdk.initialize()
        if result != 0:
            print(f"初始化失败: {result}")
            return False
        
        self.sdk.setBindInfo(b"0.0.0.0", 62618)
        self.sdk.setBoardcastInfo(b"255.255.255.255", 63082)
        self.sdk.setDeviceType(self.DeviceType['DT3M'])
        self.sdk.setDeviceId(0)
        self.sdk.setLaserWaveLength(self.LaserWaveLength['LW_632_8'])
        self.sdk.setDataCallBack(self._callback_func)
        self.sdk.setDataLength(256)  # 每次回调256点
        self.sdk.setOutputFilter(self.OutputFilter['of10k'])  # 设置为10000Hz采样
        self.sdk.setVelocityRange(self.VelocityRange['vr1'])
        self.sdk.setDisplacementRange(self.DisplacementRange['dr1'])
        self.sdk.setOutDataType(self.OutDataType['odtDisplacement'])
        
        print("SDK初始化完成")
        return True

    def start_collection(self):
        """启动数据采集"""
        print("启动数据采集...")
        result = self.sdk.start()
        if result != 0:
            print(f"启动失败: {result}")
            return False
        return True

    def stop_collection(self):
        """停止采集"""
        self.sdk.stop()
        self.sdk.unInitialize()
        print("采集停止，SDK已反初始化")

    def save_training_data(self):
        """保存训练数据到 v1.txt，均匀从每100个点取1个，使用3秒内收集的所有数据"""
        filename = "v1.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("时间[ms]\t位移[μm]\n")
            total_points = len(self.training_data)
            step = 100  # 每100个点取1个

            # 使用3秒内收集到的所有数据
            for i in range(0, total_points, step):
                point = self.training_data[i]
                f.write(f"{point['timestamp_ms']:.3f}\t{point['value']:.6f}\n")

            actual_training_points = total_points // step
            print(f"训练数据已保存到 {filename}")
            print(f"原始数据点数: {total_points}")
            print(f"降采样后训练数据点数: {actual_training_points}")
            print(f"数据时长: {total_points / 10000:.1f} 秒")

    def plot_training_data(self):
        """绘制v1.txt中的位移曲线"""
        timestamps = [d['timestamp_ms'] for d in self.training_data[::100]]  # 每100个取1
        values = [d['value'] for d in self.training_data[::100]]
        
        plt.figure(figsize=(10,5))
        plt.plot(timestamps, values, color='blue', linewidth=1)
        plt.title("位移随时间变化曲线")
        plt.xlabel("时间 [ms]")
        plt.ylabel("位移 [μm]")
        plt.grid(True)
        plt.tight_layout()
        plt.show()

    def run(self):
        """运行收集"""
        if not self.initialize_sdk():
            return False
        if not self.start_collection():
            return False
        
        print("\n开始收集前3秒数据...")
        try:
            while self.is_running:
                time.sleep(0.05)
        except KeyboardInterrupt:
            print("用户中断采集")
            self.is_running = False
        finally:
            self.stop_collection()
            self.save_training_data()
            self.plot_training_data()  # 绘图
        print("数据收集完成")
        return True

    def enable_real_time_streaming(self):
        """启用实时数据流"""
        self.enable_real_time_stream = True
        print("实时数据流已启用")

    def disable_real_time_streaming(self):
        """禁用实时数据流"""
        self.enable_real_time_stream = False
        print("实时数据流已禁用")

    def enable_continuous_mode(self):
        """启用连续模式：训练完成后继续运行进行预测"""
        self.continuous_mode = True
        print("连续模式已启用：将持续运行直到手动停止")

    def _perform_real_time_prediction(self, data_point):
        """执行实时预测"""
        try:
            if self.real_time_predictor is not None:
                # 检查预测器状态
                if hasattr(self.real_time_predictor, 'prediction_count'):
                    current_count = self.real_time_predictor.prediction_count
                    if current_count > 0 and current_count % 1000 == 0:
                        print(f"🔍 调试: 预测器已完成 {current_count} 次预测")

                # 进行预测
                prediction = self.real_time_predictor.process_single_point(data_point['value'])

                # 检查预测结果
                if prediction is None:
                    print(f"⚠️ 预测器返回None，可能遇到问题")
                    return

                # 调用预测回调
                if self.prediction_callback is not None:
                    self.prediction_callback(
                        real_value=data_point['value'],
                        prediction=prediction,
                        timestamp=data_point['timestamp'],
                        data_type=data_point['dataType']
                    )
                else:
                    print(f"⚠️ 预测回调函数为None")
            else:
                print(f"⚠️ 实时预测器为None")
        except Exception as e:
            print(f"⚠️ 实时预测错误: {e}")
            # 不要因为单次预测错误就停止整个预测过程
            import traceback
            traceback.print_exc()

    def set_real_time_predictor(self, predictor):
        """设置实时预测器"""
        self.real_time_predictor = predictor
        print("实时预测器已设置")

    def set_prediction_callback(self, callback):
        """设置预测结果回调函数"""
        self.prediction_callback = callback
        print("预测回调函数已设置")

    def set_prediction_interval(self, interval):
        """设置预测间隔（每隔多少个数据点进行一次预测）"""
        self.prediction_interval = max(1, interval)  # 确保间隔至少为1
        self.prediction_counter = 0  # 重置计数器
        print(f"预测间隔已设置为: 每{self.prediction_interval}个数据点预测一次")

        # 计算预测频率
        sampling_rate = 10000  # DSA采样率10kHz
        prediction_frequency = sampling_rate / self.prediction_interval
        print(f"预测频率: {prediction_frequency:.1f} Hz")

    def get_real_time_data_by_type(self, data_type, timeout=0.001):
        """按类型获取实时数据（非阻塞，不会消耗其他类型队列）"""
        try:
            if data_type == 'displacement':
                return self.displacement_queue.get(timeout=timeout)
            elif data_type == 'velocity':
                return self.velocity_queue.get(timeout=timeout)
            else:
                # 默认返回位移数据
                return self.displacement_queue.get(timeout=timeout)
        except queue.Empty:
            return None

    def get_real_time_queue_size(self):
        """获取实时队列大小"""
        return {
            'displacement': self.displacement_queue.qsize(),
            'velocity': self.velocity_queue.qsize()
        }

    def get_training_data_status(self):
        """获取训练数据收集状态"""
        current_count = len(self.training_data)
        target_raw_points = 300000  # 目标收集300000个原始数据点

        if self.start_time is None:
            elapsed_time = 0.0
            collected = False
        else:
            elapsed_time = time.time() - self.start_time
            collected = not self.is_running  # 如果停止运行，说明收集完成

        progress = min(100.0, (current_count / target_raw_points) * 100.0) if current_count > 0 else 0.0

        # 计算预期的训练数据点数（每100个原始数据点取1个）
        expected_training_points = current_count // 100

        return {
            'count': current_count,
            'target': target_raw_points,
            'progress': progress,
            'collected': collected,
            'elapsed_time': elapsed_time,
            'expected_training_points': expected_training_points
        }


def main():
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    if not os.path.exists(dll_path):
        print(f"错误：找不到DLL文件 {dll_path}")
        return

    collector = DSADataCollector(dll_path)
    collector.run()


if __name__ == "__main__":
    main()
