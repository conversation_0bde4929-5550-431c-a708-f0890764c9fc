"""
测试30秒预测功能
"""

import asyncio
import os
import sys
import time

# 添加当前目录到路径
sys.path.insert(0, '.')

from real_time_callback_prediction import RealTimePredictionSystem

async def test_30s_prediction():
    """测试30秒预测功能"""
    print("🧪 测试30秒预测功能")
    print("="*60)
    
    # DLL 路径
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    # 检查DLL文件
    if not os.path.exists(dll_path):
        print(f"❌ 找不到DLL文件: {dll_path}")
        return False
    
    # 创建实时预测系统
    system = RealTimePredictionSystem(
        dll_path=dll_path,
        model_path="displacement_model.pth",
        scaler_path="displacement_scaler.pkl"
    )
    
    print("🚀 开始30秒预测测试...")
    print("📊 预期结果:")
    print("   - 预测时长: 30秒")
    print("   - 预测间隔: 每10个数据点")
    print("   - 预测频率: ~1000 Hz")
    print("   - 预测次数: ~30,000次")
    print("   - 应该在30秒后自动停止")
    print("-" * 60)
    
    # 记录开始时间
    start_time = time.time()
    
    # 启动30秒预测测试
    success = await system.start_real_time_prediction(duration_seconds=30)
    
    # 记录结束时间
    end_time = time.time()
    actual_duration = end_time - start_time
    
    if success:
        print("\n" + "="*60)
        print("📊 30秒预测测试结果")
        print("="*60)
        
        # 分析预测数据
        if hasattr(system, 'real_time_data') and system.real_time_data:
            prediction_count = len(system.real_time_data)
            actual_frequency = prediction_count / actual_duration
            
            print(f"⏱️  实际运行时间: {actual_duration:.2f} 秒")
            print(f"📈 实际预测次数: {prediction_count}")
            print(f"🎯 实际预测频率: {actual_frequency:.1f} Hz")
            
            # 计算理论值
            theoretical_predictions = 30 * 1000  # 30秒 × 1000Hz
            frequency_ratio = actual_frequency / 1000 * 100
            
            print(f"📊 理论预测次数: {theoretical_predictions}")
            print(f"📊 频率达成率: {frequency_ratio:.1f}%")
            
            # 判断测试结果
            if 25 <= actual_duration <= 35:  # 允许±5秒误差
                print("✅ 预测时长控制正常")
            else:
                print("⚠️ 预测时长偏离30秒目标")
                
            if 800 <= actual_frequency <= 1200:  # 允许20%误差
                print("✅ 预测频率在预期范围内")
            else:
                print("⚠️ 预测频率偏离预期值")
            
            # 分析预测质量
            if prediction_count > 0:
                errors = [d['error'] for d in system.real_time_data]
                avg_error = sum(errors) / len(errors)
                max_error = max(errors)
                min_error = min(errors)
                
                print(f"\n📊 预测质量分析:")
                print(f"   平均误差: {avg_error:.6f} μm")
                print(f"   最大误差: {max_error:.6f} μm")
                print(f"   最小误差: {min_error:.6f} μm")
                
                # 检查是否有异常大的误差
                large_errors = [e for e in errors if e > 1000]  # 大于1000μm的误差
                if large_errors:
                    print(f"⚠️ 发现 {len(large_errors)} 个异常大误差 (>1000μm)")
                    print(f"   这可能是初始化阶段的正常现象")
                else:
                    print("✅ 所有误差都在合理范围内")
        else:
            print("❌ 未收集到预测数据")
            return False
        
        print("\n✅ 30秒预测测试完成")
        return True
    else:
        print("❌ 30秒预测测试失败")
        return False

def main():
    """主函数"""
    print("🎯 DSA 30秒预测功能测试")
    print("测试目标: 验证30秒预测时长控制和预测质量")
    print("="*60)
    
    try:
        result = asyncio.run(test_30s_prediction())
        
        if result:
            print("\n🎉 30秒预测测试通过！")
            print("📋 功能确认:")
            print("   ✅ 30秒时长控制正常")
            print("   ✅ 预测频率符合预期")
            print("   ✅ 预测质量良好")
            print("   ✅ 自动停止功能正常")
        else:
            print("\n❌ 30秒预测测试失败")
            print("请检查系统配置和硬件连接")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
