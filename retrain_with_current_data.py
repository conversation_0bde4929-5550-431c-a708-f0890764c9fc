#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用当前v1.txt数据重新训练模型
"""

import os
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
import pickle

class DisplacementLSTM(nn.Module):
    """位移预测LSTM模型"""
    
    def __init__(self, input_size=1, hidden_size=96, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.linear = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        out = lstm_out[:, -1, :]
        out = self.dropout(out)
        prediction = self.linear(out)
        return prediction

def create_sequences(data, window_size):
    """创建训练序列"""
    sequences = []
    targets = []

    for i in range(len(data) - window_size):
        seq = data[i:i+window_size]
        target = data[i+window_size]
        sequences.append(seq)
        targets.append(target)

    return np.array(sequences), np.array(targets)

def retrain_model():
    """重新训练模型"""
    print("="*70)
    print("🔄 使用当前v1.txt数据重新训练模型")
    print("="*70)
    
    # 1. 备份旧模型
    print("\n1. 备份旧模型文件...")
    backup_files = [
        'displacement_model_fixed.pth',
        'displacement_scaler_fixed.pkl',
        'training_info_fixed.pkl'
    ]
    
    for file in backup_files:
        if os.path.exists(file):
            backup_name = file.replace('.', '_backup.')
            os.rename(file, backup_name)
            print(f"   ✅ {file} → {backup_name}")
    
    # 2. 加载当前训练数据
    print("\n2. 加载当前训练数据...")
    try:
        data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        displacement_data = data.iloc[:, 1].values
        
        print(f"   📊 数据点数: {len(displacement_data)}")
        print(f"   📊 数据范围: {displacement_data.min():.6f} ~ {displacement_data.max():.6f} μm")
        print(f"   📊 数据均值: {displacement_data.mean():.6f} μm")
        print(f"   📊 数据标准差: {displacement_data.std():.6f} μm")
        
    except Exception as e:
        print(f"   ❌ 加载数据失败: {e}")
        return False
    
    # 3. 数据预处理
    print("\n3. 数据预处理...")
    
    # 使用所有数据进行训练
    train_data = displacement_data
    print(f"   使用全部{len(train_data)}个点进行训练")
    
    # 创建标准化器
    scaler = MinMaxScaler(feature_range=(-1, 1))
    train_normalized = scaler.fit_transform(train_data.reshape(-1, 1))
    train_normalized = torch.FloatTensor(train_normalized.flatten())
    
    print(f"   📊 标准化器拟合范围: {scaler.data_min_[0]:.6f} ~ {scaler.data_max_[0]:.6f} μm")
    print(f"   📊 标准化后范围: {train_normalized.min():.6f} ~ {train_normalized.max():.6f}")
    
    # 4. 创建训练序列
    print("\n4. 创建训练序列...")
    window_size = 25
    X, y = create_sequences(train_normalized.numpy(), window_size)
    
    X = torch.FloatTensor(X).unsqueeze(-1)
    y = torch.FloatTensor(y)
    
    print(f"   窗口大小: {window_size}")
    print(f"   训练序列数量: {len(X)}")
    print(f"   输入形状: {X.shape}")
    print(f"   目标形状: {y.shape}")
    
    # 5. 创建模型
    print("\n5. 创建模型...")
    hidden_size = 96
    model = DisplacementLSTM(input_size=1, hidden_size=hidden_size, output_size=1)
    
    total_params = sum(p.numel() for p in model.parameters())
    print(f"   模型参数数量: {total_params}")
    
    # 6. 训练配置
    print("\n6. 训练配置...")
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.008)
    epochs = 150  # 增加训练轮数
    
    print(f"   损失函数: MSE")
    print(f"   优化器: Adam")
    print(f"   学习率: 0.008")
    print(f"   训练轮数: {epochs}")
    
    # 7. 训练模型
    print(f"\n7. 开始训练...")
    model.train()
    
    train_losses = []
    best_loss = float('inf')
    
    for epoch in range(epochs):
        optimizer.zero_grad()
        outputs = model(X).squeeze()
        loss = criterion(outputs, y)
        loss.backward()
        optimizer.step()
        
        train_losses.append(loss.item())
        
        if loss.item() < best_loss:
            best_loss = loss.item()
        
        if epoch % 25 == 0:
            print(f'   Epoch {epoch:3d}: Loss = {loss.item():.8f}')
    
    model.eval()
    final_loss = train_losses[-1]
    print(f"\n   训练完成！最终损失: {final_loss:.8f}")
    print(f"   最佳损失: {best_loss:.8f}")
    
    # 8. 验证模型
    print(f"\n8. 验证模型...")
    
    with torch.no_grad():
        test_outputs = model(X[:5]).squeeze()
        test_targets = y[:5]
        
        print("   前5个预测结果:")
        for i in range(5):
            pred_norm = test_outputs[i].item()
            target_norm = test_targets[i].item()
            
            # 反标准化
            pred_real = scaler.inverse_transform([[pred_norm]])[0, 0]
            target_real = scaler.inverse_transform([[target_norm]])[0, 0]
            
            error = pred_real - target_real
            
            print(f"     预测{i+1}: 预测={pred_real:.6f}, 实际={target_real:.6f}, 误差={error:.6f}")
    
    # 9. 保存新模型
    print(f"\n9. 保存新模型...")
    
    try:
        # 保存模型
        torch.save(model.state_dict(), 'displacement_model_fixed.pth')
        print(f"   ✅ 模型已保存到: displacement_model_fixed.pth")
        
        # 保存标准化器
        with open('displacement_scaler_fixed.pkl', 'wb') as f:
            pickle.dump(scaler, f)
        print(f"   ✅ 标准化器已保存到: displacement_scaler_fixed.pkl")
        
        # 保存训练信息
        training_info = {
            'window_size': window_size,
            'hidden_size': hidden_size,
            'final_loss': final_loss,
            'best_loss': best_loss,
            'train_points': len(train_data),
            'epochs': epochs,
            'data_min': scaler.data_min_[0],
            'data_max': scaler.data_max_[0],
            'data_range': scaler.data_max_[0] - scaler.data_min_[0],
            'retrained_with_current_data': True
        }
        
        with open('training_info_fixed.pkl', 'wb') as f:
            pickle.dump(training_info, f)
        print(f"   ✅ 训练信息已保存到: training_info_fixed.pkl")
        
        print(f"\n✅ 重新训练完成！")
        print(f"   新模型基于当前v1.txt数据训练")
        print(f"   数据范围: {scaler.data_min_[0]:.6f} ~ {scaler.data_max_[0]:.6f} μm")
        print(f"   训练损失: {final_loss:.8f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 保存失败: {e}")
        return False

def test_new_model():
    """测试新训练的模型"""
    print(f"\n" + "="*70)
    print("🧪 测试新训练的模型")
    print("="*70)
    
    try:
        # 加载新模型
        model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
        model.load_state_dict(torch.load('displacement_model_fixed.pth'))
        model.eval()
        
        with open('displacement_scaler_fixed.pkl', 'rb') as f:
            scaler = pickle.load(f)
        
        print("✅ 新模型加载成功")
        
        # 使用训练数据的一部分进行测试
        data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        test_data = data.iloc[:, 1].values
        
        # 测试预测
        window_size = 25
        predictions = []
        
        for i in range(window_size, min(window_size + 10, len(test_data))):
            input_seq = test_data[i-window_size:i]
            normalized_seq = scaler.transform(input_seq.reshape(-1, 1)).flatten()
            input_tensor = torch.FloatTensor(normalized_seq).unsqueeze(0).unsqueeze(-1)
            
            with torch.no_grad():
                normalized_pred = model(input_tensor).item()
            
            pred = scaler.inverse_transform([[normalized_pred]])[0, 0]
            actual = test_data[i]
            
            predictions.append(pred)
            error = pred - actual
            
            print(f"   测试{i-window_size+1}: 预测={pred:.6f}, 实际={actual:.6f}, 误差={error:.6f}")
        
        # 计算性能指标
        actual_values = test_data[window_size:window_size+len(predictions)]
        predictions = np.array(predictions)
        
        mae = np.mean(np.abs(predictions - actual_values))
        rmse = np.sqrt(np.mean((predictions - actual_values)**2))
        bias = np.mean(predictions - actual_values)
        
        print(f"\n   性能指标:")
        print(f"   MAE: {mae:.6f} μm")
        print(f"   RMSE: {rmse:.6f} μm")
        print(f"   偏差: {bias:.6f} μm")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试新模型失败: {e}")
        return False

def main():
    """主函数"""
    print("重新训练模型程序")
    
    # 重新训练模型
    success = retrain_model()
    
    if success:
        # 测试新模型
        test_new_model()
        
        print(f"\n" + "="*70)
        print("🎯 总结")
        print("="*70)
        print("✅ 成功使用当前v1.txt数据重新训练模型")
        print("✅ 新模型的数据范围与当前测试数据匹配")
        print("✅ 预测偏差问题应该得到显著改善")
        print("\n🚀 现在可以重新运行 integrated_real_time_demo.py 测试效果！")
        
    else:
        print(f"\n❌ 重新训练失败")

if __name__ == "__main__":
    main()
