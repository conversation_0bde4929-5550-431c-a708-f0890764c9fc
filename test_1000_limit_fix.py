"""
测试1000次预测限制修复效果
"""

import asyncio
import os
import sys
import time

# 添加当前目录到路径
sys.path.insert(0, '.')

from real_time_callback_prediction import RealTimePredictionSystem

class FixTestPredictionSystem(RealTimePredictionSystem):
    """测试修复效果的预测系统"""
    
    def __init__(self, dll_path, model_path=None, scaler_path=None):
        super().__init__(dll_path, model_path, scaler_path)
        self.prediction_call_count = 0
        self.last_prediction_time = None
        self.predictions_after_1000 = 0
        
    def prediction_callback(self, real_value, prediction, timestamp, data_type):
        """测试修复效果的预测回调函数"""
        self.prediction_call_count += 1
        self.last_prediction_time = timestamp
        
        if self.start_time is None:
            self.start_time = timestamp
            print("🚀 开始修复效果测试...")
        
        # 计算相对时间
        relative_time = timestamp - self.start_time
        
        # 统计1000次后的预测
        if self.prediction_call_count > 1000:
            self.predictions_after_1000 += 1
        
        # 在关键点输出调试信息
        if self.prediction_call_count in [1, 500, 900, 999, 1000, 1001, 1002, 1005, 1010, 1050, 1100]:
            print(f"🔍 预测#{self.prediction_call_count}: 时间={relative_time:.6f}s, 真实值={real_value:.6f}μm")
        
        # 每100次显示进度（1000次后）
        if self.prediction_call_count > 1000 and self.prediction_call_count % 100 == 0:
            print(f"✅ 1000次后预测#{self.prediction_call_count}: 时间={relative_time:.6f}s")
        
        # 计算预测误差
        error = abs(real_value - prediction)
        self.prediction_errors.append(error)
        
        # 存储实时数据
        data_record = {
            'time': relative_time,
            'real_value': real_value,
            'prediction': prediction,
            'error': error,
            'timestamp': timestamp,
            'data_type': data_type
        }
        self.real_time_data.append(data_record)
        
        # 检查关键里程碑
        if self.prediction_call_count == 1000:
            print("🎯 到达1000次预测！检查修复效果...")
        elif self.prediction_call_count == 1001:
            print("🎉 成功突破1000次限制！修复生效！")
        elif self.prediction_call_count == 1500:
            print("🚀 已达到1500次预测，修复效果良好！")
        elif self.prediction_call_count == 2000:
            print("🎊 已达到2000次预测，完全突破限制！")

async def test_1000_limit_fix():
    """测试1000次预测限制修复效果"""
    print("🔧 测试1000次预测限制修复效果")
    print("="*60)
    
    # DLL 路径
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    # 检查DLL文件
    if not os.path.exists(dll_path):
        print(f"❌ 找不到DLL文件: {dll_path}")
        return False
    
    # 创建测试预测系统
    system = FixTestPredictionSystem(
        dll_path=dll_path,
        model_path="displacement_model.pth",
        scaler_path="displacement_scaler.pkl"
    )
    
    print("🚀 开始修复效果测试...")
    print("📊 测试目标:")
    print("   - 运行10秒测试")
    print("   - 验证预测能否超过1000次")
    print("   - 检查预测是否持续整个期间")
    print("   - 预期预测次数: ~10,000次")
    print("-" * 60)
    
    # 记录开始时间
    start_time = time.time()
    
    # 启动10秒测试
    success = await system.start_real_time_prediction(duration_seconds=10)
    
    # 记录结束时间
    end_time = time.time()
    actual_duration = end_time - start_time
    
    print("\n" + "="*60)
    print("🔧 修复效果测试结果")
    print("="*60)
    
    print(f"⏱️  实际运行时间: {actual_duration:.2f} 秒")
    print(f"📈 总预测调用次数: {system.prediction_call_count}")
    print(f"🎯 1000次后的预测次数: {system.predictions_after_1000}")
    
    if system.last_prediction_time and system.start_time:
        last_relative_time = system.last_prediction_time - system.start_time
        print(f"📊 最后一次预测时间: {last_relative_time:.6f} 秒")
        
        # 判断修复效果
        if system.prediction_call_count > 1000:
            print(f"✅ 成功突破1000次限制！预测次数: {system.prediction_call_count}")
            
            if system.predictions_after_1000 > 0:
                print(f"🎉 1000次后继续预测了 {system.predictions_after_1000} 次")
            
            # 检查预测是否持续到接近结束
            if last_relative_time > actual_duration * 0.8:
                print("✅ 预测持续到接近测试结束")
                print("🎊 修复完全成功！")
            else:
                print(f"⚠️ 预测在 {last_relative_time:.3f}s 停止，但测试持续到 {actual_duration:.3f}s")
                print("🔧 修复部分成功，但仍有改进空间")
        else:
            print(f"❌ 修复失败，预测仍然停止在 {system.prediction_call_count} 次")
    
    # 分析预测数据
    if hasattr(system, 'real_time_data') and system.real_time_data:
        prediction_count = len(system.real_time_data)
        print(f"\n📊 数据分析:")
        print(f"   收集的预测数据: {prediction_count} 条")
        
        if prediction_count > 0:
            times = [d['time'] for d in system.real_time_data]
            print(f"   时间范围: {min(times):.6f}s - {max(times):.6f}s")
            print(f"   时间跨度: {max(times) - min(times):.6f}s")
            
            # 检查预测频率
            if len(times) > 1:
                intervals = [times[i] - times[i-1] for i in range(1, len(times))]
                avg_interval = sum(intervals) / len(intervals)
                avg_frequency = 1.0 / avg_interval if avg_interval > 0 else 0
                print(f"   平均预测间隔: {avg_interval:.6f}s")
                print(f"   平均预测频率: {avg_frequency:.1f} Hz")
                
                # 计算理论值对比
                theoretical_predictions = 10 * 1000  # 10秒 × 1000Hz
                achievement_rate = prediction_count / theoretical_predictions * 100
                print(f"   理论预测次数: {theoretical_predictions}")
                print(f"   达成率: {achievement_rate:.1f}%")
    
    # 检查系统状态
    print(f"\n🔧 系统状态检查:")
    if hasattr(system, 'collector') and system.collector:
        print(f"   收集器运行状态: {system.collector.is_running}")
        if hasattr(system.collector, 'total_prediction_calls'):
            print(f"   收集器预测调用计数: {system.collector.total_prediction_calls}")
    
    if hasattr(system, 'predictor') and system.predictor:
        print(f"   预测器初始化状态: {getattr(system.predictor, 'is_initialized', 'N/A')}")
        print(f"   预测器计数: {getattr(system.predictor, 'prediction_count', 'N/A')}")
    
    return success

def main():
    """主函数"""
    print("🎯 测试1000次预测限制修复效果")
    print("目标: 验证强制保持收集器运行状态的修复方案")
    print("="*60)
    
    try:
        result = asyncio.run(test_1000_limit_fix())
        
        if result:
            print("\n🔧 修复效果测试完成")
            print("📋 修复方案:")
            print("   ✅ 在1000次预测时强制设置 is_running = True")
            print("   ✅ 在1000次后持续监控并保持运行状态")
            print("   ✅ 防止收集器意外停止")
            
            print("\n🚀 如果修复成功，现在可以进行长时间预测:")
            print("   python test_30s_prediction.py")
        else:
            print("\n❌ 修复效果测试失败")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
