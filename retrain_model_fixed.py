#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复模型训练问题并重新训练
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
import pickle
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

class DisplacementLSTM(nn.Module):
    """位移预测LSTM模型"""
    
    def __init__(self, input_size=1, hidden_size=64, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.linear = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        out = lstm_out[:, -1, :]
        out = self.dropout(out)
        prediction = self.linear(out)
        return prediction

def create_sequences(data, window_size):
    """创建训练序列"""
    sequences = []
    targets = []

    for i in range(len(data) - window_size):
        seq = data[i:i+window_size]
        target = data[i+window_size]
        sequences.append(seq)
        targets.append(target)

    return np.array(sequences), np.array(targets)

def train_fixed_model():
    """重新训练修复的模型"""
    print("=== 重新训练修复的模型 ===")
    
    # 1. 加载训练数据
    print("1. 加载训练数据...")
    try:
        data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        displacement_data = data.iloc[:, 1].values  # 位移列
        print(f"   加载成功，数据点数: {len(displacement_data)}")
        print(f"   数据范围: {displacement_data.min():.6f} - {displacement_data.max():.6f}")
        print(f"   数据均值: {displacement_data.mean():.6f}")
        
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return None, None
    
    # 2. 数据预处理
    print("\n2. 数据预处理...")
    
    # 使用前5000个点进行训练
    train_points = 5000
    train_data = displacement_data[:train_points]
    print(f"   使用前{train_points}个点进行训练")
    print(f"   训练数据范围: {train_data.min():.6f} - {train_data.max():.6f}")
    
    # 创建标准化器
    scaler = MinMaxScaler(feature_range=(-1, 1))
    train_normalized = scaler.fit_transform(train_data.reshape(-1, 1))
    train_normalized = torch.FloatTensor(train_normalized.flatten())
    
    print(f"   标准化器参数:")
    print(f"     数据最小值: {scaler.data_min_[0]:.6f}")
    print(f"     数据最大值: {scaler.data_max_[0]:.6f}")
    print(f"     数据范围: {scaler.data_max_[0] - scaler.data_min_[0]:.6f}")
    print(f"   标准化后范围: {train_normalized.min():.6f} - {train_normalized.max():.6f}")
    
    # 3. 创建训练序列
    print("\n3. 创建训练序列...")
    window_size = 25
    X, y = create_sequences(train_normalized.numpy(), window_size)
    
    X = torch.FloatTensor(X).unsqueeze(-1)
    y = torch.FloatTensor(y)
    
    print(f"   窗口大小: {window_size}")
    print(f"   训练序列数量: {len(X)}")
    print(f"   输入形状: {X.shape}")
    print(f"   目标形状: {y.shape}")
    
    # 4. 创建模型
    print("\n4. 创建模型...")
    hidden_size = 96
    model = DisplacementLSTM(input_size=1, hidden_size=hidden_size, output_size=1)
    
    total_params = sum(p.numel() for p in model.parameters())
    print(f"   模型参数数量: {total_params}")
    print(f"   隐藏层大小: {hidden_size}")
    
    # 5. 训练配置
    print("\n5. 训练配置...")
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.008)
    epochs = 100
    
    print(f"   损失函数: MSE")
    print(f"   优化器: Adam")
    print(f"   学习率: 0.008")
    print(f"   训练轮数: {epochs}")
    
    # 6. 训练模型
    print(f"\n6. 开始训练...")
    model.train()
    
    train_losses = []
    
    for epoch in range(epochs):
        optimizer.zero_grad()
        outputs = model(X).squeeze()
        loss = criterion(outputs, y)
        loss.backward()
        optimizer.step()
        
        train_losses.append(loss.item())
        
        if epoch % 25 == 0:
            print(f'   Epoch {epoch:3d}: Loss = {loss.item():.8f}')
    
    model.eval()
    final_loss = train_losses[-1]
    print(f"\n   训练完成！最终损失: {final_loss:.8f}")
    
    # 7. 验证模型
    print(f"\n7. 验证模型...")
    
    # 测试几个预测
    with torch.no_grad():
        test_outputs = model(X[:5]).squeeze()
        test_targets = y[:5]
        
        print("   前5个预测结果:")
        for i in range(5):
            pred_norm = test_outputs[i].item()
            target_norm = test_targets[i].item()
            
            # 反标准化
            pred_real = scaler.inverse_transform([[pred_norm]])[0, 0]
            target_real = scaler.inverse_transform([[target_norm]])[0, 0]
            
            error = pred_real - target_real
            
            print(f"     预测{i+1}: 预测={pred_real:.6f}, 实际={target_real:.6f}, 误差={error:.6f}")
    
    # 8. 保存模型
    print(f"\n8. 保存模型...")
    
    try:
        # 保存模型
        torch.save(model.state_dict(), 'displacement_model_fixed.pth')
        print(f"   ✅ 模型已保存到: displacement_model_fixed.pth")
        
        # 保存标准化器
        with open('displacement_scaler_fixed.pkl', 'wb') as f:
            pickle.dump(scaler, f)
        print(f"   ✅ 标准化器已保存到: displacement_scaler_fixed.pkl")
        
        # 保存训练信息
        training_info = {
            'window_size': window_size,
            'hidden_size': hidden_size,
            'final_loss': final_loss,
            'train_points': train_points,
            'epochs': epochs,
            'data_min': scaler.data_min_[0],
            'data_max': scaler.data_max_[0]
        }
        
        with open('training_info_fixed.pkl', 'wb') as f:
            pickle.dump(training_info, f)
        print(f"   ✅ 训练信息已保存到: training_info_fixed.pkl")
        
    except Exception as e:
        print(f"   ❌ 保存失败: {e}")
        return None, None
    
    return model, scaler

def test_fixed_model():
    """测试修复后的模型"""
    print(f"\n=== 测试修复后的模型 ===")
    
    # 加载修复后的模型
    try:
        model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
        model.load_state_dict(torch.load('displacement_model_fixed.pth'))
        model.eval()
        
        with open('displacement_scaler_fixed.pkl', 'rb') as f:
            scaler = pickle.load(f)
        
        print("✅ 修复后的模型加载成功")
        
    except Exception as e:
        print(f"❌ 加载修复后的模型失败: {e}")
        return
    
    # 加载测试数据
    test_data = pd.read_csv('three_seconds_data.txt', sep='\t', encoding='utf-8')
    test_displacement = test_data['位移[μm]'].values
    
    print(f"测试数据范围: {test_displacement.min():.6f} - {test_displacement.max():.6f}")
    
    # 进行预测
    window_size = 25
    predictions = []
    
    for i in range(window_size, len(test_displacement)):
        # 准备输入序列
        input_seq = test_displacement[i-window_size:i]
        normalized_seq = scaler.transform(input_seq.reshape(-1, 1)).flatten()
        input_tensor = torch.FloatTensor(normalized_seq).unsqueeze(0).unsqueeze(-1)
        
        # 预测
        with torch.no_grad():
            normalized_pred = model(input_tensor).item()
        
        # 反标准化
        pred = scaler.inverse_transform([[normalized_pred]])[0, 0]
        predictions.append(pred)
    
    # 计算性能指标
    actual_values = test_displacement[window_size:]
    predictions = np.array(predictions)
    
    mae = np.mean(np.abs(predictions - actual_values))
    rmse = np.sqrt(np.mean((predictions - actual_values)**2))
    bias = np.mean(predictions - actual_values)
    correlation = np.corrcoef(predictions, actual_values)[0, 1]
    
    print(f"\n修复后的性能指标:")
    print(f"  MAE: {mae:.6f} μm")
    print(f"  RMSE: {rmse:.6f} μm")
    print(f"  偏差: {bias:.6f} μm")
    print(f"  相关系数: {correlation:.6f}")
    
    # 绘制对比图
    plot_fixed_model_results(actual_values, predictions)
    
    return predictions, actual_values

def plot_fixed_model_results(actual_values, predictions):
    """绘制修复后的模型结果"""
    
    time_axis = np.arange(len(actual_values)) * 0.015  # 假设采样间隔约15ms
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 时间序列对比
    axes[0, 0].plot(time_axis, actual_values, 'b-', label='真实位移', linewidth=1.5, alpha=0.8)
    axes[0, 0].plot(time_axis, predictions, 'r--', label='预测位移', linewidth=1.5, alpha=0.8)
    axes[0, 0].set_xlabel('时间 [s]')
    axes[0, 0].set_ylabel('位移 [μm]')
    axes[0, 0].set_title('修复后模型：真实位移 vs 预测位移')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 误差时间序列
    error = predictions - actual_values
    axes[0, 1].plot(time_axis, error, 'g-', alpha=0.7)
    axes[0, 1].axhline(y=0, color='r', linestyle='--', alpha=0.5)
    axes[0, 1].set_xlabel('时间 [s]')
    axes[0, 1].set_ylabel('预测误差 [μm]')
    axes[0, 1].set_title('预测误差随时间变化')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 散点图
    axes[1, 0].scatter(actual_values, predictions, alpha=0.6, s=20)
    min_val = min(actual_values.min(), predictions.min())
    max_val = max(actual_values.max(), predictions.max())
    axes[1, 0].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='理想预测线')
    axes[1, 0].set_xlabel('真实位移 [μm]')
    axes[1, 0].set_ylabel('预测位移 [μm]')
    axes[1, 0].set_title('预测 vs 真实值散点图')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].set_aspect('equal', adjustable='box')
    
    # 4. 误差分布
    axes[1, 1].hist(error, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    axes[1, 1].axvline(x=np.mean(error), color='r', linestyle='--', 
                       label=f'平均误差: {np.mean(error):.6f}')
    axes[1, 1].set_xlabel('预测误差 [μm]')
    axes[1, 1].set_ylabel('频次')
    axes[1, 1].set_title('误差分布')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('fixed_model_results.png', dpi=300, bbox_inches='tight')
    print("修复后模型结果图已保存为 fixed_model_results.png")
    plt.close()

def main():
    """主函数"""
    print("模型修复和重新训练程序")
    print("="*60)
    
    # 重新训练模型
    model, scaler = train_fixed_model()
    
    if model is not None and scaler is not None:
        print("\n✅ 模型重新训练成功！")
        
        # 测试修复后的模型
        test_fixed_model()
        
        print(f"\n=== 总结 ===")
        print("问题原因:")
        print("  ❌ 原始模型的标准化器参数错误")
        print("  ❌ 标准化器使用了错误的数据范围")
        
        print(f"\n解决方案:")
        print("  ✅ 重新训练模型，确保标准化器使用正确的数据")
        print("  ✅ 验证标准化和反标准化过程")
        print("  ✅ 保存修复后的模型和标准化器")
        
        print(f"\n使用修复后的模型:")
        print("  - 模型文件: displacement_model_fixed.pth")
        print("  - 标准化器: displacement_scaler_fixed.pkl")
        print("  - 训练信息: training_info_fixed.pkl")
        
    else:
        print("\n❌ 模型重新训练失败")

if __name__ == "__main__":
    main()
