"""
真正的实时预测系统
DSA每次回调都直接提供最新数据给预测模型，确保是真正的实时预测
"""

import asyncio
import time
import numpy as np
import pandas as pd
import torch
import os
import matplotlib.pyplot as plt
from collections import deque
from data_collector_3s import DSADataCollector
from real_time_stream_processor import RealTimeStreamProcessor
from real_time_train import train_displacement_model

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class RealTimePredictionSystem:
    """真正的实时预测系统"""
    
    def __init__(self, dll_path, model_path=None, scaler_path=None):
        self.dll_path = dll_path
        self.model_path = model_path
        self.scaler_path = scaler_path
        
        # 预测器和数据收集器
        self.predictor = None
        self.collector = None
        
        # 实时数据存储
        self.real_time_data = deque(maxlen=100000)
        self.prediction_count = 0
        self.start_time = None

        # 统计信息
        self.total_predictions = 0
        self.total_processing_time = 0
        self.prediction_errors = deque(maxlen=100000)
        
    def prediction_callback(self, real_value, prediction, timestamp, data_type):
        """DSA回调触发的预测回调函数"""
        if self.start_time is None:
            self.start_time = timestamp
            print("🚀 开始实时预测...")
        
        # 计算相对时间
        relative_time = timestamp - self.start_time
        
        # 计算预测误差
        error = abs(real_value - prediction)
        self.prediction_errors.append(error)
        
        # 存储实时数据
        data_record = {
            'time': relative_time,
            'real_value': real_value,
            'prediction': prediction,
            'error': error,
            'timestamp': timestamp,
            'data_type': data_type
        }
        self.real_time_data.append(data_record)
        
        self.total_predictions += 1
        
        # 每100个预测显示一次统计
        if self.total_predictions % 100 == 0:
            avg_error = np.mean(list(self.prediction_errors))
            print(f"📊 预测#{self.total_predictions}: 时间={relative_time:.3f}s, "
                  f"真实值={real_value:.6f}μm, 预测值={prediction:.6f}μm, "
                  f"误差={error:.6f}μm, 平均误差={avg_error:.6f}μm")
    
    async def initialize_model(self):
        """初始化或训练模型"""
        print("🔧 初始化预测模型...")
        
        # 检查是否有预训练模型
        if (self.model_path and self.scaler_path and 
            os.path.exists(self.model_path) and os.path.exists(self.scaler_path)):
            print("📂 加载预训练模型...")
            try:
                # 加载模型和标准化器
                from real_time_train import load_model
                model, scaler = load_model(self.model_path, self.scaler_path)
                if model is not None and scaler is not None:
                    print("✅ 预训练模型加载成功")
                    return model, scaler
            except Exception as e:
                print(f"⚠️ 加载预训练模型失败: {e}")
        
        # 检查训练数据
        if not os.path.exists('v1.txt'):
            print("❌ 未找到训练数据文件 v1.txt")
            print("请先运行数据收集程序生成训练数据")
            return None, None
        
        # 训练新模型
        print("🚀 开始训练新模型...")
        model, scaler = train_displacement_model(
            data_file='v1.txt',
            window_size=25,
            hidden_size=128,
            learning_rate=0.001,
            epochs=100,
            train_points=3000
        )
        
        if model is None or scaler is None:
            print("❌ 模型训练失败")
            return None, None
        
        print("✅ 模型训练完成")
        return model, scaler
    
    async def start_real_time_prediction(self, duration_seconds=30):
        """启动真正的实时预测"""
        print("="*60)
        print("🎯 真正的实时预测系统")
        print("每隔10个数据点进行一次预测")
        print("="*60)
        
        # 1. 初始化模型
        model, scaler = await self.initialize_model()
        if model is None or scaler is None:
            return False
        
        # 2. 创建实时处理器
        print("🔧 创建实时处理器...")
        self.predictor = RealTimeStreamProcessor(
            model=model,
            scaler=scaler,
            window_size=25,
            buffer_size=10000,
            enable_akf=True,
            enable_bias_correction=True
        )
        
        # 3. 初始化数据收集器
        print("🔧 初始化DSA数据收集器...")
        self.collector = DSADataCollector(self.dll_path)
        
        # 启用连续模式和实时数据流
        self.collector.enable_continuous_mode()
        self.collector.enable_real_time_streaming()
        
        # 设置实时预测器和回调
        self.collector.set_real_time_predictor(self.predictor)
        self.collector.set_prediction_callback(self.prediction_callback)

        # 设置预测间隔：每隔10个数据点预测一次
        self.collector.set_prediction_interval(10)
        
        # 4. 收集初始化数据
        print("📊 收集初始化数据...")
        if not await self._collect_initialization_data():
            return False
        
        # 5. 开始实时预测
        print(f"🚀 开始{duration_seconds}秒实时预测...")
        print("每隔10个DSA数据点触发一次预测（预测频率: 1000Hz）")
        
        # 启动数据收集（这会自动触发预测）
        if not self.collector.initialize_sdk():
            print("❌ SDK初始化失败")
            return False
        
        if not self.collector.start_collection():
            print("❌ 数据收集启动失败")
            return False
        
        # 等待指定时间
        try:
            await asyncio.sleep(duration_seconds)
        except KeyboardInterrupt:
            print("⚠️ 用户中断预测")

        # 6. 停止收集
        print("🛑 停止数据收集...")
        # 先设置停止标志，防止继续收集数据
        self.collector.is_running = False
        self.collector.stop_collection()
        
        # 7. 生成报告
        await self._generate_report()
        
        return True
    
    async def _collect_initialization_data(self):
        """收集初始化数据"""
        # 临时启动收集器收集初始化数据
        temp_collector = DSADataCollector(self.dll_path)
        temp_collector.enable_real_time_streaming()
        
        if not temp_collector.initialize_sdk():
            print("❌ 临时收集器初始化失败")
            return False
        
        if not temp_collector.start_collection():
            print("❌ 临时数据收集启动失败")
            return False
        
        # 收集25个数据点
        historical_data = []
        timeout_count = 0
        max_timeout = 100  # 10秒超时
        
        while len(historical_data) < 25 and timeout_count < max_timeout:
            data = temp_collector.get_real_time_data_by_type('displacement', timeout=0.1)
            if data is not None:
                historical_data.append(data['value'])
                print(f"收集初始化数据: {len(historical_data)}/25")
            else:
                timeout_count += 1
                await asyncio.sleep(0.1)
        
        temp_collector.stop_collection()
        
        if len(historical_data) < 25:
            print(f"❌ 初始化数据不足: {len(historical_data)}/25")
            return False
        
        # 初始化预测器
        self.predictor.initialize_with_historical_data(historical_data)
        print("✅ 预测器初始化完成")
        return True
    
    async def _generate_report(self):
        """生成预测报告"""
        print("\n" + "="*60)
        print("📊 实时预测统计报告")
        print("="*60)
        
        if len(self.real_time_data) == 0:
            print("❌ 没有收集到预测数据")
            return
        
        # 转换为数组进行分析
        data_array = list(self.real_time_data)
        times = [d['time'] for d in data_array]
        real_values = [d['real_value'] for d in data_array]
        predictions = [d['prediction'] for d in data_array]
        errors = [d['error'] for d in data_array]
        
        # 计算统计指标
        total_time = max(times) - min(times) if len(times) > 1 else 0
        prediction_rate = len(data_array) / total_time if total_time > 0 else 0
        
        mae = np.mean(errors)
        rmse = np.sqrt(np.mean(np.array(errors)**2))
        max_error = np.max(errors)
        min_error = np.min(errors)
        
        print(f"⏱️  预测时长: {total_time:.2f} 秒")
        print(f"📈 预测次数: {len(data_array)}")
        print(f"🎯 预测频率: {prediction_rate:.1f} Hz")
        print(f"📊 平均绝对误差(MAE): {mae:.6f} μm")
        print(f"📊 均方根误差(RMSE): {rmse:.6f} μm")
        print(f"📊 最大误差: {max_error:.6f} μm")
        print(f"📊 最小误差: {min_error:.6f} μm")
        
        # 保存数据
        csv_filename = self._save_prediction_data(data_array)

        # 提示用户使用plot_saved_data.py进行分析
        print("📊 数据已保存，请使用以下命令进行可视化分析:")
        print(f"   python plot_saved_data.py")
        print("   或者直接运行 plot_saved_data.py 来分析最新的CSV文件")
    
    def _save_prediction_data(self, data_array):
        """保存预测数据"""
        filename = f"real_time_predictions_{int(time.time())}.csv"

        df = pd.DataFrame(data_array)
        df.to_csv(filename, index=False, encoding='utf-8')

        print(f"💾 预测数据已保存到: {filename}")
        return filename
    
    def _create_visualization(self, data_array):
        """创建可视化图表（已禁用）"""
        print("📊 可视化图表生成已禁用")
        return

        # 原始可视化代码已注释掉，避免生成图表文件
        # if len(data_array) < 10:
        #     print("⚠️ 数据点太少，跳过可视化")
        #     return
        #
        # times = [d['time'] for d in data_array]
        # real_values = [d['real_value'] for d in data_array]
        # predictions = [d['prediction'] for d in data_array]
        # errors = [d['error'] for d in data_array]
        #
        # fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        # fig.suptitle('DSA实时回调预测结果分析', fontsize=16, fontweight='bold')
        #
        # # 1. 真实值vs预测值
        # axes[0, 0].plot(times, real_values, 'b-', label='真实值', alpha=0.7)
        # axes[0, 0].plot(times, predictions, 'r-', label='预测值', alpha=0.7)
        # axes[0, 0].set_xlabel('时间 (秒)')
        # axes[0, 0].set_ylabel('位移 (μm)')
        # axes[0, 0].set_title('真实值 vs 预测值')
        # axes[0, 0].legend()
        # axes[0, 0].grid(True)
        #
        # # 2. 预测误差
        # axes[0, 1].plot(times, errors, 'g-', alpha=0.7)
        # axes[0, 1].set_xlabel('时间 (秒)')
        # axes[0, 1].set_ylabel('绝对误差 (μm)')
        # axes[0, 1].set_title('预测误差随时间变化')
        # axes[0, 1].grid(True)
        #
        # # 3. 误差分布直方图
        # axes[1, 0].hist(errors, bins=50, alpha=0.7, color='orange')
        # axes[1, 0].set_xlabel('绝对误差 (μm)')
        # axes[1, 0].set_ylabel('频次')
        # axes[1, 0].set_title('误差分布直方图')
        # axes[1, 0].grid(True)
        #
        # # 4. 散点图
        # axes[1, 1].scatter(real_values, predictions, alpha=0.6, s=10)
        # min_val = min(min(real_values), min(predictions))
        # max_val = max(max(real_values), max(predictions))
        # axes[1, 1].plot([min_val, max_val], [min_val, max_val], 'r--', label='理想预测线')
        # axes[1, 1].set_xlabel('真实值 (μm)')
        # axes[1, 1].set_ylabel('预测值 (μm)')
        # axes[1, 1].set_title('预测值 vs 真实值散点图')
        # axes[1, 1].legend()
        # axes[1, 1].grid(True)
        #
        # plt.tight_layout()
        #
        # filename = f"real_time_prediction_analysis_{int(time.time())}.png"
        # plt.savefig(filename, dpi=300, bbox_inches='tight')
        # plt.close()
        #
        # print(f"📊 可视化图表已保存到: {filename}")

async def main():
    """主函数"""
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    # 检查DLL文件
    if not os.path.exists(dll_path):
        print(f"❌ 找不到DLL文件: {dll_path}")
        return
    
    # 创建实时预测系统
    system = RealTimePredictionSystem(
        dll_path=dll_path,
        model_path="displacement_model.pth",
        scaler_path="displacement_scaler.pkl"
    )
    
    # 启动实时预测
    success = await system.start_real_time_prediction(duration_seconds=30)
    
    if success:
        print("✅ 实时预测完成")
    else:
        print("❌ 实时预测失败")

if __name__ == "__main__":
    print("🎯 DSA实时回调预测系统")
    print("每次DSA回调都会触发预测，确保真正的实时性")
    asyncio.run(main())
