#include <iostream>
#include <windows.h>

int main() {
    std::cout << "简单测试程序开始" << std::endl;
    
    // 尝试加载 DLL
    HMODULE hDll = LoadLibrary(L"DSANetSDK.dll");
    if (hDll == NULL) {
        std::cout << "无法加载 DSANetSDK.dll，错误码: " << GetLastError() << std::endl;
        system("pause");
        return 1;
    }
    
    std::cout << "成功加载 DSANetSDK.dll" << std::endl;
    
    // 获取 initialize 函数
    typedef int (*InitializeFunc)();
    InitializeFunc initialize = (InitializeFunc)GetProcAddress(hDll, "initialize");
    
    if (initialize == NULL) {
        std::cout << "无法找到 initialize 函数" << std::endl;
        FreeLibrary(hDll);
        system("pause");
        return 1;
    }
    
    std::cout << "找到 initialize 函数，正在调用..." << std::endl;
    
    // 调用 initialize
    int result = initialize();
    std::cout << "initialize 返回值: " << result << std::endl;
    
    FreeLibrary(hDll);
    std::cout << "测试完成" << std::endl;
    system("pause");
    return 0;
}
