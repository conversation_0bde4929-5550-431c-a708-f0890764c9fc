"""
验证预测速率优化效果
分析 three_seconds_data.txt 中的采样率和处理时间
"""

import pandas as pd
import numpy as np
import os
from pathlib import Path

def analyze_sampling_rate(filename="three_seconds_data.txt"):
    """分析采样率和处理时间"""
    
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        print("请先运行 integrated_real_time_demo.py 生成数据文件")
        return None
    
    print("=" * 70)
    print("预测速率优化效果验证")
    print("=" * 70)
    
    # 读取数据
    try:
        data = pd.read_csv(filename, sep='\t', encoding='utf-8')
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return None
    
    print(f"\n📊 数据统计信息:")
    print(f"   总数据点数: {len(data)}")
    print(f"   时间范围: {data['时间[s]'].min():.3f}s ~ {data['时间[s]'].max():.3f}s")
    
    # 计算采样率
    time_range = data['时间[s]'].max() - data['时间[s]'].min()
    sampling_rate = len(data) / time_range if time_range > 0 else 0
    
    print(f"\n⏱️  采样率分析:")
    print(f"   时间跨度: {time_range:.3f}s")
    print(f"   实际采样率: {sampling_rate:.1f} Hz")
    print(f"   目标采样率: 2000 Hz")
    print(f"   达成率: {sampling_rate/2000*100:.1f}%")
    
    # 按秒统计
    print(f"\n📈 按秒分布:")
    for second in range(int(data['时间[s]'].min()), int(data['时间[s]'].max()) + 1):
        count = len(data[(data['时间[s]'] >= second) & (data['时间[s]'] < second + 1)])
        if count > 0:
            print(f"   第{second}秒: {count} 个点 ({count:.0f} Hz)")
    
    # 处理时间分析
    processing_times = data['处理时间[ms]'].values
    print(f"\n⚡ 处理时间分析:")
    print(f"   平均处理时间: {np.mean(processing_times):.3f} ms")
    print(f"   最小处理时间: {np.min(processing_times):.3f} ms")
    print(f"   最大处理时间: {np.max(processing_times):.3f} ms")
    print(f"   标准差: {np.std(processing_times):.3f} ms")
    
    # 理论最大采样率
    max_theoretical_rate = 1000 / np.mean(processing_times)
    print(f"\n🎯 理论分析:")
    print(f"   平均处理时间: {np.mean(processing_times):.3f} ms")
    print(f"   理论最大采样率: {max_theoretical_rate:.1f} Hz")
    print(f"   实际采样率: {sampling_rate:.1f} Hz")
    print(f"   效率: {sampling_rate/max_theoretical_rate*100:.1f}%")
    
    # 误差分析
    if '仅LSTM误差[μm]' in data.columns and 'LSTM+AKF误差[μm]' in data.columns:
        lstm_errors = data['LSTM(纠偏后)误差[μm]'].dropna().values
        akf_errors = data['LSTM+AKF误差[μm]'].dropna().values
        
        if len(lstm_errors) > 0 and len(akf_errors) > 0:
            print(f"\n📊 误差分析:")
            print(f"   纠偏后LSTM MAE: {np.mean(lstm_errors):.6f} μm")
            print(f"   LSTM+AKF MAE: {np.mean(akf_errors):.6f} μm")
            improvement = (np.mean(lstm_errors) - np.mean(akf_errors)) / np.mean(lstm_errors) * 100
            print(f"   AKF改进: {improvement:.2f}%")
    
    # 性能评分
    print(f"\n🏆 性能评分:")
    if sampling_rate >= 1000:
        score = "⭐⭐⭐⭐⭐ 优秀"
    elif sampling_rate >= 500:
        score = "⭐⭐⭐⭐ 很好"
    elif sampling_rate >= 200:
        score = "⭐⭐⭐ 良好"
    elif sampling_rate >= 100:
        score = "⭐⭐ 一般"
    else:
        score = "⭐ 需要改进"
    
    print(f"   {score}")
    
    # 建议
    print(f"\n💡 优化建议:")
    if sampling_rate < 200:
        print(f"   1. 考虑使用 GPU 加速推理")
        print(f"   2. 实现批量预测而非单点预测")
        print(f"   3. 使用 ONNX Runtime 加速")
        print(f"   4. 优化模型架构，使用更轻量的网络")
    elif sampling_rate < 500:
        print(f"   1. 继续优化异步等待时间")
        print(f"   2. 考虑多线程处理")
        print(f"   3. 监控 CPU 占用率")
    else:
        print(f"   ✅ 采样率已达到良好水平")
    
    print("\n" + "=" * 70)
    
    return {
        'total_points': len(data),
        'time_range': time_range,
        'sampling_rate': sampling_rate,
        'avg_processing_time': np.mean(processing_times),
        'max_theoretical_rate': max_theoretical_rate
    }

def compare_with_previous():
    """与之前的性能对比"""
    print("\n📊 性能对比（优化前后）:")
    print("-" * 70)
    print(f"{'指标':<30} {'优化前':<20} {'优化后':<20}")
    print("-" * 70)
    print(f"{'采样率':<30} {'~60 Hz':<20} {'待测试':<20}")
    print(f"{'异步等待时间':<30} {'50-100 μs':<20} {'10 μs':<20}")
    print(f"{'目标采样率':<30} {'100 点/秒':<20} {'2000 点/秒':<20}")
    print(f"{'预期改进':<30} {'-':<20} {'150-200 Hz':<20}")
    print("-" * 70)

if __name__ == "__main__":
    # 分析当前数据
    results = analyze_sampling_rate()
    
    # 显示对比
    compare_with_previous()
    
    # 保存结果
    if results:
        print("\n✅ 分析完成！")
        print(f"   实际采样率: {results['sampling_rate']:.1f} Hz")
        print(f"   理论最大值: {results['max_theoretical_rate']:.1f} Hz")
        print(f"   效率: {results['sampling_rate']/results['max_theoretical_rate']*100:.1f}%")

