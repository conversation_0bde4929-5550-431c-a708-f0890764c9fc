#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
three_seconds_data.txt 数据可视化分析
绘制LSTM预测、LSTM+AKF预测与真实位移的对比图，以及误差分析图
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_data(filename="three_seconds_data.txt"):
    """加载数据文件"""
    try:
        # 读取数据，跳过表头
        df = pd.read_csv(filename, sep='\t', encoding='utf-8')
        print(f"✅ 成功加载数据文件: {filename}")
        print(f"📊 数据点数: {len(df)}")
        print(f"⏱️  时间范围: {df['时间[s]'].min():.3f} ~ {df['时间[s]'].max():.3f} 秒")
        print(f"📍 位移范围: {df['位移[μm]'].min():.6f} ~ {df['位移[μm]'].max():.6f} μm")
        return df
    except Exception as e:
        print(f"❌ 加载数据文件失败: {e}")
        return None

def plot_displacement_comparison(df, save_path="displacement_comparison_123.png"):
    """绘制位移预测对比图"""
    fig, ax = plt.subplots(1, 1, figsize=(14, 8))
    
    # 提取数据
    time = df['时间[s]'].values
    real_displacement = df['位移[μm]'].values
    lstm_prediction = df['仅LSTM预测[μm]'].values
    akf_prediction = df['预测位移[μm]'].values  # LSTM+AKF预测结果
    
    # 绘制曲线
    ax.plot(time, real_displacement, 'b-', linewidth=2, label='真实位移', alpha=0.8)
    ax.plot(time, lstm_prediction, 'r--', linewidth=1.5, label='仅LSTM预测', alpha=0.7)
    ax.plot(time, akf_prediction, 'g-', linewidth=1.5, label='LSTM+AKF预测', alpha=0.8)
    
    # 设置图形属性
    ax.set_xlabel('时间 [s]', fontsize=12)
    ax.set_ylabel('位移 [μm]', fontsize=12)
    ax.set_title('位移预测对比图 - LSTM vs LSTM+AKF', fontsize=14, fontweight='bold')
    ax.legend(fontsize=11)
    ax.grid(True, alpha=0.3)
    
    # 添加统计信息
    lstm_mae = np.mean(np.abs(real_displacement - lstm_prediction))
    akf_mae = np.mean(np.abs(real_displacement - akf_prediction))
    lstm_rmse = np.sqrt(np.mean((real_displacement - lstm_prediction)**2))
    akf_rmse = np.sqrt(np.mean((real_displacement - akf_prediction)**2))
    
    # 在图上添加统计信息
    stats_text = f'MAE: LSTM={lstm_mae:.6f}μm, AKF={akf_mae:.6f}μm\n'
    stats_text += f'RMSE: LSTM={lstm_rmse:.6f}μm, AKF={akf_rmse:.6f}μm\n'
    stats_text += f'改进: MAE={((lstm_mae-akf_mae)/lstm_mae*100):.1f}%, RMSE={((lstm_rmse-akf_rmse)/lstm_rmse*100):.1f}%'
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✅ 位移对比图已保存: {save_path}")
    return fig

def plot_error_comparison(df, save_path="error_comparison_123.png"):
    """绘制误差对比图"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
    
    # 提取数据
    time = df['时间[s]'].values
    lstm_error = df['仅LSTM误差[μm]'].values
    akf_error = df['LSTM+AKF误差[μm]'].values
    
    # 上图：误差时间序列
    ax1.plot(time, lstm_error, 'r-', linewidth=1.5, label='仅LSTM误差', alpha=0.7)
    ax1.plot(time, akf_error, 'g-', linewidth=1.5, label='LSTM+AKF误差', alpha=0.8)
    ax1.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    
    ax1.set_xlabel('时间 [s]', fontsize=12)
    ax1.set_ylabel('绝对误差 [μm]', fontsize=12)
    ax1.set_title('预测误差时间序列对比', fontsize=13, fontweight='bold')
    ax1.legend(fontsize=11)
    ax1.grid(True, alpha=0.3)
    
    # 下图：误差分布直方图
    bins = 50
    ax2.hist(lstm_error, bins=bins, alpha=0.6, color='red', label='仅LSTM误差', density=True)
    ax2.hist(akf_error, bins=bins, alpha=0.6, color='green', label='LSTM+AKF误差', density=True)
    
    ax2.set_xlabel('绝对误差 [μm]', fontsize=12)
    ax2.set_ylabel('概率密度', fontsize=12)
    ax2.set_title('预测误差分布对比', fontsize=13, fontweight='bold')
    ax2.legend(fontsize=11)
    ax2.grid(True, alpha=0.3)
    
    # 添加统计信息
    lstm_mean = np.mean(lstm_error)
    akf_mean = np.mean(akf_error)
    lstm_std = np.std(lstm_error)
    akf_std = np.std(akf_error)
    
    stats_text = f'LSTM: 均值={lstm_mean:.6f}μm, 标准差={lstm_std:.6f}μm\n'
    stats_text += f'AKF: 均值={akf_mean:.6f}μm, 标准差={akf_std:.6f}μm\n'
    stats_text += f'改进: 均值={((lstm_mean-akf_mean)/lstm_mean*100):.1f}%, 标准差={((lstm_std-akf_std)/lstm_std*100):.1f}%'
    
    ax2.text(0.98, 0.98, stats_text, transform=ax2.transAxes, fontsize=10,
             verticalalignment='top', horizontalalignment='right',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✅ 误差对比图已保存: {save_path}")
    return fig

def plot_improvement_analysis(df, save_path="improvement_analysis_123.png"):
    """绘制AKF改进效果分析图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # 提取数据
    time = df['时间[s]'].values
    improvement_pct = df['AKF相对LSTM改进[%]'].values
    improvement_abs = df['AKF相对LSTM改进[μm]'].values
    
    # 左图：改进百分比时间序列
    ax1.plot(time, improvement_pct, 'purple', linewidth=2, alpha=0.8)
    ax1.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax1.fill_between(time, improvement_pct, 0, where=(improvement_pct >= 0), 
                     color='green', alpha=0.3, label='改进区域')
    ax1.fill_between(time, improvement_pct, 0, where=(improvement_pct < 0), 
                     color='red', alpha=0.3, label='恶化区域')
    
    ax1.set_xlabel('时间 [s]', fontsize=12)
    ax1.set_ylabel('AKF相对LSTM改进 [%]', fontsize=12)
    ax1.set_title('AKF改进效果时间序列', fontsize=13, fontweight='bold')
    ax1.legend(fontsize=11)
    ax1.grid(True, alpha=0.3)
    
    # 右图：改进效果分布
    positive_pct = np.sum(improvement_pct > 0) / len(improvement_pct) * 100
    negative_pct = np.sum(improvement_pct < 0) / len(improvement_pct) * 100
    
    labels = ['改进', '恶化']
    sizes = [positive_pct, negative_pct]
    colors = ['green', 'red']
    explode = (0.05, 0)
    
    ax2.pie(sizes, explode=explode, labels=labels, colors=colors, autopct='%1.1f%%',
            shadow=True, startangle=90)
    ax2.set_title('AKF改进效果分布', fontsize=13, fontweight='bold')
    
    # 添加统计信息
    mean_improvement = np.mean(improvement_pct)
    median_improvement = np.median(improvement_pct)
    max_improvement = np.max(improvement_pct)
    min_improvement = np.min(improvement_pct)
    
    stats_text = f'平均改进: {mean_improvement:.1f}%\n'
    stats_text += f'中位数改进: {median_improvement:.1f}%\n'
    stats_text += f'最大改进: {max_improvement:.1f}%\n'
    stats_text += f'最大恶化: {min_improvement:.1f}%'
    
    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, fontsize=10,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✅ 改进分析图已保存: {save_path}")
    return fig

def print_summary_statistics(df):
    """打印汇总统计信息"""
    print("\n" + "="*60)
    print("📊 数据分析汇总统计")
    print("="*60)
    
    # 基本统计
    real_displacement = df['位移[μm]'].values
    lstm_prediction = df['仅LSTM预测[μm]'].values
    akf_prediction = df['预测位移[μm]'].values
    
    # 误差统计
    lstm_error = df['仅LSTM误差[μm]'].values
    akf_error = df['LSTM+AKF误差[μm]'].values
    
    print(f"📈 数据点数: {len(df)}")
    print(f"⏱️  时间范围: {df['时间[s]'].min():.3f} ~ {df['时间[s]'].max():.3f} 秒")
    print(f"📍 位移范围: {real_displacement.min():.6f} ~ {real_displacement.max():.6f} μm")
    
    print(f"\n🎯 LSTM预测性能:")
    print(f"   MAE: {np.mean(lstm_error):.6f} μm")
    print(f"   RMSE: {np.sqrt(np.mean(lstm_error**2)):.6f} μm")
    print(f"   最大误差: {np.max(lstm_error):.6f} μm")
    
    print(f"\n🎯 LSTM+AKF预测性能:")
    print(f"   MAE: {np.mean(akf_error):.6f} μm")
    print(f"   RMSE: {np.sqrt(np.mean(akf_error**2)):.6f} μm")
    print(f"   最大误差: {np.max(akf_error):.6f} μm")
    
    # 改进统计
    mae_improvement = (np.mean(lstm_error) - np.mean(akf_error)) / np.mean(lstm_error) * 100
    rmse_improvement = (np.sqrt(np.mean(lstm_error**2)) - np.sqrt(np.mean(akf_error**2))) / np.sqrt(np.mean(lstm_error**2)) * 100
    
    print(f"\n🚀 AKF改进效果:")
    print(f"   MAE改进: {mae_improvement:.1f}%")
    print(f"   RMSE改进: {rmse_improvement:.1f}%")
    
    improvement_pct = df['AKF相对LSTM改进[%]'].values
    positive_ratio = np.sum(improvement_pct > 0) / len(improvement_pct) * 100
    print(f"   改进点占比: {positive_ratio:.1f}%")
    print(f"   平均改进: {np.mean(improvement_pct):.1f}%")

def main():
    """主函数"""
    print("🚀 开始分析 three_seconds_data.txt 数据...")
    
    # 加载数据
    df = load_data("three_seconds_data.txt")
    if df is None:
        return
    
    # 绘制图表
    print("\n📊 生成可视化图表...")
    plot_displacement_comparison(df)
    plot_error_comparison(df)
    plot_improvement_analysis(df)
    
    # 打印统计信息
    print_summary_statistics(df)
    
    print(f"\n✅ 分析完成！生成的图表文件:")
    print(f"   📄 displacement_comparison_123.png - 位移预测对比图")
    print(f"   📄 error_comparison_123.png - 误差对比图")
    print(f"   📄 improvement_analysis_123.png - AKF改进效果分析图")
    print("="*60)

if __name__ == "__main__":
    main()
