"""
绘制实际位移值和预测位移对比图，以及误差图
"""

import asyncio
import time
import numpy as np
import os
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from collections import deque
from data_collector_3s import DSADataCollector
from real_time_stream_processor import RealTimeStreamProcessor
from real_time_train import train_displacement_model

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class DisplacementComparisonPlotter:
    """位移对比图绘制器"""

    def __init__(self, dll_path, max_points=2000, time_threshold=0.0001):
        self.dll_path = dll_path
        self.max_points = max_points
        self.time_threshold = time_threshold  # 时间过滤阈值

        # 数据存储
        self.times = deque(maxlen=max_points)
        self.real_values = deque(maxlen=max_points)
        self.predictions = deque(maxlen=max_points)
        self.errors = deque(maxlen=max_points)

        # 统计信息
        self.prediction_count = 0
        self.start_time = None
        self.total_error = 0
        self.filtered_count = 0  # 过滤掉的数据点数量

        # 图形设置
        self.fig = None
        self.axes = None
        self.lines = {}
        
    def prediction_callback(self, real_value, prediction, timestamp, data_type):
        """预测回调函数"""
        if self.start_time is None:
            self.start_time = timestamp
            print(f"🚀 开始收集预测数据用于可视化...")
            print(f"🔍 将过滤掉前 {self.time_threshold} 秒的数据")

        relative_time = timestamp - self.start_time

        # 时间过滤：只保留time_threshold秒以后的数据
        if relative_time <= self.time_threshold:
            self.filtered_count += 1
            return  # 跳过这个数据点

        # 重置时间，使过滤后的数据从0开始
        adjusted_time = relative_time - self.time_threshold
        error = real_value - prediction  # 使用有符号误差

        # 存储数据
        self.times.append(adjusted_time)
        self.real_values.append(real_value)
        self.predictions.append(prediction)
        self.errors.append(error)

        self.prediction_count += 1
        self.total_error += abs(error)

        # 每1000个预测显示一次进度
        if self.prediction_count % 1000 == 0:
            avg_error = self.total_error / self.prediction_count
            print(f"📊 已收集 {self.prediction_count} 个有效预测点，平均误差: {avg_error:.6f}μm")
            print(f"   (已过滤掉 {self.filtered_count} 个初始数据点)")
    
    def setup_plots(self):
        """设置图形界面"""
        self.fig, self.axes = plt.subplots(3, 1, figsize=(15, 12))
        self.fig.suptitle('DSA实时位移预测对比分析', fontsize=16, fontweight='bold')
        
        # 1. 位移对比图
        self.axes[0].set_title('实际位移 vs 预测位移')
        self.axes[0].set_xlabel('时间 (秒)')
        self.axes[0].set_ylabel('位移 (μm)')
        self.axes[0].grid(True, alpha=0.3)
        self.lines['real'] = self.axes[0].plot([], [], 'b-', linewidth=1.5, label='实际位移', alpha=0.8)[0]
        self.lines['pred'] = self.axes[0].plot([], [], 'r-', linewidth=1.5, label='预测位移', alpha=0.8)[0]
        self.axes[0].legend()
        
        # 2. 误差图
        self.axes[1].set_title('预测误差 (预测值 - 实际值)')
        self.axes[1].set_xlabel('时间 (秒)')
        self.axes[1].set_ylabel('误差 (μm)')
        self.axes[1].grid(True, alpha=0.3)
        self.lines['error'] = self.axes[1].plot([], [], 'g-', linewidth=1, label='预测误差', alpha=0.7)[0]
        self.axes[1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
        self.axes[1].legend()
        
        # 3. 误差分布直方图
        self.axes[2].set_title('误差分布直方图')
        self.axes[2].set_xlabel('误差 (μm)')
        self.axes[2].set_ylabel('频次')
        self.axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
    def update_plots(self):
        """更新图形"""
        if len(self.times) < 2:
            return
        
        times_array = np.array(self.times)
        real_array = np.array(self.real_values)
        pred_array = np.array(self.predictions)
        error_array = np.array(self.errors)
        
        # 更新位移对比图
        self.lines['real'].set_data(times_array, real_array)
        self.lines['pred'].set_data(times_array, pred_array)
        
        # 更新误差图
        self.lines['error'].set_data(times_array, error_array)
        
        # 自动调整坐标轴范围
        for ax in self.axes[:2]:
            ax.relim()
            ax.autoscale_view()
        
        # 更新误差分布直方图
        self.axes[2].clear()
        self.axes[2].hist(error_array, bins=50, alpha=0.7, color='orange', edgecolor='black')
        self.axes[2].axvline(x=np.mean(error_array), color='red', linestyle='--', linewidth=2,
                            label=f'平均误差: {np.mean(error_array):.6f}μm')
        self.axes[2].axvline(x=0, color='black', linestyle='-', alpha=0.5)
        self.axes[2].set_title('误差分布直方图')
        self.axes[2].set_xlabel('误差 (μm)')
        self.axes[2].set_ylabel('频次')
        self.axes[2].legend()
        self.axes[2].grid(True, alpha=0.3)
        
        # 添加统计信息
        if len(error_array) > 0:
            mae = np.mean(np.abs(error_array))
            rmse = np.sqrt(np.mean(error_array**2))
            std_error = np.std(error_array)
            
            stats_text = f'统计信息:\nMAE: {mae:.6f}μm\nRMSE: {rmse:.6f}μm\nSTD: {std_error:.6f}μm\n数据点: {len(error_array)}'
            self.axes[2].text(0.02, 0.98, stats_text, transform=self.axes[2].transAxes, 
                             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.draw()
    
    async def run_real_time_plotting(self, duration=30):
        """运行实时绘图"""
        print("="*60)
        print("📊 DSA实时位移预测对比可视化")
        print("="*60)
        
        # 1. 检查训练数据
        if not os.path.exists('v1.txt'):
            print("❌ 未找到训练数据 v1.txt")
            print("请先运行数据收集程序")
            return False
        
        # 2. 训练模型
        print("🚀 训练预测模型...")
        model, scaler = train_displacement_model(
            data_file='v1.txt',
            window_size=25,
            hidden_size=128,
            learning_rate=0.001,
            epochs=100,
            train_points=3000
        )
        
        if model is None or scaler is None:
            print("❌ 模型训练失败")
            return False
        
        print("✅ 模型训练完成")
        
        # 3. 创建预测器
        print("🔧 创建预测器...")
        predictor = RealTimeStreamProcessor(
            model=model,
            scaler=scaler,
            window_size=25,
            enable_akf=True,
            enable_bias_correction=True
        )
        
        # 4. 创建数据收集器
        print("🔧 创建数据收集器...")
        collector = DSADataCollector(self.dll_path)
        collector.enable_continuous_mode()
        collector.enable_real_time_streaming()
        
        # 5. 初始化SDK和收集器
        print("📊 初始化数据收集...")
        if not collector.initialize_sdk():
            print("❌ SDK初始化失败")
            return False
        
        if not collector.start_collection():
            print("❌ 数据收集启动失败")
            return False
        
        # 6. 收集初始化数据
        print("📊 收集初始化数据...")
        historical_data = []
        timeout_count = 0
        
        while len(historical_data) < 25 and timeout_count < 50:
            data = collector.get_real_time_data_by_type('displacement', timeout=0.1)
            if data is not None:
                historical_data.append(data['value'])
                if len(historical_data) % 5 == 0:
                    print(f"收集初始化数据: {len(historical_data)}/25")
            else:
                timeout_count += 1
                await asyncio.sleep(0.1)
        
        if len(historical_data) < 25:
            print(f"❌ 初始化数据不足: {len(historical_data)}/25")
            collector.stop_collection()
            return False
        
        # 7. 初始化预测器
        predictor.initialize_with_historical_data(historical_data)
        print("✅ 预测器初始化完成")
        
        # 8. 设置实时预测
        collector.set_real_time_predictor(predictor)
        collector.set_prediction_callback(self.prediction_callback)
        
        # 9. 设置图形界面
        print("🎨 设置可视化界面...")
        self.setup_plots()
        
        print(f"🚀 开始{duration}秒实时预测可视化...")
        print("图形窗口将显示实时更新的位移对比图")
        
        # 10. 实时数据收集和绘图
        plt.ion()  # 开启交互模式
        plt.show()
        
        start_time = time.time()
        update_interval = 0.5  # 每0.5秒更新一次图形
        last_update = start_time
        
        try:
            while time.time() - start_time < duration:
                current_time = time.time()
                
                # 定期更新图形
                if current_time - last_update >= update_interval:
                    self.update_plots()
                    last_update = current_time
                
                await asyncio.sleep(0.1)
                
        except KeyboardInterrupt:
            print("⚠️ 用户中断可视化")
        
        # 11. 停止收集
        collector.stop_collection()
        
        # 12. 最终更新和保存
        print("📊 生成最终分析图表...")
        self.update_plots()
        
        # 保存图表
        timestamp = int(time.time())
        filename = f"displacement_comparison_{timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 位移对比图已保存到: {filename}")
        
        # 保存数据
        self.save_data(timestamp)
        
        # 显示最终统计
        self.show_final_statistics()
        
        plt.ioff()  # 关闭交互模式
        input("按回车键关闭图形窗口...")
        plt.close()
        
        return True
    
    def save_data(self, timestamp):
        """保存数据到CSV文件"""
        if len(self.times) == 0:
            return
        
        import pandas as pd
        
        data = {
            'time': list(self.times),
            'real_displacement': list(self.real_values),
            'predicted_displacement': list(self.predictions),
            'error': list(self.errors)
        }
        
        df = pd.DataFrame(data)
        filename = f"displacement_data_{timestamp}.csv"
        df.to_csv(filename, index=False)
        print(f"💾 位移数据已保存到: {filename}")
    
    def show_final_statistics(self):
        """显示最终统计信息"""
        if len(self.errors) == 0:
            return
        
        errors = np.array(self.errors)
        abs_errors = np.abs(errors)
        
        print("\n" + "="*60)
        print("📊 最终统计分析")
        print("="*60)
        print(f"📈 有效预测次数: {len(self.errors)}")
        print(f"🗑️ 过滤掉的数据点: {self.filtered_count}")
        print(f"📊 总数据点: {len(self.errors) + self.filtered_count}")
        print(f"⏱️  有效数据时长: {max(self.times):.2f} 秒")
        print(f"🎯 有效预测频率: {len(self.errors)/max(self.times):.1f} Hz")
        
        print(f"\n📊 误差统计:")
        print(f"   平均绝对误差(MAE): {np.mean(abs_errors):.6f} μm")
        print(f"   均方根误差(RMSE): {np.sqrt(np.mean(errors**2)):.6f} μm")
        print(f"   误差标准差: {np.std(errors):.6f} μm")
        print(f"   最大正误差: {np.max(errors):.6f} μm")
        print(f"   最大负误差: {np.min(errors):.6f} μm")
        print(f"   误差范围: [{np.min(errors):.6f}, {np.max(errors):.6f}] μm")
        
        print(f"\n📈 位移统计:")
        real_array = np.array(self.real_values)
        pred_array = np.array(self.predictions)
        print(f"   实际位移范围: [{np.min(real_array):.6f}, {np.max(real_array):.6f}] μm")
        print(f"   预测位移范围: [{np.min(pred_array):.6f}, {np.max(pred_array):.6f}] μm")
        print(f"   位移变化幅度: {np.max(real_array) - np.min(real_array):.6f} μm")
        
        # 相关性分析
        correlation = np.corrcoef(real_array, pred_array)[0, 1]
        print(f"   预测-实际相关系数: {correlation:.6f}")
        
        if correlation > 0.95:
            print(f"   ✅ 预测质量: 优秀")
        elif correlation > 0.9:
            print(f"   ✅ 预测质量: 良好")
        elif correlation > 0.8:
            print(f"   ⚠️ 预测质量: 一般")
        else:
            print(f"   ❌ 预测质量: 较差")

async def main():
    """主函数"""
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"

    # 检查DLL文件
    if not os.path.exists(dll_path):
        print(f"❌ 找不到DLL文件: {dll_path}")
        return

    # 获取用户输入
    time_threshold_input = input("请输入时间过滤阈值（秒，默认0.0001秒）: ").strip()
    try:
        time_threshold = float(time_threshold_input) if time_threshold_input else 0.0001
    except ValueError:
        time_threshold = 0.0001
        print(f"⚠️ 输入无效，使用默认值: {time_threshold} 秒")

    duration = input("请输入可视化时长（秒，默认30秒）: ").strip()
    try:
        duration = int(duration) if duration else 30
    except ValueError:
        duration = 30

    # 创建绘图器
    plotter = DisplacementComparisonPlotter(dll_path, max_points=5000, time_threshold=time_threshold)

    # 运行实时绘图
    success = await plotter.run_real_time_plotting(duration=duration)
    
    if success:
        print("✅ 位移对比可视化完成")
    else:
        print("❌ 位移对比可视化失败")

if __name__ == "__main__":
    print("📊 DSA实时位移预测对比可视化程序")
    print("将生成实际位移vs预测位移对比图和误差分析图")
    asyncio.run(main())
