"""
测试采样率优化效果
"""

import asyncio
import time
import numpy as np
from real_time_data_bridge import DSADataSource, RealTimeVisualizationBridge


async def test_sampling_rates():
    """测试不同采样策略的效果"""
    print("="*60)
    print("采样率测试工具")
    print("="*60)
    
    # 检查DLL文件
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    # 创建数据源
    print("步骤1: 初始化数据源...")
    bridge = RealTimeVisualizationBridge(dll_path)
    
    # 启动位移数据收集
    if not await bridge.start_displacement_visualization():
        print("启动DSA位移数据收集失败")
        return
    displacement_source = bridge.get_displacement_source()
    
    # 等待数据收集稳定
    print("步骤2: 等待数据收集稳定...")
    await asyncio.sleep(2.0)
    
    # 测试不同的读取策略
    test_duration = 3.0  # 每个测试3秒
    
    strategies = [
        ("标准桥接器读取", "bridge"),
        ("直接队列读取(0.5ms超时)", "direct_timeout"),
        ("直接队列读取(非阻塞)", "direct_nonblock"),
        ("超高频轮询", "ultra_polling")
    ]
    
    results = {}
    
    for strategy_name, strategy_type in strategies:
        print(f"\n{'='*60}")
        print(f"测试策略: {strategy_name}")
        print(f"{'='*60}")
        
        start_time = time.time()
        data_count = 0
        read_attempts = 0
        successful_reads = 0
        
        while time.time() - start_time < test_duration:
            if strategy_type == "bridge":
                # 使用标准桥接器
                data = await displacement_source.read_data()
                read_attempts += 1
                if data is not None:
                    data_count += 1
                    successful_reads += 1
                await asyncio.sleep(0.001)  # 1ms
                
            elif strategy_type == "direct_timeout":
                # 直接访问队列，有超时
                data = displacement_source.collector.get_real_time_data_by_type('displacement', timeout=0.0005)
                read_attempts += 1
                if data is not None:
                    data_count += 1
                    successful_reads += 1
                await asyncio.sleep(0.0001)  # 0.1ms
                
            elif strategy_type == "direct_nonblock":
                # 直接访问队列，非阻塞
                data = displacement_source.collector.get_real_time_data_by_type('displacement', timeout=0)
                read_attempts += 1
                if data is not None:
                    data_count += 1
                    successful_reads += 1
                await asyncio.sleep(0.0001)  # 0.1ms
                
            elif strategy_type == "ultra_polling":
                # 超高频轮询
                for _ in range(20):
                    data = displacement_source.collector.get_real_time_data_by_type('displacement', timeout=0)
                    read_attempts += 1
                    if data is not None:
                        data_count += 1
                        successful_reads += 1
                await asyncio.sleep(0.00001)  # 10μs
        
        elapsed = time.time() - start_time
        actual_rate = data_count / elapsed
        read_efficiency = (successful_reads / read_attempts * 100) if read_attempts > 0 else 0
        queue_size = displacement_source.collector.get_real_time_queue_size()
        
        results[strategy_name] = {
            'rate': actual_rate,
            'efficiency': read_efficiency,
            'attempts': read_attempts,
            'successful': successful_reads,
            'queue_size': queue_size
        }
        
        print(f"📈 结果:")
        print(f"  - 总耗时: {elapsed:.2f}s")
        print(f"  - 读取尝试: {read_attempts}")
        print(f"  - 成功读取: {successful_reads}")
        print(f"  - 实际采样率: {actual_rate:.1f} Hz")
        print(f"  - 读取效率: {read_efficiency:.1f}%")
        print(f"  - 队列大小: {queue_size}")
    
    # 停止数据收集
    print(f"\n{'='*60}")
    print("停止数据收集...")
    bridge.stop_all()
    
    # 输出对比结果
    print(f"\n{'='*60}")
    print("📊 采样率对比结果")
    print(f"{'='*60}")
    
    print(f"{'策略':<20} {'采样率(Hz)':<12} {'效率(%)':<10} {'队列大小':<10}")
    print("-" * 60)
    
    for strategy_name, result in results.items():
        print(f"{strategy_name:<20} {result['rate']:<12.1f} {result['efficiency']:<10.1f} {result['queue_size']:<10}")
    
    # 找出最佳策略
    best_strategy = max(results.items(), key=lambda x: x[1]['rate'])
    print(f"\n🏆 最佳策略: {best_strategy[0]} ({best_strategy[1]['rate']:.1f} Hz)")
    
    # 分析建议
    print(f"\n💡 优化建议:")
    max_rate = best_strategy[1]['rate']
    if max_rate < 100:
        print("  ⚠️ 所有策略的采样率都较低，可能是DSA设备本身的限制")
        print("  建议检查:")
        print("    1. DSA设备配置是否正确")
        print("    2. 网络连接是否稳定")
        print("    3. 设备是否正在产生数据")
    elif max_rate < 200:
        print("  📈 采样率中等，可以进一步优化:")
        print("    1. 增加队列大小")
        print("    2. 减少异步等待时间")
        print("    3. 使用多线程读取")
    else:
        print("  ✅ 采样率良好，已达到预期目标")


if __name__ == "__main__":
    asyncio.run(test_sampling_rates())
