"""
数据质量优化模块
提供数据预处理、异常值检测和数据清洗功能，提高训练数据质量
"""

import numpy as np
import pandas as pd
from scipy import signal
from scipy.stats import zscore
from sklearn.preprocessing import MinMaxScaler, RobustScaler
import warnings

warnings.filterwarnings("ignore")


class DataQualityOptimizer:
    """数据质量优化器"""
    
    def __init__(self, outlier_threshold=3.0, noise_reduction=True, smoothing_window=5):
        """
        初始化数据质量优化器
        
        Args:
            outlier_threshold: 异常值检测阈值（Z-score）
            noise_reduction: 是否启用噪声降低
            smoothing_window: 平滑窗口大小
        """
        self.outlier_threshold = outlier_threshold
        self.noise_reduction = noise_reduction
        self.smoothing_window = smoothing_window
        self.scaler = None
        
    def detect_outliers_zscore(self, data):
        """使用Z-score方法检测异常值"""
        z_scores = np.abs(zscore(data))
        outliers = z_scores > self.outlier_threshold
        return outliers
    
    def detect_outliers_iqr(self, data):
        """使用IQR方法检测异常值"""
        Q1 = np.percentile(data, 25)
        Q3 = np.percentile(data, 75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        outliers = (data < lower_bound) | (data > upper_bound)
        return outliers
    
    def detect_outliers_combined(self, data):
        """结合多种方法检测异常值"""
        zscore_outliers = self.detect_outliers_zscore(data)
        iqr_outliers = self.detect_outliers_iqr(data)
        
        # 只有当两种方法都认为是异常值时才标记为异常值
        combined_outliers = zscore_outliers & iqr_outliers
        return combined_outliers
    
    def remove_outliers(self, data, method='combined'):
        """移除异常值"""
        if method == 'zscore':
            outliers = self.detect_outliers_zscore(data)
        elif method == 'iqr':
            outliers = self.detect_outliers_iqr(data)
        else:  # combined
            outliers = self.detect_outliers_combined(data)
        
        # 使用线性插值替换异常值
        cleaned_data = data.copy()
        outlier_indices = np.where(outliers)[0]
        
        for idx in outlier_indices:
            # 找到前后的正常值进行插值
            left_idx = idx - 1
            right_idx = idx + 1
            
            # 向左寻找正常值
            while left_idx >= 0 and outliers[left_idx]:
                left_idx -= 1
            
            # 向右寻找正常值
            while right_idx < len(data) and outliers[right_idx]:
                right_idx += 1
            
            # 进行插值
            if left_idx >= 0 and right_idx < len(data):
                cleaned_data[idx] = (data[left_idx] + data[right_idx]) / 2
            elif left_idx >= 0:
                cleaned_data[idx] = data[left_idx]
            elif right_idx < len(data):
                cleaned_data[idx] = data[right_idx]
        
        return cleaned_data, outliers
    
    def apply_noise_reduction(self, data):
        """应用噪声降低技术"""
        if not self.noise_reduction:
            return data
        
        # 使用Savitzky-Golay滤波器进行平滑
        if len(data) > self.smoothing_window:
            smoothed = signal.savgol_filter(data, 
                                          window_length=min(self.smoothing_window, len(data)//2*2-1),
                                          polyorder=2)
        else:
            # 如果数据太少，使用简单的移动平均
            smoothed = self.apply_moving_average(data)
        
        return smoothed
    
    def apply_moving_average(self, data):
        """应用移动平均平滑"""
        window = min(self.smoothing_window, len(data))
        if window <= 1:
            return data
        
        smoothed = np.convolve(data, np.ones(window)/window, mode='same')
        return smoothed
    
    def normalize_data(self, data, method='minmax'):
        """数据标准化"""
        data_reshaped = data.reshape(-1, 1)
        
        if method == 'minmax':
            if self.scaler is None:
                self.scaler = MinMaxScaler(feature_range=(-1, 1))
                normalized = self.scaler.fit_transform(data_reshaped)
            else:
                normalized = self.scaler.transform(data_reshaped)
        elif method == 'robust':
            if self.scaler is None:
                self.scaler = RobustScaler()
                normalized = self.scaler.fit_transform(data_reshaped)
            else:
                normalized = self.scaler.transform(data_reshaped)
        else:
            # Z-score标准化
            normalized = zscore(data_reshaped)
        
        return normalized.flatten()
    
    def optimize_data_quality(self, data, remove_outliers=True, apply_smoothing=True):
        """
        综合优化数据质量
        
        Args:
            data: 原始数据
            remove_outliers: 是否移除异常值
            apply_smoothing: 是否应用平滑
        
        Returns:
            tuple: (优化后的数据, 异常值标记, 质量报告)
        """
        original_data = data.copy()
        optimized_data = data.copy()
        outliers = np.zeros(len(data), dtype=bool)
        
        # 1. 异常值检测和处理
        if remove_outliers:
            optimized_data, outliers = self.remove_outliers(optimized_data)
        
        # 2. 噪声降低
        if apply_smoothing:
            optimized_data = self.apply_noise_reduction(optimized_data)
        
        # 3. 生成质量报告
        quality_report = self.generate_quality_report(original_data, optimized_data, outliers)
        
        return optimized_data, outliers, quality_report
    
    def generate_quality_report(self, original_data, optimized_data, outliers):
        """生成数据质量报告"""
        report = {
            'original_stats': {
                'mean': np.mean(original_data),
                'std': np.std(original_data),
                'min': np.min(original_data),
                'max': np.max(original_data),
                'range': np.max(original_data) - np.min(original_data)
            },
            'optimized_stats': {
                'mean': np.mean(optimized_data),
                'std': np.std(optimized_data),
                'min': np.min(optimized_data),
                'max': np.max(optimized_data),
                'range': np.max(optimized_data) - np.min(optimized_data)
            },
            'outliers': {
                'count': np.sum(outliers),
                'percentage': np.sum(outliers) / len(outliers) * 100,
                'indices': np.where(outliers)[0].tolist()
            },
            'noise_reduction': {
                'original_variance': np.var(original_data),
                'optimized_variance': np.var(optimized_data),
                'variance_reduction': (np.var(original_data) - np.var(optimized_data)) / np.var(original_data) * 100
            }
        }
        
        return report
    
    def print_quality_report(self, report):
        """打印数据质量报告"""
        print("=" * 60)
        print("数据质量优化报告")
        print("=" * 60)
        
        print("\n📊 原始数据统计:")
        orig = report['original_stats']
        print(f"  均值: {orig['mean']:.6f} μm")
        print(f"  标准差: {orig['std']:.6f} μm")
        print(f"  范围: {orig['min']:.6f} ~ {orig['max']:.6f} μm")
        print(f"  数据跨度: {orig['range']:.6f} μm")
        
        print("\n📈 优化后数据统计:")
        opt = report['optimized_stats']
        print(f"  均值: {opt['mean']:.6f} μm")
        print(f"  标准差: {opt['std']:.6f} μm")
        print(f"  范围: {opt['min']:.6f} ~ {opt['max']:.6f} μm")
        print(f"  数据跨度: {opt['range']:.6f} μm")
        
        print("\n🔍 异常值检测:")
        outliers = report['outliers']
        print(f"  检测到异常值: {outliers['count']} 个")
        print(f"  异常值比例: {outliers['percentage']:.2f}%")
        
        print("\n🔧 噪声降低:")
        noise = report['noise_reduction']
        print(f"  原始方差: {noise['original_variance']:.8f}")
        print(f"  优化后方差: {noise['optimized_variance']:.8f}")
        print(f"  方差降低: {noise['variance_reduction']:.2f}%")
        
        print("=" * 60)


def optimize_training_data(data_file='v1.txt', output_file='v1_optimized.txt'):
    """
    优化训练数据文件
    
    Args:
        data_file: 输入数据文件
        output_file: 输出优化后的数据文件
    
    Returns:
        bool: 是否成功优化
    """
    try:
        # 读取数据
        print(f"📂 读取数据文件: {data_file}")
        data = pd.read_csv(data_file, sep='\t', encoding='utf-8')
        displacement_data = data.iloc[:, 1].values
        
        print(f"📊 原始数据点数: {len(displacement_data)}")
        
        # 创建优化器
        optimizer = DataQualityOptimizer(
            outlier_threshold=3.0,
            noise_reduction=True,
            smoothing_window=5
        )
        
        # 优化数据质量
        optimized_data, outliers, quality_report = optimizer.optimize_data_quality(
            displacement_data,
            remove_outliers=True,
            apply_smoothing=True
        )
        
        # 打印质量报告
        optimizer.print_quality_report(quality_report)
        
        # 保存优化后的数据
        optimized_df = data.copy()
        optimized_df.iloc[:, 1] = optimized_data
        
        optimized_df.to_csv(output_file, sep='\t', index=False, encoding='utf-8')
        print(f"\n💾 优化后的数据已保存到: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据优化失败: {e}")
        return False


if __name__ == "__main__":
    # 测试数据质量优化
    print("数据质量优化模块测试")
    optimize_training_data()
