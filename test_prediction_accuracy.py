"""
测试DSA实时回调预测系统的预测精度
验证修复后的预测误差是否正常（不再为0）
"""

import asyncio
import time
import numpy as np
import os
import matplotlib.pyplot as plt
from data_collector_3s import DSADataCollector
from real_time_stream_processor import RealTimeStreamProcessor
from real_time_train import train_displacement_model

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PredictionAccuracyTest:
    """预测精度测试"""
    
    def __init__(self, dll_path):
        self.dll_path = dll_path
        self.prediction_count = 0
        self.start_time = None
        self.predictions = []
        self.zero_error_count = 0
        
    def prediction_callback(self, real_value, prediction, timestamp, data_type):
        """预测回调函数 - 专注预测精度分析"""
        if self.start_time is None:
            self.start_time = timestamp
            print("🚀 开始接收实时预测结果...")
            print("📊 预测精度分析:")
            print("   格式: 预测#N: 真实值 | 预测值 | 误差 | 状态")
        
        relative_time = timestamp - self.start_time
        error = abs(real_value - prediction)
        
        # 检查是否为零误差
        is_zero_error = error < 1e-10  # 考虑浮点精度
        if is_zero_error:
            self.zero_error_count += 1
        
        self.prediction_count += 1
        self.predictions.append({
            'prediction_num': self.prediction_count,
            'relative_time': relative_time,
            'real_value': real_value,
            'prediction': prediction,
            'error': error,
            'is_zero_error': is_zero_error
        })
        
        # 显示详细的预测信息
        status = "零误差" if is_zero_error else "正常"
        if self.prediction_count <= 50 or is_zero_error or self.prediction_count % 100 == 0:
            print(f"   预测#{self.prediction_count}: {real_value:.6f}μm | {prediction:.6f}μm | {error:.6f}μm | {status}")
    
    async def run_test(self, duration=20):
        """运行预测精度测试"""
        print("="*60)
        print("🧪 DSA实时回调预测系统 - 预测精度测试")
        print("="*60)
        
        # 1. 检查训练数据
        if not os.path.exists('v1.txt'):
            print("❌ 未找到训练数据 v1.txt")
            print("请先运行数据收集程序")
            return False
        
        # 2. 训练模型
        print("🚀 训练预测模型...")
        model, scaler = train_displacement_model(
            data_file='v1.txt',
            window_size=25,
            hidden_size=128,
            learning_rate=0.001,
            epochs=100,
            train_points=3000
        )
        
        if model is None or scaler is None:
            print("❌ 模型训练失败")
            return False
        
        print("✅ 模型训练完成")
        
        # 3. 创建预测器
        print("🔧 创建预测器...")
        predictor = RealTimeStreamProcessor(
            model=model,
            scaler=scaler,
            window_size=25,
            enable_akf=True,
            enable_bias_correction=True
        )
        
        # 4. 创建数据收集器
        print("🔧 创建数据收集器...")
        collector = DSADataCollector(self.dll_path)
        collector.enable_continuous_mode()
        collector.enable_real_time_streaming()
        
        # 5. 收集初始化数据
        print("📊 收集初始化数据...")
        if not collector.initialize_sdk():
            print("❌ SDK初始化失败")
            return False
        
        if not collector.start_collection():
            print("❌ 数据收集启动失败")
            return False
        
        # 收集25个初始化数据点
        historical_data = []
        timeout_count = 0
        
        while len(historical_data) < 25 and timeout_count < 50:
            data = collector.get_real_time_data_by_type('displacement', timeout=0.1)
            if data is not None:
                historical_data.append(data['value'])
                if len(historical_data) % 5 == 0:
                    print(f"收集初始化数据: {len(historical_data)}/25")
            else:
                timeout_count += 1
                await asyncio.sleep(0.1)
        
        if len(historical_data) < 25:
            print(f"❌ 初始化数据不足: {len(historical_data)}/25")
            collector.stop_collection()
            return False
        
        # 初始化预测器
        predictor.initialize_with_historical_data(historical_data)
        print("✅ 预测器初始化完成")
        
        # 6. 设置实时预测
        collector.set_real_time_predictor(predictor)
        collector.set_prediction_callback(self.prediction_callback)
        
        print(f"🚀 开始{duration}秒预测精度测试...")
        print("验证修复后的预测误差是否正常")
        
        # 7. 等待测试完成
        try:
            await asyncio.sleep(duration)
        except KeyboardInterrupt:
            print("⚠️ 用户中断测试")
        
        # 8. 停止收集
        collector.stop_collection()
        
        # 9. 分析预测精度
        self.analyze_prediction_accuracy()
        
        # 10. 生成可视化图表
        self.create_accuracy_visualization()
        
        return True
    
    def analyze_prediction_accuracy(self):
        """分析预测精度"""
        print("\n" + "="*60)
        print("📊 预测精度分析结果")
        print("="*60)
        
        if len(self.predictions) == 0:
            print("❌ 没有收集到预测数据")
            return
        
        # 提取数据
        errors = [p['error'] for p in self.predictions]
        real_values = [p['real_value'] for p in self.predictions]
        predictions = [p['prediction'] for p in self.predictions]
        relative_times = [p['relative_time'] for p in self.predictions]
        
        # 基本统计
        total_time = max(relative_times) - min(relative_times) if len(relative_times) > 1 else 0
        prediction_rate = len(self.predictions) / total_time if total_time > 0 else 0
        
        mae = np.mean(errors)
        rmse = np.sqrt(np.mean(np.array(errors)**2))
        max_error = np.max(errors)
        min_error = np.min(errors)
        std_error = np.std(errors)
        
        print(f"⏱️  测试时长: {total_time:.2f} 秒")
        print(f"📈 预测次数: {len(self.predictions)}")
        print(f"🎯 预测频率: {prediction_rate:.1f} Hz")
        print(f"📊 平均绝对误差(MAE): {mae:.6f} μm")
        print(f"📊 均方根误差(RMSE): {rmse:.6f} μm")
        print(f"📊 误差标准差: {std_error:.6f} μm")
        print(f"📊 最大误差: {max_error:.6f} μm")
        print(f"📊 最小误差: {min_error:.6f} μm")
        
        # 零误差分析
        zero_error_ratio = self.zero_error_count / len(self.predictions) * 100
        print(f"\n🔍 零误差分析:")
        print(f"   零误差预测数量: {self.zero_error_count}/{len(self.predictions)}")
        print(f"   零误差比例: {zero_error_ratio:.2f}%")
        
        if zero_error_ratio > 50:
            print(f"   ❌ 零误差比例过高，预测系统可能仍有问题")
        elif zero_error_ratio > 10:
            print(f"   ⚠️ 零误差比例较高，需要进一步检查")
        elif zero_error_ratio > 1:
            print(f"   ✅ 零误差比例正常，偶尔出现零误差是可接受的")
        else:
            print(f"   ✅ 零误差比例很低，预测系统工作正常")
        
        # 误差分布分析
        error_ranges = {
            '< 0.001μm': sum(1 for e in errors if e < 0.001),
            '0.001-0.01μm': sum(1 for e in errors if 0.001 <= e < 0.01),
            '0.01-0.1μm': sum(1 for e in errors if 0.01 <= e < 0.1),
            '≥ 0.1μm': sum(1 for e in errors if e >= 0.1)
        }
        
        print(f"\n📈 误差分布:")
        for range_name, count in error_ranges.items():
            percentage = count / len(errors) * 100
            print(f"   {range_name}: {count} ({percentage:.1f}%)")
        
        # 预测质量评估
        print(f"\n🎯 预测质量评估:")
        
        # 计算相关系数
        correlation = np.corrcoef(real_values, predictions)[0, 1]
        print(f"   预测-真实值相关系数: {correlation:.4f}")
        
        # 计算相对误差
        relative_errors = [abs(e/r) for e, r in zip(errors, real_values) if abs(r) > 1e-10]
        if relative_errors:
            avg_relative_error = np.mean(relative_errors) * 100
            print(f"   平均相对误差: {avg_relative_error:.2f}%")
        
        # 预测稳定性
        if len(errors) > 10:
            # 计算误差的变异系数
            cv_error = std_error / mae if mae > 0 else float('inf')
            print(f"   误差变异系数: {cv_error:.4f}")
            
            if cv_error < 0.5:
                stability = "优秀"
            elif cv_error < 1.0:
                stability = "良好"
            elif cv_error < 2.0:
                stability = "一般"
            else:
                stability = "较差"
            
            print(f"   预测稳定性: {stability}")
        
        # 显示前10个和后10个预测的详细信息
        print(f"\n📋 前10个预测详情:")
        for i, pred in enumerate(self.predictions[:10]):
            status = "零误差" if pred['is_zero_error'] else "正常"
            print(f"  {i+1:2d}. 真实={pred['real_value']:.6f}μm, "
                  f"预测={pred['prediction']:.6f}μm, "
                  f"误差={pred['error']:.6f}μm ({status})")
        
        if len(self.predictions) > 10:
            print(f"\n📋 后10个预测详情:")
            for i, pred in enumerate(self.predictions[-10:], len(self.predictions)-9):
                status = "零误差" if pred['is_zero_error'] else "正常"
                print(f"  {i:2d}. 真实={pred['real_value']:.6f}μm, "
                      f"预测={pred['prediction']:.6f}μm, "
                      f"误差={pred['error']:.6f}μm ({status})")
    
    def create_accuracy_visualization(self):
        """创建预测精度可视化图表"""
        if len(self.predictions) < 10:
            print("⚠️ 数据点太少，跳过可视化")
            return
        
        # 提取数据
        times = [p['relative_time'] for p in self.predictions]
        real_values = [p['real_value'] for p in self.predictions]
        predictions = [p['prediction'] for p in self.predictions]
        errors = [p['error'] for p in self.predictions]
        zero_errors = [p['is_zero_error'] for p in self.predictions]
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('DSA实时预测精度分析', fontsize=16, fontweight='bold')
        
        # 1. 真实值vs预测值对比
        axes[0, 0].plot(times, real_values, 'b-', alpha=0.7, linewidth=1, label='真实值')
        axes[0, 0].plot(times, predictions, 'r-', alpha=0.7, linewidth=1, label='预测值')
        axes[0, 0].set_xlabel('时间 (秒)')
        axes[0, 0].set_ylabel('位移 (μm)')
        axes[0, 0].set_title('真实值 vs 预测值')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 预测误差随时间变化
        # 用不同颜色标记零误差点
        normal_times = [t for t, z in zip(times, zero_errors) if not z]
        normal_errors = [e for e, z in zip(errors, zero_errors) if not z]
        zero_times = [t for t, z in zip(times, zero_errors) if z]
        zero_error_values = [e for e, z in zip(errors, zero_errors) if z]
        
        if normal_times:
            axes[0, 1].plot(normal_times, normal_errors, 'g-', alpha=0.7, linewidth=1, label='正常误差')
        if zero_times:
            axes[0, 1].scatter(zero_times, zero_error_values, color='red', s=20, alpha=0.8, label='零误差')
        
        axes[0, 1].set_xlabel('时间 (秒)')
        axes[0, 1].set_ylabel('绝对误差 (μm)')
        axes[0, 1].set_title('预测误差随时间变化')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 误差分布直方图
        axes[1, 0].hist(errors, bins=50, alpha=0.7, color='orange', edgecolor='black')
        axes[1, 0].axvline(x=np.mean(errors), color='red', linestyle='--', linewidth=2,
                          label=f'平均误差: {np.mean(errors):.6f}μm')
        axes[1, 0].set_xlabel('绝对误差 (μm)')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].set_title('误差分布直方图')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 预测vs真实值散点图
        axes[1, 1].scatter(real_values, predictions, alpha=0.6, s=10, c=errors, cmap='viridis')
        
        # 添加理想预测线 (y=x)
        min_val = min(min(real_values), min(predictions))
        max_val = max(max(real_values), max(predictions))
        axes[1, 1].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='理想预测')
        
        axes[1, 1].set_xlabel('真实值 (μm)')
        axes[1, 1].set_ylabel('预测值 (μm)')
        axes[1, 1].set_title('预测值 vs 真实值散点图')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        # 添加颜色条
        cbar = plt.colorbar(axes[1, 1].collections[0], ax=axes[1, 1])
        cbar.set_label('预测误差 (μm)')
        
        plt.tight_layout()

        # 图表生成已禁用
        plt.close()
        print("📊 预测精度分析图表生成已禁用，避免生成额外文件")

async def main():
    """主函数"""
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    # 检查DLL文件
    if not os.path.exists(dll_path):
        print(f"❌ 找不到DLL文件: {dll_path}")
        return
    
    # 创建测试实例
    test = PredictionAccuracyTest(dll_path)
    
    # 运行测试
    success = await test.run_test(duration=20)  # 20秒测试
    
    if success:
        print("✅ 预测精度测试完成")
    else:
        print("❌ 预测精度测试失败")

if __name__ == "__main__":
    print("🧪 DSA实时回调预测系统 - 预测精度测试程序")
    print("验证修复后的预测误差是否正常（不再为0）")
    asyncio.run(main())
