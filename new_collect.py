import ctypes
from ctypes import *
import time
import os

class DSADataCollector:
    def __init__(self, dll_path):
        """初始化数据收集器"""
        self.sdk = ctypes.cdll.LoadLibrary(dll_path)
        
        # 定义回调函数类型
        self.CALLBACK = CFUNCTYPE(None, c_int, c_int, c_int, c_int, POINTER(c_float), c_int)
        self._callback_func = self.CALLBACK(self._data_callback)
        
        # 数据收集相关变量
        self.training_data = []  # 位移数据缓冲区
        self.start_time = None
        self.total_duration = 3  # 收集前3秒数据
        self.is_running = True

    def _data_callback(self, dataType, sRate, vRange, dRange, data_ptr, dataLen):
        """数据回调函数"""
        if not self.is_running:
            return

        array_type = c_float * dataLen
        data = cast(data_ptr, POINTER(array_type)).contents
        values = list(data)

        # 初始化起始时间
        current_time = time.time()
        if self.start_time is None:
            self.start_time = current_time
            print("开始收集数据...")

        # 使用原始采样率计算每个点的相对时间（毫秒）
        # sRate 为SDK实际采样率，如果为0，默认使用2000Hz
        effective_sRate = sRate if sRate > 0 else 2000
        for i, value in enumerate(values):
            timestamp_ms = (current_time - self.start_time) * 1000 + i * (1000.0 / effective_sRate)
            self.training_data.append({'timestamp_ms': timestamp_ms, 'value': value})

        # 停止条件
        if (current_time - self.start_time) >= self.total_duration:
            self.is_running = False
            print(f"\n已收集 {self.total_duration} 秒数据，停止收集")

    def _save_training_data(self):
        """保存训练数据到 x1.txt"""
        filename = "x1.txt"
        print(f"\n💾 保存训练数据到 {filename}...")

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("时间[ms]\t位移[μm]\n")  # 表头
            for point in self.training_data:
                f.write(f"{point['timestamp_ms']:.3f}\t{point['value']:.6f}\n")

        print(f"✅ 数据保存完成，共 {len(self.training_data)} 个数据点")

    def initialize_sdk(self):
        """初始化SDK"""
        print("初始化SDK...")
        result = self.sdk.initialize()
        if result != 0:
            print(f"初始化失败: {result}")
            return False

        # 设置回调
        self.sdk.setDataCallBack(self._callback_func)
        # 设置数据长度
        self.sdk.setDataLength(256)
        # 输出类型：位移
        self.sdk.setOutDataType(0x02)  # odtDisplacement
        return True

    def start_collection(self):
        """启动数据收集"""
        print("启动数据收集...")
        result = self.sdk.start()
        if result != 0:
            print(f"启动失败: {result}")
            return False
        return True

    def run(self):
        if not self.initialize_sdk():
            return
        if not self.start_collection():
            return

        # 等待数据收集完成
        while self.is_running:
            time.sleep(0.05)

        # 停止SDK
        self.sdk.stop()
        self.sdk.unInitialize()

        # 保存数据
        self._save_training_data()
        print("数据收集完成！")


def main():
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    if not os.path.exists(dll_path):
        print(f"错误：找不到 DLL 文件 {dll_path}")
        return

    collector = DSADataCollector(dll_path)
    collector.run()


if __name__ == "__main__":
    main()
