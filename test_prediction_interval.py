"""
测试预测间隔功能
"""

import asyncio
import os
import sys
import time

# 添加当前目录到路径
sys.path.insert(0, '.')

from real_time_callback_prediction import RealTimePredictionSystem

async def test_prediction_interval():
    """测试预测间隔功能"""
    print("🧪 测试预测间隔功能")
    print("="*60)
    
    # DLL 路径
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    # 检查DLL文件
    if not os.path.exists(dll_path):
        print(f"❌ 找不到DLL文件: {dll_path}")
        return False
    
    # 创建实时预测系统
    system = RealTimePredictionSystem(
        dll_path=dll_path,
        model_path="displacement_model.pth",
        scaler_path="displacement_scaler.pkl"
    )
    
    print("🚀 开始测试预测间隔功能...")
    print("📊 预期结果:")
    print("   - DSA采样率: 10,000 Hz")
    print("   - 预测间隔: 每10个数据点")
    print("   - 预测频率: 1,000 Hz")
    print("   - 1秒内预测次数: ~1,000次")
    print("-" * 60)
    
    # 记录开始时间
    start_time = time.time()
    
    # 启动1秒预测测试
    success = await system.start_real_time_prediction(duration_seconds=1)
    
    # 记录结束时间
    end_time = time.time()
    actual_duration = end_time - start_time
    
    if success:
        print("\n" + "="*60)
        print("📊 测试结果分析")
        print("="*60)
        
        # 分析预测数据
        if hasattr(system, 'real_time_data') and system.real_time_data:
            prediction_count = len(system.real_time_data)
            actual_frequency = prediction_count / actual_duration
            
            print(f"⏱️  实际运行时间: {actual_duration:.2f} 秒")
            print(f"📈 实际预测次数: {prediction_count}")
            print(f"🎯 实际预测频率: {actual_frequency:.1f} Hz")
            
            # 计算理论值
            theoretical_predictions = 1000  # 1000Hz * 1秒
            frequency_ratio = actual_frequency / 1000 * 100
            
            print(f"📊 理论预测次数: {theoretical_predictions}")
            print(f"📊 频率达成率: {frequency_ratio:.1f}%")
            
            # 判断测试结果
            if 800 <= actual_frequency <= 1200:  # 允许20%误差
                print("✅ 预测间隔功能正常工作")
                print("✅ 预测频率在预期范围内")
            else:
                print("⚠️ 预测频率偏离预期值")
                if actual_frequency > 1200:
                    print("   可能原因: 预测间隔设置未生效")
                else:
                    print("   可能原因: 系统性能限制或其他瓶颈")
            
            # 分析预测质量
            if prediction_count > 0:
                errors = [d['error'] for d in system.real_time_data]
                avg_error = sum(errors) / len(errors)
                max_error = max(errors)
                min_error = min(errors)
                
                print(f"\n📊 预测质量分析:")
                print(f"   平均误差: {avg_error:.6f} μm")
                print(f"   最大误差: {max_error:.6f} μm")
                print(f"   最小误差: {min_error:.6f} μm")
        else:
            print("❌ 未收集到预测数据")
            return False
        
        print("\n✅ 预测间隔测试完成")
        return True
    else:
        print("❌ 预测测试失败")
        return False

def main():
    """主函数"""
    print("🎯 DSA预测间隔功能测试")
    print("测试目标: 验证每隔10个数据点预测一次的功能")
    print("="*60)
    
    try:
        result = asyncio.run(test_prediction_interval())
        
        if result:
            print("\n🎉 所有测试通过！")
            print("📋 功能确认:")
            print("   ✅ 预测间隔控制正常")
            print("   ✅ 预测频率符合预期")
            print("   ✅ 系统性能良好")
        else:
            print("\n❌ 测试失败")
            print("请检查系统配置和硬件连接")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
