#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU 加速修改示例
展示如何修改现有代码以支持 GPU 加速
"""

import torch
import torch.nn as nn
import numpy as np
from sklearn.preprocessing import MinMaxScaler
import time


# ============================================================================
# 示例 1：修改训练代码
# ============================================================================

class DisplacementLSTM(nn.Module):
    """位移预测LSTM模型"""
    
    def __init__(self, input_size=1, hidden_size=96, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.linear = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.1)
    
    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        out = lstm_out[:, -1, :]
        out = self.dropout(out)
        prediction = self.linear(out)
        return prediction


def train_with_gpu_example():
    """
    示例：如何修改训练代码以支持 GPU
    
    修改前：
        model = DisplacementLSTM(...)
        for epoch in range(epochs):
            outputs = model(X)
            loss = criterion(outputs, y)
    
    修改后：
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model = DisplacementLSTM(...).to(device)
        X = X.to(device)
        y = y.to(device)
        for epoch in range(epochs):
            outputs = model(X)
            loss = criterion(outputs, y)
    """
    
    print("=" * 70)
    print("示例 1：修改训练代码")
    print("=" * 70)
    
    # 创建示例数据
    X = torch.randn(1000, 25, 1)
    y = torch.randn(1000)
    
    # ✨ 修改 1：检测设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"\n✓ 设备: {device}")
    
    # ✨ 修改 2：创建模型并移到 GPU
    model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
    model = model.to(device)
    print(f"✓ 模型已移到 {device}")
    
    # ✨ 修改 3：数据移到 GPU
    X = X.to(device)
    y = y.to(device)
    print(f"✓ 数据已移到 {device}")
    
    # 训练配置
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.008)
    
    # 性能测试
    print("\n📊 性能对比:")
    
    # CPU 训练
    model_cpu = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
    X_cpu = torch.randn(1000, 25, 1)
    y_cpu = torch.randn(1000)
    criterion_cpu = nn.MSELoss()
    optimizer_cpu = torch.optim.Adam(model_cpu.parameters(), lr=0.008)
    
    start = time.time()
    for epoch in range(10):
        optimizer_cpu.zero_grad()
        outputs = model_cpu(X_cpu).squeeze()
        loss = criterion_cpu(outputs, y_cpu)
        loss.backward()
        optimizer_cpu.step()
    cpu_time = time.time() - start
    
    # GPU 训练
    start = time.time()
    for epoch in range(10):
        optimizer.zero_grad()
        outputs = model(X).squeeze()
        loss = criterion(outputs, y)
        loss.backward()
        optimizer.step()
    torch.cuda.synchronize()
    gpu_time = time.time() - start
    
    print(f"  CPU 耗时: {cpu_time:.3f}s")
    print(f"  GPU 耗时: {gpu_time:.3f}s")
    print(f"  加速比: {cpu_time / gpu_time:.1f}x")
    
    # ✨ 修改 4：模型移回 CPU（保存时）
    model = model.cpu()
    print(f"\n✓ 模型已移回 CPU（用于保存）")


# ============================================================================
# 示例 2：修改推理代码
# ============================================================================

class GPUStreamProcessor:
    """
    示例：如何修改推理代码以支持 GPU
    
    修改前：
        def predict(self, value):
            input_seq = torch.FloatTensor(...)
            output = self.model(input_seq)
    
    修改后：
        def predict(self, value):
            input_seq = torch.FloatTensor(...)
            input_seq = input_seq.to(self.device)
            output = self.model(input_seq)
    """
    
    def __init__(self, model, scaler, window_size=25):
        # ✨ 修改 1：检测设备
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"✓ StreamProcessor 使用设备: {self.device}")
        
        # ✨ 修改 2：模型移到 GPU
        self.model = model.to(self.device)
        self.model.eval()
        
        self.scaler = scaler
        self.window_size = window_size
        self.data_buffer = []
    
    def predict(self, value):
        """单点预测"""
        # 标准化
        normalized = self.scaler.transform([[value]])[0, 0]
        self.data_buffer.append(normalized)
        
        if len(self.data_buffer) > self.window_size:
            self.data_buffer.pop(0)
        
        if len(self.data_buffer) < self.window_size:
            return None
        
        # 准备输入
        input_seq = torch.FloatTensor(self.data_buffer).unsqueeze(0).unsqueeze(-1)
        
        # ✨ 修改 3：数据移到 GPU
        input_seq = input_seq.to(self.device)
        
        # 推理
        with torch.no_grad():
            normalized_pred = self.model(input_seq).item()
        
        # 反标准化
        prediction = self.scaler.inverse_transform([[normalized_pred]])[0, 0]
        return prediction


def inference_with_gpu_example():
    """示例：GPU 加速推理"""
    
    print("\n" + "=" * 70)
    print("示例 2：修改推理代码")
    print("=" * 70)
    
    # 创建模型和标准化器
    model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
    scaler = MinMaxScaler(feature_range=(-1, 1))
    scaler.fit(np.random.randn(1000, 1))
    
    # 创建处理器
    processor = GPUStreamProcessor(model, scaler, window_size=25)
    
    # 性能测试
    print("\n📊 推理性能对比:")
    
    # CPU 推理
    model_cpu = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
    model_cpu.eval()
    
    test_data = np.random.randn(1000)
    
    start = time.time()
    for value in test_data:
        input_seq = torch.FloatTensor([value]).unsqueeze(0).unsqueeze(-1)
        with torch.no_grad():
            _ = model_cpu(input_seq)
    cpu_time = time.time() - start
    
    # GPU 推理
    start = time.time()
    for value in test_data:
        input_seq = torch.FloatTensor([value]).unsqueeze(0).unsqueeze(-1)
        input_seq = input_seq.to(processor.device)
        with torch.no_grad():
            _ = processor.model(input_seq)
    torch.cuda.synchronize()
    gpu_time = time.time() - start
    
    print(f"  CPU 耗时: {cpu_time:.3f}s")
    print(f"  GPU 耗时: {gpu_time:.3f}s")
    print(f"  加速比: {cpu_time / gpu_time:.1f}x")
    print(f"  单点推理时间:")
    print(f"    CPU: {cpu_time / len(test_data) * 1000:.3f}ms")
    print(f"    GPU: {gpu_time / len(test_data) * 1000:.3f}ms")


# ============================================================================
# 示例 3：修改主程序
# ============================================================================

def main_program_example():
    """示例：如何修改主程序"""
    
    print("\n" + "=" * 70)
    print("示例 3：修改主程序")
    print("=" * 70)
    
    # ✨ 修改 1：显示 GPU 信息
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"\n🖥️  使用设备: {device}")
    
    if torch.cuda.is_available():
        print(f"   GPU: {torch.cuda.get_device_name(0)}")
        props = torch.cuda.get_device_properties(0)
        print(f"   显存: {props.total_memory / 1e9:.2f} GB")
        print(f"   计算能力: {props.major}.{props.minor}")
    
    # ✨ 修改 2：在关键位置添加 GPU 信息
    print("\n📊 系统信息:")
    print(f"   PyTorch 版本: {torch.__version__}")
    print(f"   CUDA 版本: {torch.version.cuda}")
    
    # ✨ 修改 3：在训练前显示预期性能
    print("\n⚡ 预期性能提升:")
    if torch.cuda.is_available():
        print("   ✓ 训练速度: 7-8x 加速")
        print("   ✓ 推理速度: 6-8x 加速")
        print("   ✓ 采样率: 3-5x 提升（60 Hz → 200-300 Hz）")
    else:
        print("   ⚠️  GPU 不可用，使用 CPU 计算")


# ============================================================================
# 修改清单
# ============================================================================

def print_modification_checklist():
    """打印修改清单"""
    
    print("\n" + "=" * 70)
    print("GPU 加速修改清单")
    print("=" * 70)
    
    checklist = [
        ("real_time_train.py", [
            "在 train_displacement_model 函数开始添加: device = torch.device(...)",
            "在创建模型后添加: model = model.to(device)",
            "在创建张量后添加: X = X.to(device), y = y.to(device)",
            "在保存前添加: model = model.cpu()",
        ]),
        ("real_time_stream_processor.py", [
            "在 __init__ 中添加: self.device = torch.device(...)",
            "在 __init__ 中添加: self.model = self.model.to(self.device)",
            "在 _make_prediction 中添加: input_seq = input_seq.to(self.device)",
        ]),
        ("integrated_real_time_demo.py", [
            "在 main 函数开始添加 GPU 信息输出",
            "验证所有模型都使用了 GPU",
        ]),
    ]
    
    for file, modifications in checklist:
        print(f"\n📄 {file}:")
        for i, mod in enumerate(modifications, 1):
            print(f"   {i}. {mod}")


# ============================================================================
# 主程序
# ============================================================================

if __name__ == "__main__":
    print("\n" + "=" * 70)
    print("GPU 加速修改示例")
    print("=" * 70)
    
    # 运行示例
    train_with_gpu_example()
    inference_with_gpu_example()
    main_program_example()
    print_modification_checklist()
    
    print("\n" + "=" * 70)
    print("✅ 示例完成！")
    print("=" * 70)
    print("\n📝 下一步:")
    print("   1. 按照修改清单修改你的代码")
    print("   2. 运行 check_gpu.py 验证 GPU 环境")
    print("   3. 运行 integrated_real_time_demo.py 测试性能")
    print("   4. 运行 verify_optimization.py 验证性能提升")
    print()

