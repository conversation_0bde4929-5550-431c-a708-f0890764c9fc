# DSA实时回调预测系统

## 概述

本系统实现了真正的实时预测，DSA每次回调函数被调用时都会立即写入最新的位移和速度数据给预测模型，确保是真正的实时预测而不是基于预先准备的测试集。

## 系统特点

### 🎯 真正的实时性
- **每次DSA回调都触发预测**：不再使用静态测试文件
- **零延迟数据传输**：DSA回调直接调用预测器
- **实时数据流**：位移和速度数据分别处理

### 🔧 技术架构
- **回调驱动**：DSA SDK回调函数直接触发预测
- **分离队列**：位移和速度数据使用独立队列
- **即时预测**：每个新数据点立即进行LSTM+AKF预测

### 📊 多维度预测
- **位移预测**：基于历史位移数据的LSTM预测
- **速度预测**：基于历史速度数据的预测（可扩展）
- **组合分析**：位移和速度数据的联合分析

## 文件结构

### 核心文件
- `real_time_callback_prediction.py` - 主要的实时回调预测系统
- `test_real_time_callback.py` - 简单的测试程序
- `advanced_real_time_prediction.py` - 高级双重预测系统
- `data_collector_3s.py` - 修改后的DSA数据收集器（支持实时预测回调）

### 支持文件
- `real_time_stream_processor.py` - 实时数据流处理器
- `real_time_data_bridge.py` - 数据桥接器（已更新）
- `real_time_train.py` - 模型训练模块

## 使用方法

### 1. 快速测试

运行简单的测试程序验证系统工作：

```bash
python test_real_time_callback.py
```

**测试内容：**
- 快速训练一个小模型
- 收集25个初始化数据点
- 运行15秒实时预测测试
- 显示预测统计结果

### 2. 完整实时预测

运行完整的实时预测系统：

```bash
python real_time_callback_prediction.py
```

**系统流程：**
1. 检查并加载预训练模型（如果存在）
2. 或者基于v1.txt训练新模型
3. 收集初始化数据（25个点）
4. 启动实时预测（默认60秒）
5. 每次DSA回调都触发预测
6. 生成详细的统计报告和可视化

### 3. 高级双重预测

运行支持位移和速度的高级预测：

```bash
python advanced_real_time_prediction.py
```

**高级功能：**
- 同时处理位移和速度数据
- 独立的预测器和队列
- 数据同步和组合分析
- 更详细的统计报告

## 系统配置

### DSA设置
```python
# 在data_collector_3s.py中的配置
self.sdk.setDataLength(256)  # 每次回调256点
self.sdk.setOutputFilter(self.OutputFilter['of10k'])  # 10000Hz采样
self.sdk.setOutDataType(self.OutDataType['odtDisplacement'])  # 位移数据
```

### 预测器设置
```python
# 实时处理器配置
processor = RealTimeStreamProcessor(
    model=model,
    scaler=scaler,
    window_size=25,           # LSTM窗口大小
    buffer_size=10000,        # 数据缓冲区大小
    enable_akf=True,          # 启用自适应卡尔曼滤波
    enable_bias_correction=True  # 启用偏移校正
)
```

## 核心机制

### 1. 回调驱动预测

```python
def _data_callback(self, dataType, sRate, vRange, dRange, data_ptr, dataLen):
    """DSA SDK回调函数"""
    # 处理每个数据点
    for i, value in enumerate(values):
        # 保存到相应队列
        if dataType == 2:  # 位移数据
            self.displacement_queue.put_nowait(data_point)
            # 立即进行预测
            if self.real_time_predictor is not None:
                self._perform_real_time_prediction(data_point)
```

### 2. 实时预测执行

```python
def _perform_real_time_prediction(self, data_point):
    """执行实时预测"""
    if self.real_time_predictor is not None:
        # 进行预测
        prediction = self.real_time_predictor.process_single_point(data_point['value'])
        
        # 调用预测回调
        if self.prediction_callback is not None and prediction is not None:
            self.prediction_callback(
                real_value=data_point['value'],
                prediction=prediction,
                timestamp=data_point['timestamp'],
                data_type=data_point['dataType']
            )
```

### 3. 预测回调处理

```python
def prediction_callback(self, real_value, prediction, timestamp, data_type):
    """预测结果回调函数"""
    # 计算误差
    error = abs(real_value - prediction)
    
    # 存储结果
    self.real_time_data.append({
        'time': relative_time,
        'real_value': real_value,
        'prediction': prediction,
        'error': error
    })
    
    # 实时显示
    print(f"预测: 真实={real_value:.6f}μm, 预测={prediction:.6f}μm, 误差={error:.6f}μm")
```

## 性能指标

### 预期性能
- **预测频率**：100-1000 Hz（取决于DSA采样率和数据处理策略）
- **预测延迟**：< 5ms（从DSA回调到预测完成）
- **内存使用**：< 100MB（包含模型和数据缓冲区）

### 实际测试结果
运行测试程序后会显示：
- 实际预测频率
- 平均预测误差
- 处理时间统计
- 数据丢失率（如果有）

## 输出文件

### 数据文件
- `real_time_predictions_[timestamp].csv` - 实时预测结果
- `displacement_predictions_[timestamp].csv` - 位移预测数据
- `velocity_predictions_[timestamp].csv` - 速度预测数据

### 可视化文件
- `real_time_prediction_analysis_[timestamp].png` - 预测结果分析图
- `advanced_prediction_analysis_[timestamp].png` - 高级预测分析图

## 故障排除

### 1. DLL文件问题
```
❌ 找不到DLL文件: SDK发布20200723\x64\DSANetSDK.dll
```
**解决方案：** 确保DSA SDK DLL文件存在且路径正确

### 2. 训练数据缺失
```
❌ 未找到训练数据文件 v1.txt
```
**解决方案：** 先运行数据收集程序生成训练数据

### 3. 初始化数据不足
```
❌ 初始化数据不足: 15/25
```
**解决方案：** 检查DSA设备连接，确保数据流正常

### 4. 预测频率过低
**可能原因：**
- DSA采样率设置过低
- 网络延迟
- 模型计算时间过长

**解决方案：**
- 调整DSA采样率设置
- 使用更小的模型
- 优化数据处理流程

## 扩展功能

### 1. 多设备支持
可以扩展支持多个DSA设备同时进行实时预测

### 2. 自定义预测模型
可以替换LSTM模型为其他类型的预测模型

### 3. 实时可视化
可以添加实时图表显示预测结果

### 4. 数据库存储
可以将实时预测结果存储到数据库

## 注意事项

1. **确保DSA设备正常连接**
2. **预先准备训练数据（v1.txt）**
3. **根据实际需求调整预测持续时间**
4. **监控系统资源使用情况**
5. **定期检查预测精度并重新训练模型**

## 技术支持

如果遇到问题，请检查：
1. DSA SDK版本兼容性
2. Python依赖包版本
3. 系统资源使用情况
4. 网络连接状态

---

**注意：** 本系统实现了真正的实时预测，每次DSA回调都会触发预测，确保了最高的实时性和准确性。
