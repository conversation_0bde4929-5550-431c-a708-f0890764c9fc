import ctypes
from ctypes import *
import time
import os
import socket

class DSANetSDK:
    def __init__(self, dll_path):
        """初始化 DSANet SDK"""
        self.sdk = ctypes.cdll.LoadLibrary(dll_path)

        # 定义回调函数类型
        self.CALLBACK = CFUNCTYPE(None, c_int, c_int, c_int, c_int, POINTER(c_float), c_int)
        self._callback_func = self.CALLBACK(self._data_callback)

        # 数据收集相关变量
        self.data_buffer = []  # 数据缓冲区
        self.start_time = None  # 开始时间
        self.last_output_time = 0  # 上次输出时间
        self.output_count = 0  # 输出计数
        self.target_points_per_second = 2000  # 每秒目标数据点数
        self.total_duration = 3  # 总运行时间（秒）
        self.is_running = True  # 运行状态
        
        # 定义枚举值（对应 C++ 中的枚举）
        self.OutputFilter = {
            'of2k': 0x01,
            'of10k': 0x02,
            'of20k': 0x03,
            'of40k': 0x04,
            'of80k': 0x05,
            'of160k': 0x06,
            'of360k': 0x07,
            'of1M': 0x08,
            'of3M': 0x09
        }
        
        self.OutDataType = {
            'odtVelocity': 0x01,
            'odtDisplacement': 0x02,
            'odtAll': 0x03,
            'odtNoOutput': 0x04
        }
        
        self.VelocityRange = {
            'vr1': 0x01, 'vr2': 0x02, 'vr3': 0x03, 'vr4': 0x04,
            'vr5': 0x05, 'vr6': 0x06, 'vr7': 0x07, 'vr8': 0x08,
            'vr9': 0x09, 'vr10': 0x0A, 'vr11': 0x0B, 'vr12': 0x0C,
            'vr13': 0x0D, 'vr14': 0x0E, 'vr15': 0x0F, 'vr16': 0x10
        }
        
        self.DisplacementRange = {
            'dr1': 0x01, 'dr2': 0x02, 'dr3': 0x03, 'dr4': 0x04,
            'dr5': 0x05, 'dr6': 0x06, 'dr7': 0x07, 'dr8': 0x08,
            'dr9': 0x09, 'dr10': 0x0A, 'dr11': 0x0B, 'dr12': 0x0C,
            'dr13': 0x0D, 'dr14': 0x0E, 'dr15': 0x0F, 'dr16': 0x10,
            'dr17': 0x11, 'dr18': 0x12, 'dr19': 0x13, 'dr20': 0x14, 'dr21': 0x15
        }
        
        self.DeviceType = {
            'DT3M': 0x01,
            'DT25M': 0x02
        }
        
        self.LaserWaveLength = {
            'LW_632_8': 0x01,  # 氦氖632.8
            'LW_1550': 0x02    # 光纤1550
        }

    def _data_callback(self, dataType, sRate, vRange, dRange, data_ptr, dataLen):
        """数据回调函数 - 收集数据并按时间输出到文件"""
        if not self.is_running:
            return

        # 将指针转换为数组
        array_type = c_float * dataLen
        data = cast(data_ptr, POINTER(array_type)).contents
        values = list(data)

        # 记录开始时间
        current_time = time.time()
        if self.start_time is None:
            self.start_time = current_time
            self.last_output_time = current_time
            print("开始数据收集...")

        # 检查是否超过总运行时间
        elapsed_time = current_time - self.start_time
        if elapsed_time >= self.total_duration:
            self.is_running = False
            print(f"\n已运行{self.total_duration}秒，停止数据收集")
            return

        # 将数据添加到缓冲区
        for value in values:
            self.data_buffer.append({
                'timestamp': current_time,
                'value': value,
                'dataType': dataType
            })

        # 检查是否需要输出数据（每秒输出一次）
        time_since_last_output = current_time - self.last_output_time
        if time_since_last_output >= 1.0:  # 每秒输出一次
            self._output_data_to_file()
            self.last_output_time = current_time

    def _output_data_to_file(self):
        """输出数据到文件"""
        if len(self.data_buffer) == 0:
            return

        self.output_count += 1

        # 确定数据类型和单位
        sample_data_type = self.data_buffer[0]['dataType']
        if sample_data_type == self.OutDataType['odtVelocity']:
            data_type_name = "velocity"
            unit = "mm/s"
        elif sample_data_type == self.OutDataType['odtDisplacement']:
            data_type_name = "displacement"
            unit = "um"
        else:
            data_type_name = "unknown"
            unit = ""

        # 选择要输出的数据点（目标：2000个点）
        total_points = len(self.data_buffer)
        if total_points >= self.target_points_per_second:
            # 如果数据点足够，均匀选择2000个点
            step = total_points / self.target_points_per_second
            selected_indices = [int(i * step) for i in range(self.target_points_per_second)]
            selected_data = [self.data_buffer[i] for i in selected_indices]
        else:
            # 如果数据点不够，输出所有数据
            selected_data = self.data_buffer.copy()

        # 生成文件名
        filename = f"data_second_{self.output_count}_{data_type_name}.txt"

        # 写入文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# 第{self.output_count}秒的{data_type_name}数据\n")
            f.write(f"# 数据类型: {data_type_name}\n")
            f.write(f"# 单位: {unit}\n")
            f.write(f"# 数据点数: {len(selected_data)}\n")
            f.write(f"# 时间戳\t数值({unit})\n")

            for data_point in selected_data:
                f.write(f"{data_point['timestamp']:.6f}\t{data_point['value']:.6f}\n")

        print(f"第{self.output_count}秒: 已输出{len(selected_data)}个数据点到文件 {filename}")

        # 清空缓冲区
        self.data_buffer.clear()

    def get_error_description(self, error_code):
        """获取错误码的描述"""
        error_descriptions = {
            0: "成功",
            10013: "网络权限被拒绝 - 可能是端口被占用或防火墙阻止",
            10048: "地址已在使用中 - 端口被占用",
            10049: "无法分配请求的地址",
            10054: "连接被对方重置",
            10060: "连接超时",
            10061: "连接被拒绝"
        }
        return error_descriptions.get(error_code, f"未知错误码: {error_code}")

    def check_port(self, port):
        """检查端口是否被占用"""
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0

    def initialize(self):
        """初始化 SDK"""
        print("正在初始化...")
        result = self.sdk.initialize()
        print(f"初始化结果: {result}")
        if result != 0:
            print(f"initialize error {result}")
        return result

    def set_bind_info(self, ip_address, port):
        """设置绑定信息"""
        result = self.sdk.setBindInfo(ip_address.encode('utf-8'), port)
        if result != 0:
            print(f"setBindInfo error {result}")
        return result

    def set_boardcast_info(self, ip_address, port):
        """设置广播信息"""
        result = self.sdk.setBoardcastInfo(ip_address.encode('utf-8'), port)
        if result != 0:
            print(f"setBoardcastInfo error {result}")
        return result

    def set_device_type(self, device_type):
        """设置设备类型"""
        result = self.sdk.setDeviceType(device_type)
        if result != 0:
            print(f"setDeviceType error {result}")
        return result

    def set_device_id(self, device_id):
        """设置设备ID"""
        result = self.sdk.setDeviceId(device_id)
        if result != 0:
            print(f"setDeviceId error {result}")
        return result

    def set_laser_wave_length(self, wave_length):
        """设置激光器波长"""
        result = self.sdk.setLaserWaveLength(wave_length)
        if result != 0:
            print(f"setLaserWaveLength error {result}")
        return result

    def set_data_callback(self):
        """设置数据回调"""
        result = self.sdk.setDataCallBack(self._callback_func)
        if result != 0:
            print(f"setDataCallBack error {result}")
        return result

    def set_data_length(self, length):
        """设置每次回调的数据长度"""
        result = self.sdk.setDataLength(length)
        if result != 0:
            print(f"setDataLength error {result}")
        return result

    def set_output_filter(self, filter_type):
        """设置输出滤波"""
        result = self.sdk.setOutputFilter(filter_type)
        if result != 0:
            print(f"setOutputFilter error {result}")
        return result

    def set_velocity_range(self, velocity_range):
        """设置速度档位"""
        result = self.sdk.setVelocityRange(velocity_range)
        if result != 0:
            print(f"setVelocityRange error {result}")
        return result

    def set_displacement_range(self, displacement_range):
        """设置位移档位"""
        result = self.sdk.setDisplacementRange(displacement_range)
        if result != 0:
            print(f"setDisplacementRange error {result}")
        return result

    def set_out_data_type(self, data_type):
        """设置返回数据类型"""
        result = self.sdk.setOutDataType(data_type)
        if result != 0:
            print(f"setOutDataType error {result}")
        return result

    def start(self):
        """开始读取数据"""
        print("正在启动测量...")
        result = self.sdk.start()
        print(f"启动结果: {result}")
        if result != 0:
            print(f"start error {result}")
        return result

    def stop(self):
        """停止读取数据"""
        result = self.sdk.stop()
        if result != 0:
            print(f"stop error {result}")
        return result

    def uninitialize(self):
        """反初始化"""
        result = self.sdk.unInitialize()
        if result != 0:
            print(f"unInitialize error {result}")
        return result


def main():
    """主函数"""
    print("DSANet SDK 测试程序开始")
    print("========================")
    
    # 初始化 SDK
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"  # 修正为实际路径
    dsa = DSANetSDK(dll_path)
    
    # 初始化
    ret = dsa.initialize()
    
    # 必须开始前调用 需要再次设置 ⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇
    # 0.0.0.0 是绑定本地所有  但是多网卡的时候由于静态路由表的问题 可能会接收不到数据
    # 如果绑定指定网卡 设置那个指定网卡的ip  比如说************
    dsa.set_bind_info("0.0.0.0", 62618)
    
    # 广播地址 默认***************就行
    dsa.set_boardcast_info("***************", 63082)
    
    # 默认就行
    dsa.set_device_type(dsa.DeviceType['DT3M'])
    
    # 默认就行
    dsa.set_device_id(0)
    
    # 设置激光器的波长  氦氖632.8  光纤1550
    dsa.set_laser_wave_length(dsa.LaserWaveLength['LW_632_8'])
    
    # 设置数据回调
    dsa.set_data_callback()
    
    # 设置每次回调的数据长度 - 调整为更小的值以获得更频繁的数据更新
    dsa.set_data_length(512)  # 减小数据包大小，提高数据收集频率
    # 必须开始前调用 需要再次设置 ⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆
    
    # 随时可以调用
    # 设置输出滤波 - 修改为2k以获得2000Hz采样率
    dsa.set_output_filter(dsa.OutputFilter['of2k'])

    # 设置速度档位
    dsa.set_velocity_range(dsa.VelocityRange['vr1'])

    # 设置位移档位
    dsa.set_displacement_range(dsa.DisplacementRange['dr1'])

    # 设置返回数据类型
    dsa.set_out_data_type(dsa.OutDataType['odtNoOutput'])
    time.sleep(0.05)  # 对应 C++ 中的 Sleep(50)

    # 设置速度输出 - 修改为速度测量
    dsa.set_out_data_type(dsa.OutDataType['odtVelocity'])
    # 随时可以调用
    
    # 开始读取数据
    ret = dsa.start()

    print("程序开始运行，将收集3秒数据...")
    print("每秒输出2000个数据点到txt文件")
    print("=" * 50)

    try:
        # 运行3秒，每0.1秒检查一次状态
        start_time = time.time()
        while time.time() - start_time < 3.1 and dsa.is_running:
            time.sleep(0.1)

        # 如果还有剩余数据，输出最后一批
        if len(dsa.data_buffer) > 0:
            dsa._output_data_to_file()

    except KeyboardInterrupt:
        print("\n用户中断程序...")

    # 停止读取数据
    dsa.stop()

    # 程序退出调用  调用一次即可
    dsa.uninitialize()

    print("=" * 50)
    print(f"程序结束，共输出了 {dsa.output_count} 个数据文件")
    print("文件命名格式: data_second_X_[displacement/velocity].txt")


if __name__ == "__main__":
    main()
