"""
实时预测系统训练模块
包含LSTM模型定义和训练功能
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
import warnings

warnings.filterwarnings("ignore")


class DisplacementLSTM(nn.Module):
    """位移预测LSTM模型"""
    
    def __init__(self, input_size=1, hidden_size=64, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.linear = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        out = lstm_out[:, -1, :]
        out = self.dropout(out)
        prediction = self.linear(out)
        return prediction


def create_sequences(data, window_size):
    """创建训练序列"""
    sequences = []
    targets = []

    for i in range(len(data) - window_size):
        seq = data[i:i+window_size]
        target = data[i+window_size]
        sequences.append(seq)
        targets.append(target)

    return np.array(sequences), np.array(targets)


def train_displacement_model(data_file='v1.txt', window_size=25, hidden_size=96,
                           learning_rate=0.008, epochs=100, train_points=5000):
    """
    训练位移预测LSTM模型
    
    Args:
        data_file: 训练数据文件路径
        window_size: LSTM窗口大小
        hidden_size: LSTM隐藏层大小
        learning_rate: 学习率
        epochs: 训练轮数
        train_points: 使用的训练数据点数（默认5000）
    
    Returns:
        tuple: (model, scaler) 训练好的模型和标准化器
    """
    print("正在训练LSTM模型...")

    # 1. 加载训练数据
    try:
        data = pd.read_csv(data_file, sep='\t', encoding='utf-8')
        displacement_data = data.iloc[:, 1].values  # 位移列
        print(f"加载{data_file}数据成功，总数据点数: {len(displacement_data)}")

        # 使用指定数量的点作为训练数据
        train_data = displacement_data[:train_points]
        print(f"使用前{train_points}个点进行模型训练")

    except FileNotFoundError:
        print(f"错误: 未找到{data_file}文件")
        return None, None
    except Exception as e:
        print(f"加载数据时发生错误: {e}")
        return None, None

    # 2. 数据预处理（只用训练数据拟合scaler）
    scaler = MinMaxScaler(feature_range=(-1, 1))
    train_normalized = scaler.fit_transform(train_data.reshape(-1, 1))
    train_normalized = torch.FloatTensor(train_normalized.flatten())
    
    # 3. 创建训练序列（使用训练数据）
    X, y = create_sequences(train_normalized.numpy(), window_size)

    X = torch.FloatTensor(X).unsqueeze(-1)
    y = torch.FloatTensor(y)

    print(f"训练序列数量: {len(X)}, 窗口大小: {window_size}")
    
    # 4. 训练模型
    model = DisplacementLSTM(input_size=1, hidden_size=hidden_size, output_size=1)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)

    print(f"开始训练，共{epochs}个epoch...")
    for epoch in range(epochs):
        optimizer.zero_grad()
        outputs = model(X).squeeze()
        loss = criterion(outputs, y)
        loss.backward()
        optimizer.step()

        if epoch % 25 == 0:
            print(f'Epoch {epoch:3d}: Loss = {loss.item():.6f}')

    model.eval()
    print(f"模型训练完成！最终损失: {loss.item():.6f}")

    return model, scaler


def save_model(model, scaler, model_path='displacement_model.pth', scaler_path='displacement_scaler.pkl'):
    """
    保存训练好的模型和标准化器
    
    Args:
        model: 训练好的LSTM模型
        scaler: 标准化器
        model_path: 模型保存路径
        scaler_path: 标准化器保存路径
    """
    try:
        # 保存模型
        torch.save(model.state_dict(), model_path)
        print(f"模型已保存到: {model_path}")
        
        # 保存标准化器
        import pickle
        with open(scaler_path, 'wb') as f:
            pickle.dump(scaler, f)
        print(f"标准化器已保存到: {scaler_path}")
        
        return True
    except Exception as e:
        print(f"保存模型时发生错误: {e}")
        return False


def load_model(model_path='displacement_model.pth', scaler_path='displacement_scaler.pkl', 
               hidden_size=96):
    """
    加载训练好的模型和标准化器
    
    Args:
        model_path: 模型文件路径
        scaler_path: 标准化器文件路径
        hidden_size: LSTM隐藏层大小
    
    Returns:
        tuple: (model, scaler) 加载的模型和标准化器
    """
    try:
        # 加载模型
        model = DisplacementLSTM(input_size=1, hidden_size=hidden_size, output_size=1)
        model.load_state_dict(torch.load(model_path))
        model.eval()
        print(f"模型已从 {model_path} 加载")
        
        # 加载标准化器
        import pickle
        with open(scaler_path, 'rb') as f:
            scaler = pickle.load(f)
        print(f"标准化器已从 {scaler_path} 加载")
        
        return model, scaler
    except Exception as e:
        print(f"加载模型时发生错误: {e}")
        return None, None


def get_model_info(model, scaler):
    """
    获取模型信息
    
    Args:
        model: LSTM模型
        scaler: 标准化器
    
    Returns:
        dict: 模型信息字典
    """
    if model is None or scaler is None:
        return {}
    
    # 计算模型参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    return {
        'model_type': 'DisplacementLSTM',
        'hidden_size': model.hidden_size,
        'total_parameters': total_params,
        'trainable_parameters': trainable_params,
        'scaler_feature_range': scaler.feature_range,
        'scaler_data_min': scaler.data_min_[0] if hasattr(scaler, 'data_min_') else None,
        'scaler_data_max': scaler.data_max_[0] if hasattr(scaler, 'data_max_') else None
    }


if __name__ == "__main__":
    """训练模块测试"""
    print("LSTM模型训练模块测试")
    print("="*50)
    
    # 训练模型
    model, scaler = train_displacement_model()
    
    if model is not None and scaler is not None:
        print("\n模型训练成功！")
        
        # 显示模型信息
        info = get_model_info(model, scaler)
        print(f"模型信息:")
        for key, value in info.items():
            print(f"  {key}: {value}")
        
        # 保存模型
        if save_model(model, scaler):
            print("\n模型保存成功！")
            
            # 测试加载
            loaded_model, loaded_scaler = load_model()
            if loaded_model is not None:
                print("模型加载测试成功！")
            else:
                print("模型加载测试失败！")
        else:
            print("\n模型保存失败！")
    else:
        print("\n模型训练失败！")
