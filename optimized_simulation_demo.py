"""
优化后实时预测仿真演示
使用模拟数据测试优化后的预测系统
"""

import time
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
import warnings
from collections import deque

# 导入优化模块
from data_quality_optimizer import DataQualityOptimizer, optimize_training_data
from model_architecture_optimizer import ModelArchitectureOptimizer, EnhancedLSTM
from training_strategy_optimizer import TrainingStrategyOptimizer, create_data_loaders
from akf_parameter_optimizer import OptimizedAdaptiveKalmanFilter, AKFParameterOptimizer

warnings.filterwarnings("ignore")

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    print("中文字体设置成功")
except:
    print("使用默认字体")


class OptimizedSimulationPredictor:
    """优化后的仿真预测器"""
    
    def __init__(self, window_size=25):
        self.window_size = window_size
        self.model = None
        self.scaler = None
        self.akf = None
        self.data_buffer = deque(maxlen=window_size)
        self.prediction_history = []
        self.actual_history = []
        self.lstm_history = []
        self.akf_history = []
        
    def train_optimized_model(self, data_file='v1.txt'):
        """训练优化后的模型"""
        print("🚀 开始训练优化后的模型...")
        
        # 1. 数据质量优化
        print("步骤1: 数据质量优化")
        success = optimize_training_data(data_file, 'v1_optimized.txt')
        if success:
            data_file = 'v1_optimized.txt'
            print("✅ 数据质量优化完成")
        
        # 2. 加载和预处理数据
        print("步骤2: 加载数据")
        data = pd.read_csv(data_file, sep='\t', encoding='utf-8')
        displacement_data = data.iloc[:, 1].values
        
        train_points = min(1000, len(displacement_data) - self.window_size)
        train_data = displacement_data[:train_points + self.window_size]
        
        # 创建序列
        X, y = self._create_sequences(train_data)
        
        # 数据标准化
        self.scaler = MinMaxScaler(feature_range=(-1, 1))
        flat_data = train_data.reshape(-1, 1)
        self.scaler.fit(flat_data)
        
        # 标准化序列数据
        X_normalized = []
        for seq in X:
            norm_seq = self.scaler.transform(seq.reshape(-1, 1)).flatten()
            X_normalized.append(norm_seq)
        
        y_normalized = self.scaler.transform(y.reshape(-1, 1)).flatten()
        
        # 转换为张量
        X_tensor = torch.FloatTensor(X_normalized).unsqueeze(-1)
        y_tensor = torch.FloatTensor(y_normalized)
        
        # 3. 模型架构优化
        print("步骤3: 模型架构优化")
        arch_optimizer = ModelArchitectureOptimizer()
        
        # 比较架构并选择最佳
        arch_results = arch_optimizer.compare_model_architectures(
            X_tensor[:100], y_tensor[:100], 
            architectures=['basic_lstm', 'enhanced_lstm']
        )
        
        valid_archs = {k: v for k, v in arch_results.items() if 'loss' in v}
        if valid_archs:
            best_arch = min(valid_archs.keys(), key=lambda k: valid_archs[k]['loss'])
            print(f"选择最佳架构: {best_arch}")
            self.model = arch_optimizer.create_model(best_arch, input_size=1, output_size=1)
        else:
            self.model = EnhancedLSTM(input_size=1, hidden_size=128, num_layers=2)
        
        # 4. 训练策略优化
        print("步骤4: 高级训练策略")
        strategy_optimizer = TrainingStrategyOptimizer()
        trainer = strategy_optimizer.create_trainer(self.model, strategy_name='advanced')
        
        # 创建数据加载器
        train_loader, val_loader = create_data_loaders(
            X_tensor, y_tensor, batch_size=32, val_split=0.2
        )
        
        # 训练模型
        history = trainer.train(train_loader, val_loader, epochs=100, verbose=False)
        print(f"训练完成，最佳验证损失: {history['best_val_loss']:.6f}")
        
        # 5. AKF参数优化
        print("步骤5: AKF参数优化")
        
        # 生成一些预测用于参数优化
        test_predictions = []
        test_actuals = []
        
        self.model.eval()
        with torch.no_grad():
            for i in range(min(200, len(X_tensor))):
                pred = self.model(X_tensor[i:i+1]).item()
                pred_denorm = self.scaler.inverse_transform([[pred]])[0, 0]
                actual_denorm = self.scaler.inverse_transform([[y_tensor[i].item()]])[0, 0]
                
                test_predictions.append(pred_denorm)
                test_actuals.append(actual_denorm)
        
        # 优化AKF参数
        akf_optimizer = AKFParameterOptimizer()
        optimization_result = akf_optimizer.optimize_parameters(
            test_predictions, test_actuals, test_actuals,
            method='differential_evolution', max_evaluations=30
        )
        
        # 创建优化后的AKF
        best_params = optimization_result['best_params']
        self.akf = OptimizedAdaptiveKalmanFilter(
            process_variance=best_params['process_variance'],
            measurement_variance=best_params['measurement_variance'],
            adaptation_rate=best_params['adaptation_rate'],
            adaptive_strategy='hybrid'
        )
        
        print("✅ 优化后模型训练完成")
        return True
    
    def _create_sequences(self, data):
        """创建训练序列"""
        sequences = []
        targets = []
        
        for i in range(len(data) - self.window_size):
            seq = data[i:i+self.window_size]
            target = data[i+self.window_size]
            sequences.append(seq)
            targets.append(target)
        
        return np.array(sequences), np.array(targets)
    
    def predict_single(self, new_value, actual_value=None):
        """单点预测"""
        # 添加到缓冲区
        self.data_buffer.append(new_value)
        
        if len(self.data_buffer) < self.window_size:
            return new_value, new_value  # 缓冲区未满，返回原值
        
        # LSTM预测
        input_seq = np.array(list(self.data_buffer))
        normalized_seq = self.scaler.transform(input_seq.reshape(-1, 1)).flatten()
        input_tensor = torch.FloatTensor(normalized_seq).unsqueeze(0).unsqueeze(-1)
        
        self.model.eval()
        with torch.no_grad():
            normalized_pred = self.model(input_tensor).item()
        
        lstm_pred = self.scaler.inverse_transform([[normalized_pred]])[0, 0]
        
        # AKF滤波
        if self.akf is not None:
            akf_pred = self.akf.predict_and_update(new_value, lstm_pred, actual_value)
        else:
            akf_pred = lstm_pred
        
        # 记录历史
        self.lstm_history.append(lstm_pred)
        self.akf_history.append(akf_pred)
        if actual_value is not None:
            self.actual_history.append(actual_value)
        
        return lstm_pred, akf_pred


def generate_simulation_data(duration_seconds=10, sample_rate=2000):
    """生成仿真数据"""
    print(f"📊 生成{duration_seconds}秒仿真数据，采样率{sample_rate}Hz...")
    
    # 时间序列
    t = np.linspace(0, duration_seconds, duration_seconds * sample_rate)
    
    # 基础信号：正弦波 + 趋势 + 噪声
    base_signal = -8.2 + 0.1 * np.sin(2 * np.pi * 0.5 * t)  # 0.5Hz正弦波
    trend = 0.01 * t  # 线性趋势
    noise = 0.005 * np.random.randn(len(t))  # 高斯噪声
    
    # 添加一些突变点模拟真实情况
    for i in range(0, len(t), len(t)//20):
        if i < len(t):
            base_signal[i:i+10] += 0.02 * np.random.randn()
    
    displacement_data = base_signal + trend + noise
    
    return t, displacement_data


def run_simulation_prediction(predictor, time_data, displacement_data):
    """运行仿真预测"""
    print("🔮 开始仿真预测...")
    
    results = []
    start_time = time.time()
    
    for i, (t, displacement) in enumerate(zip(time_data, displacement_data)):
        # 预测
        lstm_pred, akf_pred = predictor.predict_single(displacement, displacement)
        
        # 记录结果
        results.append({
            'time': t,
            'actual': displacement,
            'lstm_pred': lstm_pred,
            'akf_pred': akf_pred,
            'lstm_error': abs(lstm_pred - displacement) if len(predictor.lstm_history) > 0 else 0,
            'akf_error': abs(akf_pred - displacement) if len(predictor.akf_history) > 0 else 0
        })
        
        # 显示进度
        if i % 2000 == 0 and i > 0:
            elapsed = time.time() - start_time
            print(f"  📈 处理{i}个样本，用时{elapsed:.2f}秒")
    
    print(f"✅ 仿真预测完成，共处理{len(results)}个数据点")
    return pd.DataFrame(results)


def analyze_simulation_results(df):
    """分析仿真结果"""
    print("\n📊 仿真结果分析")
    print("=" * 50)
    
    # 过滤掉初始化阶段的数据
    valid_data = df[df.index >= 25].copy()  # 跳过前25个点（窗口大小）
    
    if len(valid_data) == 0:
        print("⚠️ 没有有效数据进行分析")
        return
    
    # LSTM性能
    lstm_mae = valid_data['lstm_error'].mean()
    lstm_rmse = np.sqrt((valid_data['lstm_error'] ** 2).mean())
    lstm_mape = (valid_data['lstm_error'] / np.abs(valid_data['actual'])).mean() * 100
    
    # AKF性能
    akf_mae = valid_data['akf_error'].mean()
    akf_rmse = np.sqrt((valid_data['akf_error'] ** 2).mean())
    akf_mape = (valid_data['akf_error'] / np.abs(valid_data['actual'])).mean() * 100
    
    print("🔍 LSTM预测性能:")
    print(f"  MAE: {lstm_mae:.6f} μm")
    print(f"  RMSE: {lstm_rmse:.6f} μm")
    print(f"  MAPE: {lstm_mape:.2f}%")
    
    print("\n🎯 LSTM+AKF优化性能:")
    print(f"  MAE: {akf_mae:.6f} μm")
    print(f"  RMSE: {akf_rmse:.6f} μm")
    print(f"  MAPE: {akf_mape:.2f}%")
    
    # 改进程度
    mae_improvement = (lstm_mae - akf_mae) / lstm_mae * 100
    rmse_improvement = (lstm_rmse - akf_rmse) / lstm_rmse * 100
    mape_improvement = (lstm_mape - akf_mape) / lstm_mape * 100
    
    print(f"\n🚀 AKF优化改进:")
    print(f"  MAE改进: {mae_improvement:.2f}%")
    print(f"  RMSE改进: {rmse_improvement:.2f}%")
    print(f"  MAPE改进: {mape_improvement:.2f}%")
    
    return {
        'lstm': {'mae': lstm_mae, 'rmse': lstm_rmse, 'mape': lstm_mape},
        'akf': {'mae': akf_mae, 'rmse': akf_rmse, 'mape': akf_mape},
        'improvement': {'mae': mae_improvement, 'rmse': rmse_improvement, 'mape': mape_improvement}
    }


def create_simulation_visualization(df, metrics):
    """创建仿真可视化"""
    print("\n📈 生成可视化图表...")
    
    # 选择一段数据进行可视化（避免图表过于密集）
    plot_data = df[1000:3000].copy()  # 显示中间2000个点
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('优化后实时预测仿真结果', fontsize=16, fontweight='bold')
    
    # 预测对比
    axes[0, 0].plot(plot_data['time'], plot_data['actual'], 'b-', label='实际值', alpha=0.8, linewidth=1)
    axes[0, 0].plot(plot_data['time'], plot_data['lstm_pred'], 'r--', label='LSTM预测', alpha=0.7, linewidth=1)
    axes[0, 0].plot(plot_data['time'], plot_data['akf_pred'], 'g:', label='LSTM+AKF预测', alpha=0.7, linewidth=1.5)
    axes[0, 0].set_xlabel('时间 (s)')
    axes[0, 0].set_ylabel('位移 (μm)')
    axes[0, 0].set_title('预测对比')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 误差对比
    axes[0, 1].plot(plot_data['time'], plot_data['lstm_error'], 'r-', label='LSTM误差', alpha=0.7)
    axes[0, 1].plot(plot_data['time'], plot_data['akf_error'], 'g-', label='LSTM+AKF误差', alpha=0.7)
    axes[0, 1].set_xlabel('时间 (s)')
    axes[0, 1].set_ylabel('绝对误差 (μm)')
    axes[0, 1].set_title('预测误差对比')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 误差分布
    valid_data = df[df.index >= 25]
    axes[1, 0].hist(valid_data['lstm_error'], bins=50, alpha=0.7, color='red', label='LSTM误差', density=True)
    axes[1, 0].hist(valid_data['akf_error'], bins=50, alpha=0.7, color='green', label='LSTM+AKF误差', density=True)
    axes[1, 0].set_xlabel('绝对误差 (μm)')
    axes[1, 0].set_ylabel('密度')
    axes[1, 0].set_title('误差分布对比')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 性能指标对比
    metrics_names = ['MAE', 'RMSE', 'MAPE']
    lstm_values = [metrics['lstm']['mae'], metrics['lstm']['rmse'], metrics['lstm']['mape']]
    akf_values = [metrics['akf']['mae'], metrics['akf']['rmse'], metrics['akf']['mape']]
    improvements = [metrics['improvement']['mae'], metrics['improvement']['rmse'], metrics['improvement']['mape']]
    
    x_pos = np.arange(len(metrics_names))
    width = 0.35
    
    bars1 = axes[1, 1].bar(x_pos - width/2, lstm_values, width, label='LSTM', color='red', alpha=0.7)
    bars2 = axes[1, 1].bar(x_pos + width/2, akf_values, width, label='LSTM+AKF', color='green', alpha=0.7)
    
    # 添加改进百分比标注
    for i, improvement in enumerate(improvements):
        axes[1, 1].text(i, max(lstm_values[i], akf_values[i]) * 1.1, f'+{improvement:.1f}%', 
                       ha='center', va='bottom', fontweight='bold', color='blue')
    
    axes[1, 1].set_xlabel('性能指标')
    axes[1, 1].set_ylabel('数值')
    axes[1, 1].set_title('性能指标对比')
    axes[1, 1].set_xticks(x_pos)
    axes[1, 1].set_xticklabels(metrics_names)
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('optimized_simulation_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 可视化图表已保存: optimized_simulation_results.png")


def main():
    """主函数"""
    print("🚀 优化后实时预测仿真演示")
    print("=" * 60)
    
    # 1. 初始化预测器
    print("步骤1: 初始化优化预测器")
    predictor = OptimizedSimulationPredictor(window_size=25)
    
    # 2. 训练优化模型
    print("\n步骤2: 训练优化模型")
    if not predictor.train_optimized_model():
        print("❌ 模型训练失败")
        return
    
    # 3. 生成仿真数据
    print("\n步骤3: 生成仿真数据")
    time_data, displacement_data = generate_simulation_data(duration_seconds=10, sample_rate=2000)
    
    # 4. 运行仿真预测
    print("\n步骤4: 运行仿真预测")
    results_df = run_simulation_prediction(predictor, time_data, displacement_data)
    
    # 5. 保存结果
    print("\n步骤5: 保存仿真结果")
    filename = f"optimized_simulation_{int(time.time())}.csv"
    results_df.to_csv(filename, index=False)
    print(f"✅ 结果已保存: {filename}")
    
    # 6. 分析结果
    print("\n步骤6: 分析仿真结果")
    metrics = analyze_simulation_results(results_df)
    
    # 7. 生成可视化
    print("\n步骤7: 生成可视化图表")
    if metrics:
        create_simulation_visualization(results_df, metrics)
    
    print("\n" + "="*60)
    print("✅ 优化后实时预测仿真演示完成！")
    print("="*60)


if __name__ == "__main__":
    main()
