"""
收集训练数据脚本
从DSA数据收集器的第1秒收集前2000个位移数据点作为训练数据
"""

import time
import os
from data_collector_3s import DSADataCollector


def collect_training_data():
    """收集训练数据"""
    print("DSA训练数据收集器")
    print("功能：收集前1000个位移数据点作为训练数据（2000Hz采样）")
    print("输出：v1.txt文件")
    print("-" * 60)
    
    # 检查DLL文件
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    if not os.path.exists(dll_path):
        print(f"错误：找不到DLL文件 {dll_path}")
        return False
    
    try:
        # 创建数据收集器
        print("1. 创建DSA数据收集器...")
        collector = DSADataCollector(dll_path)
        
        # 初始化SDK
        print("2. 初始化SDK...")
        if not collector.initialize_sdk():
            print("SDK初始化失败")
            return False
        
        # 启动数据收集
        print("3. 启动数据收集...")
        if not collector.start_collection():
            print("数据收集启动失败")
            return False
        
        print("4. 开始收集训练数据...")
        print("目标：收集前1000个位移数据点（2000Hz采样）")
        
        # 监控训练数据收集进度
        last_count = 0
        start_time = time.time()
        
        while not collector.training_data_collected:
            time.sleep(0.5)  # 每0.5秒检查一次
            
            status = collector.get_training_data_status()
            current_count = status['count']
            
            # 显示进度
            if current_count != last_count:
                elapsed_time = time.time() - start_time
                rate = current_count / elapsed_time if elapsed_time > 0 else 0
                print(f"进度: {current_count}/{status['target']} ({status['progress']:.1f}%) - 速率: {rate:.1f} 点/秒")
                last_count = current_count
            
            # 超时检查（最多等待60秒）
            if time.time() - start_time > 60:
                print("训练数据收集超时")
                break
        
        # 检查收集结果
        if collector.training_data_collected:
            print("✅ 训练数据收集完成！")
            
            # 显示统计信息
            status = collector.get_training_data_status()
            print(f"收集的数据点数: {status['count']}")
            
            if len(collector.training_data) > 0:
                data_min = min(collector.training_data)
                data_max = max(collector.training_data)
                data_mean = sum(collector.training_data) / len(collector.training_data)
                print(f"数据范围: {data_min:.6f} ~ {data_max:.6f} μm")
                print(f"平均值: {data_mean:.6f} μm")
            
            success = True
        else:
            print("❌ 训练数据收集失败")
            success = False
        
        # 停止数据收集
        print("5. 停止数据收集...")
        collector.is_running = False
        collector.sdk.stop()
        collector.sdk.unInitialize()
        
        return success
        
    except Exception as e:
        print(f"收集过程中发生错误: {e}")
        return False


def verify_training_data():
    """验证训练数据文件"""
    filename = "v1.txt"
    
    if not os.path.exists(filename):
        print(f"❌ 训练数据文件 {filename} 不存在")
        return False
    
    try:
        print(f"\n验证训练数据文件: {filename}")
        
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 检查文件格式
        if len(lines) < 2:
            print("❌ 文件内容不足")
            return False
        
        # 检查表头
        header = lines[0].strip()
        expected_header = "时间[ms]\t1-5-a1-(位移)-时域[μm]"
        if header != expected_header:
            print(f"⚠️ 表头格式可能不正确: {header}")
        
        # 检查数据行数
        data_lines = len(lines) - 1  # 减去表头
        print(f"数据行数: {data_lines}")
        
        if data_lines < 1000:
            print(f"⚠️ 数据行数不足1000行: {data_lines}")
        
        # 检查前几行数据格式
        print("前5行数据:")
        for i in range(1, min(6, len(lines))):
            line = lines[i].strip()
            parts = line.split('\t')
            if len(parts) == 2:
                try:
                    time_val = float(parts[0])
                    disp_val = float(parts[1])
                    print(f"  {i}: 时间={time_val:.3f}ms, 位移={disp_val:.6f}μm")
                except ValueError:
                    print(f"  {i}: 数据格式错误: {line}")
            else:
                print(f"  {i}: 列数错误: {line}")
        
        print("✅ 训练数据文件验证完成")
        return True
        
    except Exception as e:
        print(f"❌ 验证文件时发生错误: {e}")
        return False


def main():
    """主函数"""
    print("="*60)
    print("DSA训练数据收集器")
    print("="*60)
    
    # 收集训练数据
    success = collect_training_data()
    
    if success:
        # 验证训练数据
        verify_training_data()
        
        print("\n" + "="*60)
        print("🎉 训练数据收集成功！")
        print("📁 生成的文件:")
        print("  📄 v1.txt - 训练数据文件")
        print("\n现在可以运行以下程序:")
        print("  python integrated_real_time_demo.py  # 完整演示")
        print("="*60)
    else:
        print("\n" + "="*60)
        print("❌ 训练数据收集失败")
        print("请检查:")
        print("  1. DSA设备是否正确连接")
        print("  2. DLL文件路径是否正确")
        print("  3. 网络配置是否正确")
        print("="*60)


if __name__ == "__main__":
    main()
