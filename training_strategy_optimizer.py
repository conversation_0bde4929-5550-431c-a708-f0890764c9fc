"""
训练策略优化模块
提供高级训练技术，包括学习率调度、早停、数据增强等
"""

import torch
import torch.nn as nn
import numpy as np
from torch.optim.lr_scheduler import ReduceLROnPlateau, CosineAnnealingLR, StepLR
import warnings

warnings.filterwarnings("ignore")


class EarlyStopping:
    """早停机制"""
    
    def __init__(self, patience=10, min_delta=1e-6, restore_best_weights=True):
        """
        Args:
            patience: 容忍的epoch数
            min_delta: 最小改进量
            restore_best_weights: 是否恢复最佳权重
        """
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_loss = float('inf')
        self.counter = 0
        self.best_weights = None
        
    def __call__(self, val_loss, model):
        """
        检查是否应该早停
        
        Returns:
            bool: 是否应该停止训练
        """
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
        else:
            self.counter += 1
            
        if self.counter >= self.patience:
            if self.restore_best_weights and self.best_weights is not None:
                model.load_state_dict(self.best_weights)
            return True
        
        return False


class DataAugmentation:
    """数据增强"""
    
    def __init__(self, noise_std=0.01, scaling_range=(0.95, 1.05), 
                 time_shift_range=(-2, 2)):
        """
        Args:
            noise_std: 噪声标准差
            scaling_range: 缩放范围
            time_shift_range: 时间偏移范围
        """
        self.noise_std = noise_std
        self.scaling_range = scaling_range
        self.time_shift_range = time_shift_range
    
    def add_noise(self, data):
        """添加高斯噪声"""
        noise = torch.randn_like(data) * self.noise_std
        return data + noise
    
    def scale_data(self, data):
        """数据缩放"""
        scale_factor = np.random.uniform(*self.scaling_range)
        return data * scale_factor
    
    def time_shift(self, data):
        """时间偏移"""
        shift = np.random.randint(*self.time_shift_range)
        if shift == 0:
            return data
        
        if shift > 0:
            # 向右偏移，左边填充
            padded = torch.cat([data[:, :shift], data[:, :-shift]], dim=1)
        else:
            # 向左偏移，右边填充
            padded = torch.cat([data[:, -shift:], data[:, :shift]], dim=1)
        
        return padded
    
    def augment_batch(self, X, y, augment_prob=0.5):
        """
        批量数据增强
        
        Args:
            X: 输入数据 (batch_size, seq_len, features)
            y: 目标数据 (batch_size,)
            augment_prob: 增强概率
        
        Returns:
            tuple: 增强后的(X, y)
        """
        augmented_X = []
        augmented_y = []
        
        for i in range(X.shape[0]):
            x_sample = X[i:i+1]
            y_sample = y[i:i+1]
            
            # 原始数据
            augmented_X.append(x_sample)
            augmented_y.append(y_sample)
            
            # 随机决定是否增强
            if np.random.random() < augment_prob:
                # 应用随机增强
                aug_x = x_sample.clone()
                
                # 随机选择增强方法
                aug_methods = [self.add_noise, self.scale_data]
                method = np.random.choice(aug_methods)
                aug_x = method(aug_x)
                
                augmented_X.append(aug_x)
                augmented_y.append(y_sample)
        
        return torch.cat(augmented_X, dim=0), torch.cat(augmented_y, dim=0)


class AdvancedTrainer:
    """高级训练器"""
    
    def __init__(self, model, criterion=None, optimizer=None, scheduler=None,
                 early_stopping=None, data_augmentation=None, device='cpu'):
        """
        Args:
            model: 要训练的模型
            criterion: 损失函数
            optimizer: 优化器
            scheduler: 学习率调度器
            early_stopping: 早停机制
            data_augmentation: 数据增强
            device: 设备
        """
        self.model = model.to(device)
        self.criterion = criterion or nn.MSELoss()
        self.optimizer = optimizer or torch.optim.Adam(model.parameters(), lr=0.001)
        self.scheduler = scheduler
        self.early_stopping = early_stopping
        self.data_augmentation = data_augmentation
        self.device = device
        
        self.train_losses = []
        self.val_losses = []
        
    def train_epoch(self, train_loader, use_augmentation=True):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        num_batches = 0
        
        for X_batch, y_batch in train_loader:
            X_batch, y_batch = X_batch.to(self.device), y_batch.to(self.device)
            
            # 数据增强
            if use_augmentation and self.data_augmentation is not None:
                X_batch, y_batch = self.data_augmentation.augment_batch(X_batch, y_batch)
            
            # 前向传播
            self.optimizer.zero_grad()
            outputs = self.model(X_batch).squeeze()
            loss = self.criterion(outputs, y_batch)
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        return total_loss / num_batches
    
    def validate_epoch(self, val_loader):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0
        num_batches = 0
        
        with torch.no_grad():
            for X_batch, y_batch in val_loader:
                X_batch, y_batch = X_batch.to(self.device), y_batch.to(self.device)
                
                outputs = self.model(X_batch).squeeze()
                loss = self.criterion(outputs, y_batch)
                
                total_loss += loss.item()
                num_batches += 1
        
        return total_loss / num_batches
    
    def train(self, train_loader, val_loader=None, epochs=100, verbose=True):
        """
        完整训练过程
        
        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            epochs: 训练轮数
            verbose: 是否打印详细信息
        
        Returns:
            dict: 训练历史
        """
        best_val_loss = float('inf')
        
        for epoch in range(epochs):
            # 训练
            train_loss = self.train_epoch(train_loader)
            self.train_losses.append(train_loss)
            
            # 验证
            if val_loader is not None:
                val_loss = self.validate_epoch(val_loader)
                self.val_losses.append(val_loss)
                
                # 学习率调度
                if self.scheduler is not None:
                    if isinstance(self.scheduler, ReduceLROnPlateau):
                        self.scheduler.step(val_loss)
                    else:
                        self.scheduler.step()
                
                # 早停检查
                if self.early_stopping is not None:
                    if self.early_stopping(val_loss, self.model):
                        if verbose:
                            print(f"早停在第 {epoch + 1} 轮")
                        break
                
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                
                if verbose and (epoch + 1) % 10 == 0:
                    current_lr = self.optimizer.param_groups[0]['lr']
                    print(f'Epoch {epoch + 1:3d}: Train Loss = {train_loss:.6f}, '
                          f'Val Loss = {val_loss:.6f}, LR = {current_lr:.6f}')
            else:
                # 没有验证集时的学习率调度
                if self.scheduler is not None and not isinstance(self.scheduler, ReduceLROnPlateau):
                    self.scheduler.step()
                
                if verbose and (epoch + 1) % 10 == 0:
                    current_lr = self.optimizer.param_groups[0]['lr']
                    print(f'Epoch {epoch + 1:3d}: Train Loss = {train_loss:.6f}, '
                          f'LR = {current_lr:.6f}')
        
        return {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'best_val_loss': best_val_loss
        }


class TrainingStrategyOptimizer:
    """训练策略优化器"""
    
    def __init__(self):
        self.strategies = {
            'basic': self._get_basic_strategy,
            'advanced': self._get_advanced_strategy,
            'aggressive': self._get_aggressive_strategy,
            'conservative': self._get_conservative_strategy
        }
    
    def _get_basic_strategy(self, model):
        """基础训练策略"""
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        return {
            'optimizer': optimizer,
            'scheduler': None,
            'early_stopping': None,
            'data_augmentation': None
        }
    
    def _get_advanced_strategy(self, model):
        """高级训练策略"""
        optimizer = torch.optim.Adam(model.parameters(), lr=0.01, weight_decay=1e-5)
        scheduler = ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=10)
        early_stopping = EarlyStopping(patience=20, min_delta=1e-6)
        data_augmentation = DataAugmentation(noise_std=0.005)
        
        return {
            'optimizer': optimizer,
            'scheduler': scheduler,
            'early_stopping': early_stopping,
            'data_augmentation': data_augmentation
        }
    
    def _get_aggressive_strategy(self, model):
        """激进训练策略"""
        optimizer = torch.optim.AdamW(model.parameters(), lr=0.02, weight_decay=1e-4)
        scheduler = CosineAnnealingLR(optimizer, T_max=50, eta_min=1e-6)
        early_stopping = EarlyStopping(patience=15, min_delta=1e-5)
        data_augmentation = DataAugmentation(noise_std=0.01, scaling_range=(0.9, 1.1))
        
        return {
            'optimizer': optimizer,
            'scheduler': scheduler,
            'early_stopping': early_stopping,
            'data_augmentation': data_augmentation
        }
    
    def _get_conservative_strategy(self, model):
        """保守训练策略"""
        optimizer = torch.optim.Adam(model.parameters(), lr=0.005, weight_decay=1e-6)
        scheduler = StepLR(optimizer, step_size=30, gamma=0.8)
        early_stopping = EarlyStopping(patience=30, min_delta=1e-7)
        data_augmentation = DataAugmentation(noise_std=0.002)
        
        return {
            'optimizer': optimizer,
            'scheduler': scheduler,
            'early_stopping': early_stopping,
            'data_augmentation': data_augmentation
        }
    
    def get_strategy(self, strategy_name, model):
        """获取指定的训练策略"""
        if strategy_name not in self.strategies:
            raise ValueError(f"未知的训练策略: {strategy_name}")
        
        return self.strategies[strategy_name](model)
    
    def create_trainer(self, model, strategy_name='advanced', device='cpu'):
        """创建训练器"""
        strategy = self.get_strategy(strategy_name, model)
        
        trainer = AdvancedTrainer(
            model=model,
            optimizer=strategy['optimizer'],
            scheduler=strategy['scheduler'],
            early_stopping=strategy['early_stopping'],
            data_augmentation=strategy['data_augmentation'],
            device=device
        )
        
        return trainer


def create_data_loaders(X, y, batch_size=32, val_split=0.2, shuffle=True):
    """创建数据加载器"""
    from torch.utils.data import TensorDataset, DataLoader
    
    # 划分训练集和验证集
    if val_split > 0:
        val_size = int(len(X) * val_split)
        train_size = len(X) - val_size
        
        if shuffle:
            indices = torch.randperm(len(X))
            train_indices = indices[:train_size]
            val_indices = indices[train_size:]
        else:
            train_indices = torch.arange(train_size)
            val_indices = torch.arange(train_size, len(X))
        
        X_train, y_train = X[train_indices], y[train_indices]
        X_val, y_val = X[val_indices], y[val_indices]
        
        train_dataset = TensorDataset(X_train, y_train)
        val_dataset = TensorDataset(X_val, y_val)
        
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=shuffle)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        
        return train_loader, val_loader
    else:
        train_dataset = TensorDataset(X, y)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=shuffle)
        return train_loader, None


if __name__ == "__main__":
    # 测试训练策略优化
    print("训练策略优化模块测试")
    
    # 生成测试数据
    X = torch.randn(1000, 25, 1)
    y = torch.randn(1000)
    
    # 创建简单模型
    from model_architecture_optimizer import EnhancedLSTM
    model = EnhancedLSTM(input_size=1, hidden_size=64, num_layers=1)
    
    # 创建数据加载器
    train_loader, val_loader = create_data_loaders(X, y, batch_size=32, val_split=0.2)
    
    # 测试不同训练策略
    optimizer = TrainingStrategyOptimizer()
    
    for strategy_name in ['basic', 'advanced', 'conservative']:
        print(f"\n测试策略: {strategy_name}")
        trainer = optimizer.create_trainer(model, strategy_name)
        history = trainer.train(train_loader, val_loader, epochs=20, verbose=False)
        print(f"最终训练损失: {history['train_losses'][-1]:.6f}")
        print(f"最佳验证损失: {history['best_val_loss']:.6f}")
