"""
测试DSA实时回调预测系统的时间间隔一致性
专门验证修复后的时间戳是否具有一致的间隔
"""

import asyncio
import time
import numpy as np
import os
import matplotlib.pyplot as plt
from data_collector_3s import DSADataCollector
from real_time_stream_processor import RealTimeStreamProcessor
from real_time_train import train_displacement_model

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class TimeIntervalConsistencyTest:
    """时间间隔一致性测试"""
    
    def __init__(self, dll_path):
        self.dll_path = dll_path
        self.prediction_count = 0
        self.start_time = None
        self.predictions = []
        self.last_timestamp = None
        self.large_intervals = []  # 记录异常大的时间间隔
        
    def prediction_callback(self, real_value, prediction, timestamp, data_type):
        """预测回调函数 - 专注时间间隔分析"""
        if self.start_time is None:
            self.start_time = timestamp
            print("🚀 开始接收实时预测结果...")
            print("📊 时间间隔一致性分析:")
            print("   格式: 预测#N: 相对时间 | 时间间隔(ms) | 状态")
        
        relative_time = timestamp - self.start_time
        
        # 计算与上一个预测的时间间隔
        time_interval = 0.0
        interval_status = "首次"
        if self.last_timestamp is not None:
            time_interval = timestamp - self.last_timestamp
            
            # 判断间隔是否正常（期望约0.0001秒，即100μs）
            expected_interval = 1.0 / 10000  # 10kHz采样率的期望间隔
            if time_interval > expected_interval * 10:  # 超过期望间隔10倍
                interval_status = "异常大"
                self.large_intervals.append({
                    'prediction_num': self.prediction_count,
                    'interval': time_interval,
                    'timestamp': timestamp
                })
            elif time_interval < expected_interval * 0.1:  # 小于期望间隔0.1倍
                interval_status = "异常小"
            else:
                interval_status = "正常"
        
        error = abs(real_value - prediction)
        
        self.prediction_count += 1
        self.predictions.append({
            'prediction_num': self.prediction_count,
            'absolute_timestamp': timestamp,
            'relative_time': relative_time,
            'time_interval': time_interval,
            'interval_status': interval_status,
            'real': real_value,
            'pred': prediction,
            'error': error
        })
        
        # 显示详细的时间戳信息（前50个和异常间隔）
        if self.prediction_count <= 50 or interval_status in ["异常大", "异常小"]:
            print(f"   预测#{self.prediction_count}: {relative_time:.6f}s | {time_interval*1000:.3f}ms | {interval_status}")
        elif self.prediction_count % 100 == 0:
            print(f"   预测#{self.prediction_count}: {relative_time:.6f}s | {time_interval*1000:.3f}ms | {interval_status}")
        
        self.last_timestamp = timestamp
    
    async def run_test(self, duration=15):
        """运行时间间隔一致性测试"""
        print("="*60)
        print("🧪 DSA实时回调预测系统 - 时间间隔一致性测试")
        print("="*60)
        
        # 1. 检查训练数据
        if not os.path.exists('v1.txt'):
            print("❌ 未找到训练数据 v1.txt")
            print("请先运行数据收集程序")
            return False
        
        # 2. 快速训练模型
        print("🚀 快速训练模型...")
        model, scaler = train_displacement_model(
            data_file='v1.txt',
            window_size=25,
            hidden_size=64,
            learning_rate=0.01,
            epochs=30,
            train_points=500
        )
        
        if model is None or scaler is None:
            print("❌ 模型训练失败")
            return False
        
        print("✅ 模型训练完成")
        
        # 3. 创建预测器
        print("🔧 创建预测器...")
        predictor = RealTimeStreamProcessor(
            model=model,
            scaler=scaler,
            window_size=25,
            enable_akf=True
        )
        
        # 4. 创建数据收集器
        print("🔧 创建数据收集器...")
        collector = DSADataCollector(self.dll_path)
        collector.enable_continuous_mode()
        collector.enable_real_time_streaming()
        
        # 5. 收集初始化数据
        print("📊 收集初始化数据...")
        if not collector.initialize_sdk():
            print("❌ SDK初始化失败")
            return False
        
        if not collector.start_collection():
            print("❌ 数据收集启动失败")
            return False
        
        # 收集25个初始化数据点
        historical_data = []
        timeout_count = 0
        
        while len(historical_data) < 25 and timeout_count < 50:
            data = collector.get_real_time_data_by_type('displacement', timeout=0.1)
            if data is not None:
                historical_data.append(data['value'])
                if len(historical_data) % 5 == 0:
                    print(f"收集初始化数据: {len(historical_data)}/25")
            else:
                timeout_count += 1
                await asyncio.sleep(0.1)
        
        if len(historical_data) < 25:
            print(f"❌ 初始化数据不足: {len(historical_data)}/25")
            collector.stop_collection()
            return False
        
        # 初始化预测器
        predictor.initialize_with_historical_data(historical_data)
        print("✅ 预测器初始化完成")
        
        # 6. 设置实时预测
        collector.set_real_time_predictor(predictor)
        collector.set_prediction_callback(self.prediction_callback)
        
        print(f"🚀 开始{duration}秒时间间隔一致性测试...")
        print("专门分析DSA回调的时间间隔一致性")
        
        # 7. 等待测试完成
        try:
            await asyncio.sleep(duration)
        except KeyboardInterrupt:
            print("⚠️ 用户中断测试")
        
        # 8. 停止收集
        collector.stop_collection()
        
        # 9. 分析时间间隔一致性
        self.analyze_interval_consistency()
        
        # 10. 生成可视化图表
        self.create_interval_visualization()
        
        return True
    
    def analyze_interval_consistency(self):
        """分析时间间隔一致性"""
        print("\n" + "="*60)
        print("📊 时间间隔一致性分析结果")
        print("="*60)
        
        if len(self.predictions) < 2:
            print("❌ 预测数据不足，无法分析时间间隔")
            return
        
        # 提取时间间隔数据（跳过第一个）
        intervals = [p['time_interval'] for p in self.predictions[1:]]
        relative_times = [p['relative_time'] for p in self.predictions]
        
        # 基本统计
        total_time = max(relative_times) - min(relative_times)
        prediction_rate = len(self.predictions) / total_time if total_time > 0 else 0
        
        avg_interval = np.mean(intervals)
        std_interval = np.std(intervals)
        min_interval = np.min(intervals)
        max_interval = np.max(intervals)
        median_interval = np.median(intervals)
        
        print(f"⏱️  测试时长: {total_time:.6f} 秒")
        print(f"📈 预测次数: {len(self.predictions)}")
        print(f"🎯 预测频率: {prediction_rate:.2f} Hz")
        print(f"📊 平均时间间隔: {avg_interval:.6f} 秒 ({avg_interval*1000:.3f} ms)")
        print(f"📊 中位数间隔: {median_interval:.6f} 秒 ({median_interval*1000:.3f} ms)")
        print(f"📊 间隔标准差: {std_interval:.6f} 秒 ({std_interval*1000:.3f} ms)")
        print(f"📊 最小间隔: {min_interval:.6f} 秒 ({min_interval*1000:.3f} ms)")
        print(f"📊 最大间隔: {max_interval:.6f} 秒 ({max_interval*1000:.3f} ms)")
        
        # 期望间隔分析
        expected_interval = 1.0 / 10000  # 10kHz
        interval_deviation = abs(avg_interval - expected_interval)
        
        print(f"\n🔍 与期望间隔对比:")
        print(f"   期望间隔: {expected_interval:.6f} 秒 ({expected_interval*1000:.3f} ms)")
        print(f"   实际平均: {avg_interval:.6f} 秒 ({avg_interval*1000:.3f} ms)")
        print(f"   绝对偏差: {interval_deviation:.6f} 秒 ({interval_deviation*1000:.3f} ms)")
        print(f"   相对偏差: {interval_deviation/expected_interval*100:.2f}%")
        
        # 一致性评估
        cv = std_interval / avg_interval if avg_interval > 0 else float('inf')  # 变异系数
        print(f"   变异系数: {cv:.4f} ({cv*100:.2f}%)")
        
        if cv < 0.1:
            consistency_level = "优秀"
        elif cv < 0.2:
            consistency_level = "良好"
        elif cv < 0.5:
            consistency_level = "一般"
        else:
            consistency_level = "较差"
        
        print(f"   一致性评级: {consistency_level}")
        
        # 异常间隔分析
        print(f"\n⚠️ 异常间隔分析:")
        print(f"   异常大间隔数量: {len(self.large_intervals)}")
        
        if len(self.large_intervals) > 0:
            print(f"   异常间隔占比: {len(self.large_intervals)/len(intervals)*100:.2f}%")
            print(f"   异常间隔详情:")
            for i, large_int in enumerate(self.large_intervals[:10]):  # 显示前10个
                print(f"     {i+1}. 预测#{large_int['prediction_num']}: {large_int['interval']*1000:.3f}ms")
        else:
            print(f"   ✅ 未发现异常大间隔")
        
        # 间隔分布分析
        normal_intervals = [i for i in intervals if expected_interval*0.5 <= i <= expected_interval*2]
        normal_ratio = len(normal_intervals) / len(intervals) * 100
        
        print(f"\n📈 间隔分布分析:")
        print(f"   正常间隔数量: {len(normal_intervals)}/{len(intervals)}")
        print(f"   正常间隔占比: {normal_ratio:.2f}%")
        
        if normal_ratio >= 95:
            print(f"   ✅ 间隔分布优秀")
        elif normal_ratio >= 90:
            print(f"   ✅ 间隔分布良好")
        elif normal_ratio >= 80:
            print(f"   ⚠️ 间隔分布一般")
        else:
            print(f"   ❌ 间隔分布较差")
    
    def create_interval_visualization(self):
        """创建时间间隔可视化图表"""
        if len(self.predictions) < 10:
            print("⚠️ 数据点太少，跳过可视化")
            return
        
        intervals = [p['time_interval'] for p in self.predictions[1:]]
        prediction_nums = [p['prediction_num'] for p in self.predictions[1:]]
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('DSA实时预测时间间隔一致性分析', fontsize=16, fontweight='bold')
        
        # 1. 时间间隔随预测次数变化
        axes[0, 0].plot(prediction_nums, [i*1000 for i in intervals], 'b-', alpha=0.7, linewidth=1)
        axes[0, 0].set_xlabel('预测次数')
        axes[0, 0].set_ylabel('时间间隔 (ms)')
        axes[0, 0].set_title('时间间隔随预测次数变化')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 添加期望间隔线
        expected_interval_ms = 1000 / 10000  # 0.1ms
        axes[0, 0].axhline(y=expected_interval_ms, color='red', linestyle='--', 
                          label=f'期望间隔: {expected_interval_ms:.1f}ms')
        axes[0, 0].legend()
        
        # 2. 时间间隔分布直方图
        axes[0, 1].hist([i*1000 for i in intervals], bins=50, alpha=0.7, color='green', edgecolor='black')
        axes[0, 1].axvline(x=expected_interval_ms, color='red', linestyle='--', linewidth=2,
                          label=f'期望间隔: {expected_interval_ms:.1f}ms')
        axes[0, 1].axvline(x=np.mean(intervals)*1000, color='orange', linestyle='--', linewidth=2,
                          label=f'平均间隔: {np.mean(intervals)*1000:.3f}ms')
        axes[0, 1].set_xlabel('时间间隔 (ms)')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].set_title('时间间隔分布直方图')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 累积时间偏差
        expected_times = [i * (1.0/10000) for i in range(len(intervals))]
        actual_times = np.cumsum(intervals)
        time_drift = [(actual_times[i] - expected_times[i])*1000 for i in range(len(intervals))]
        
        axes[1, 0].plot(prediction_nums, time_drift, 'purple', linewidth=2)
        axes[1, 0].set_xlabel('预测次数')
        axes[1, 0].set_ylabel('累积时间偏差 (ms)')
        axes[1, 0].set_title('累积时间偏差')
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # 4. 间隔变异性分析
        window_size = 100
        if len(intervals) > window_size:
            rolling_std = []
            rolling_mean = []
            for i in range(window_size, len(intervals)):
                window_intervals = intervals[i-window_size:i]
                rolling_std.append(np.std(window_intervals)*1000)
                rolling_mean.append(np.mean(window_intervals)*1000)
            
            axes[1, 1].plot(range(window_size, len(intervals)), rolling_std, 'red', 
                           linewidth=2, label='滚动标准差')
            axes[1, 1].set_xlabel('预测次数')
            axes[1, 1].set_ylabel('时间间隔标准差 (ms)')
            axes[1, 1].set_title(f'时间间隔变异性 (窗口大小: {window_size})')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
        else:
            axes[1, 1].text(0.5, 0.5, '数据不足\n无法计算滚动统计', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('时间间隔变异性分析')
        
        plt.tight_layout()

        # 图表生成已禁用
        plt.close()
        print("📊 时间间隔一致性分析图表生成已禁用，避免生成额外文件")

async def main():
    """主函数"""
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    # 检查DLL文件
    if not os.path.exists(dll_path):
        print(f"❌ 找不到DLL文件: {dll_path}")
        return
    
    # 创建测试实例
    test = TimeIntervalConsistencyTest(dll_path)
    
    # 运行测试
    success = await test.run_test(duration=15)  # 15秒测试
    
    if success:
        print("✅ 时间间隔一致性测试完成")
    else:
        print("❌ 时间间隔一致性测试失败")

if __name__ == "__main__":
    print("🧪 DSA实时回调预测系统 - 时间间隔一致性测试程序")
    print("专门验证修复后的时间戳间隔是否一致")
    asyncio.run(main())
